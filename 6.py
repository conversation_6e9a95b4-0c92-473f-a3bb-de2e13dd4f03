import os
import pandas as pd

# مسار المجلد الرئيسي الذي يحتوي على مجلدات الثغرات
main_folder_path = r'D:\AI\web hacking\github_files1'  # قم بتغيير هذا إلى المسار الصحيح

# قائمة الصيغ النصية المدعومة (في هذه الحالة، نريد فقط ملفات .sol)
text_extensions = ['.txt' ,'.md' , '.xml' , '.INSERT' , '.WHERE' ,'.sql' ,'.fuzz']

# قائمة لتخزين البيانات
data = []

# استعراض جميع المجلدات في المجلد الرئيسي
for root, dirs, files in os.walk(main_folder_path):
    for dir_name in dirs:
        # مسار المجلد الحالي (مجلد الثغرة)
        vulnerability_folder_path = os.path.join(root, dir_name)
        
        # استعراض جميع الملفات في مجلد الثغرة
        for file_name in os.listdir(vulnerability_folder_path):
            # الحصول على المسار الكامل للملف
            file_path = os.path.join(vulnerability_folder_path, file_name)
            
            # استخراج اسم الثغرة من اسم المجلد
            vulnerability_name = dir_name
            
            # تحديد نوع الملف (بناءً على الامتداد)
            file_extension = os.path.splitext(file_name)[1].lower()
            
            # إذا كان الملف نصيًا (بناءً على القائمة)، نقرأه
            if file_extension in text_extensions:
                try:
                    with open(file_path, 'r', encoding='utf-8') as file:
                        # قراءة محتويات الملف بالكامل
                        content = file.read()
                        if content.strip():  # تجاهل الملفات الفارغة
                            # إضافة البيانات إلى القائمة
                            data.append({
                                'vulnerability': vulnerability_name,  # اسم الثغرة فقط
                                'code': content.strip(),  # محتوى الملف الكامل
                                'file_type': file_extension,  # نوع الملف
                                'file_path': file_path  # مسار الملف
                            })
                except Exception as e:
                    print(f"خطأ في قراءة الملف: {file_path} - {str(e)}")
            else:
                # تجاهل الملفات غير النصية
                print(f"تم تجاهل الملف: {file_path} (امتداد غير مدعوم)")

# تحويل البيانات إلى DataFrame
df = pd.DataFrame(data)

# حفظ البيانات في ملف CSV
output_csv = 'github_payloads2.csv'
df.to_csv(output_csv, index=False, encoding='utf-8')

print(f"تم حفظ البيانات في ملف {output_csv} بنجاح!")