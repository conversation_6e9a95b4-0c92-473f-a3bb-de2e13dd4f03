import pandas as pd
import joblib
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.model_selection import train_test_split
from sklearn.naive_bayes import MultinomialNB
from sklearn.pipeline import make_pipeline

# تحميل البيانات من ملف CSV
csv_file = "final_dataset00.csv"  # تأكد من تغيير الاسم حسب ملفك
df = pd.read_csv(csv_file)

# التحقق من الأعمدة المطلوبة
if not {'vulnerability', 'code'}.issubset(df.columns):
    raise ValueError("يجب أن يحتوي ملف CSV على الأعمدة: 'vulnerability' و 'code'")

# تقسيم البيانات إلى المدخلات (X) والتصنيفات (y)
X = df["code"]  # الكود الخاص بالثغرة
y = df["vulnerability"]  # اسم الثغرة

# تقسيم البيانات إلى تدريب واختبار
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# إنشاء وتحميل الـ Vectorizer
vectorizer = TfidfVectorizer()
X_train_tfidf = vectorizer.fit_transform(X_train)
X_test_tfidf = vectorizer.transform(X_test)

# تدريب نموذج Naïve Bayes
model = MultinomialNB()
model.fit(X_train_tfidf, y_train)

# حفظ النموذج والـ Vectorizer في ملفات pkl
joblib.dump(model, "final_dataset00.pkl")
joblib.dump(vectorizer, "final_dataset00_Vectorizer1.pkl")

print("✅ تم حفظ الملفات بنجاح:")
print("   - exploit-smart_contracts_data.pkl")
print("   - Vectorizer6.pkl")

# اختبار النموذج على عينة جديدة
sample_code = ["strcpy(buffer, input); // Possible buffer overflow"]
sample_tfidf = vectorizer.transform(sample_code)
predicted_vuln = model.predict(sample_tfidf)
print(f"🔍 تصنيف العينة: {predicted_vuln[0]}")
