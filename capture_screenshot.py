"""
Screenshot Capture Module for Vulnerability Scanner

This module provides functions to capture screenshots of web pages for vulnerability evidence.
"""

import os
import time
import logging
from datetime import datetime
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
from webdriver_manager.chrome import ChromeDriverManager
from PIL import Image, ImageEnhance
from colorama import Fore, Style, init

# Initialize colorama
init(autoreset=True)

# Configure logging
logger = logging.getLogger(__name__)

def capture_screenshot_sync(url, output_dir, filename, include_vulnerability_info=None):
    """
    Capture a screenshot of a URL using Selenium with enhanced evidence collection.

    This improved version:
    1. Captures full-page screenshots
    2. Optionally highlights vulnerability elements
    3. Adds timestamp and URL information to the screenshot
    4. Supports capturing specific elements (forms, inputs, etc.)
    5. Handles timeouts and errors gracefully
    6. Implements retry mechanism for better reliability
    7. Supports parameter highlighting for better vulnerability visualization

    Args:
        url (str): The URL to capture
        output_dir (str): Directory to save the screenshot
        filename (str): Filename for the screenshot
        include_vulnerability_info (dict, optional): Vulnerability information to include in the screenshot
                                                    Expected keys:
                                                    - type: Vulnerability type (e.g., 'XSS', 'SQLi')
                                                    - element_type: Type of element ('form', 'parameter', etc.)
                                                    - element_name: Name of the element to highlight
                                                    - payload: The payload used (if applicable)

    Returns:
        str: Path to the saved screenshot or None if failed
    """
    driver = None
    max_retries = 3
    retry_count = 0

    # Ensure the URL is properly formatted
    if not url.startswith(('http://', 'https://')):
        url = 'http://' + url

    # Create output directory if it doesn't exist
    try:
        os.makedirs(output_dir, exist_ok=True)
    except Exception as dir_error:
        logger.error(f"Error creating output directory: {str(dir_error)}")
        print(Fore.RED + f"[-] Error creating output directory: {str(dir_error)}")
        # Try to create a fallback directory
        output_dir = os.path.join(os.getcwd(), "screenshots")
        os.makedirs(output_dir, exist_ok=True)

    # Retry loop for better reliability
    while retry_count < max_retries:
        try:
            print(Fore.CYAN + f"[*] Capturing screenshot of {url} (Attempt {retry_count + 1}/{max_retries})")

            # Configure Chrome options with improved settings
            chrome_options = Options()
            chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            chrome_options.add_argument("--disable-gpu")
            chrome_options.add_argument("--window-size=1920,1080")
            chrome_options.add_argument("--disable-extensions")
            chrome_options.add_argument("--disable-popup-blocking")
            chrome_options.add_argument("--ignore-certificate-errors")
            chrome_options.add_argument("--disable-web-security")
            chrome_options.add_argument("--disable-notifications")
            chrome_options.add_argument("--disable-logging")
            chrome_options.add_argument("--log-level=3")
            chrome_options.add_argument("--silent")

            # Add user agent to avoid detection
            chrome_options.add_argument("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")

            # Initialize Chrome driver with error handling
            try:
                service = Service(ChromeDriverManager().install())
                driver = webdriver.Chrome(service=service, options=chrome_options)
            except Exception as driver_error:
                logger.error(f"Error initializing Chrome driver: {str(driver_error)}")
                print(Fore.RED + f"[-] Error initializing Chrome driver: {str(driver_error)}")
                # Try alternative initialization
                try:
                    driver = webdriver.Chrome(options=chrome_options)
                except Exception as alt_driver_error:
                    logger.error(f"Alternative driver initialization failed: {str(alt_driver_error)}")
                    print(Fore.RED + f"[-] Alternative driver initialization failed: {str(alt_driver_error)}")
                    retry_count += 1
                    time.sleep(2)  # Wait before retrying
                    continue

            # Set page load timeout with a reasonable value
            driver.set_page_load_timeout(30)

            # Navigate to URL with proper error handling
            try:
                print(Fore.CYAN + f"[*] Navigating to {url}")
                driver.get(url)
            except TimeoutException:
                print(Fore.YELLOW + f"[!] Timeout while loading {url}, but will try to capture anyway")
                # Continue anyway, we might still get a partial screenshot
            except Exception as nav_error:
                logger.error(f"Navigation error: {str(nav_error)}")
                print(Fore.RED + f"[-] Navigation error: {str(nav_error)}")
                if driver:
                    driver.quit()
                retry_count += 1
                time.sleep(2)  # Wait before retrying
                continue

            # Wait for page to load with a more reliable condition
            try:
                WebDriverWait(driver, 10).until(
                    lambda d: d.execute_script("return document.readyState") == "complete"
                )
                print(Fore.GREEN + f"[+] Page loaded successfully")
            except Exception as wait_error:
                print(Fore.YELLOW + f"[!] Page load wait timed out: {str(wait_error)}")
                # Continue anyway, we might still get a useful screenshot

            # If vulnerability info is provided, highlight the vulnerable element
            if include_vulnerability_info:
                try:
                    # Get vulnerability details with fallbacks for different key names
                    vuln_type = include_vulnerability_info.get('type',
                                include_vulnerability_info.get('prediction',
                                include_vulnerability_info.get('vuln_type', 'Unknown')))

                    element_type = include_vulnerability_info.get('element_type',
                                  include_vulnerability_info.get('type', ''))

                    element_name = include_vulnerability_info.get('element_name',
                                  include_vulnerability_info.get('parameter_name',
                                  include_vulnerability_info.get('input_name', '')))

                    payload = include_vulnerability_info.get('payload', '')

                    print(Fore.CYAN + f"[*] Highlighting vulnerability: {vuln_type} in {element_type} {element_name}")

                    # Determine what to highlight based on element type
                    if element_type == 'form':
                        # Try to find the form
                        form_id = include_vulnerability_info.get('form_id', '')

                        if form_id:
                            # Try to find form by ID
                            try:
                                form_element = driver.find_element(By.ID, form_id)
                                driver.execute_script(
                                    "arguments[0].style.border='3px solid red';" +
                                    "arguments[0].style.boxShadow='0 0 10px rgba(255,0,0,0.5)';",
                                    form_element
                                )
                            except Exception as form_error:
                                print(Fore.YELLOW + f"[!] Could not highlight form by ID: {str(form_error)}")

                        # Try to find form by containing an input with the given name
                        if element_name:
                            try:
                                # Find input by name
                                input_elements = driver.find_elements(By.NAME, element_name)
                                if input_elements:
                                    for input_element in input_elements:
                                        # Highlight the input
                                        driver.execute_script(
                                            "arguments[0].style.border='3px solid red';" +
                                            "arguments[0].style.backgroundColor='rgba(255,0,0,0.1)';" +
                                            "arguments[0].style.boxShadow='0 0 5px red';",
                                            input_element
                                        )

                                        # Try to highlight parent form
                                        driver.execute_script("""
                                            var element = arguments[0];
                                            while(element.parentNode && element.tagName.toLowerCase() !== 'form') {
                                                element = element.parentNode;
                                            }
                                            if(element.tagName.toLowerCase() === 'form') {
                                                element.style.border='2px solid orange';
                                                element.style.boxShadow='0 0 10px rgba(255,165,0,0.5)';
                                            }
                                        """, input_element)
                            except Exception as input_error:
                                print(Fore.YELLOW + f"[!] Could not highlight input: {str(input_error)}")

                    elif element_type == 'parameter' or element_name:
                        # Try different strategies to find and highlight the element
                        highlighted = False

                        # Strategy 1: Try by name attribute
                        if element_name and not highlighted:
                            try:
                                elements = driver.find_elements(By.NAME, element_name)
                                if elements:
                                    for element in elements:
                                        driver.execute_script(
                                            "arguments[0].style.border='3px solid red';" +
                                            "arguments[0].style.backgroundColor='rgba(255,0,0,0.1)';" +
                                            "arguments[0].style.boxShadow='0 0 5px red';",
                                            element
                                        )
                                    highlighted = True
                                    print(Fore.GREEN + f"[+] Highlighted element by name: {element_name}")
                            except Exception as name_error:
                                print(Fore.YELLOW + f"[!] Could not highlight by name: {str(name_error)}")

                        # Strategy 2: Try by ID attribute
                        if element_name and not highlighted:
                            try:
                                element = driver.find_element(By.ID, element_name)
                                driver.execute_script(
                                    "arguments[0].style.border='3px solid red';" +
                                    "arguments[0].style.backgroundColor='rgba(255,0,0,0.1)';" +
                                    "arguments[0].style.boxShadow='0 0 5px red';",
                                    element
                                )
                                highlighted = True
                                print(Fore.GREEN + f"[+] Highlighted element by ID: {element_name}")
                            except:
                                pass

                        # Strategy 3: Try by XPath for input elements
                        if element_name and not highlighted:
                            try:
                                xpath = f"//input[@name='{element_name}' or @id='{element_name}']"
                                elements = driver.find_elements(By.XPATH, xpath)
                                if elements:
                                    for element in elements:
                                        driver.execute_script(
                                            "arguments[0].style.border='3px solid red';" +
                                            "arguments[0].style.backgroundColor='rgba(255,0,0,0.1)';" +
                                            "arguments[0].style.boxShadow='0 0 5px red';",
                                            element
                                        )
                                    highlighted = True
                                    print(Fore.GREEN + f"[+] Highlighted element by XPath: {xpath}")
                            except Exception as xpath_error:
                                print(Fore.YELLOW + f"[!] Could not highlight by XPath: {str(xpath_error)}")

                        # Strategy 4: Highlight URL parameter in the address bar
                        if element_type == 'parameter' or not highlighted:
                            try:
                                # Highlight the URL bar using JavaScript
                                driver.execute_script("""
                                    var urlHighlight = document.createElement('div');
                                    urlHighlight.style.position = 'fixed';
                                    urlHighlight.style.top = '0';
                                    urlHighlight.style.left = '0';
                                    urlHighlight.style.width = '100%';
                                    urlHighlight.style.padding = '5px';
                                    urlHighlight.style.backgroundColor = 'rgba(255, 0, 0, 0.7)';
                                    urlHighlight.style.color = 'white';
                                    urlHighlight.style.fontWeight = 'bold';
                                    urlHighlight.style.zIndex = '9999';
                                    urlHighlight.textContent = 'Vulnerable Parameter: """ + element_name + """';
                                    document.body.appendChild(urlHighlight);
                                """)
                                print(Fore.GREEN + f"[+] Added URL parameter highlight for: {element_name}")
                            except Exception as url_error:
                                print(Fore.YELLOW + f"[!] Could not add URL highlight: {str(url_error)}")

                    # Add vulnerability information overlay with more details
                    try:
                        # Format payload for display
                        payload_display = payload
                        if payload and len(payload) > 30:
                            payload_display = payload[:27] + "..."

                        driver.execute_script("""
                            var infoBox = document.createElement('div');
                            infoBox.style.position = 'fixed';
                            infoBox.style.bottom = '0';
                            infoBox.style.right = '0';
                            infoBox.style.width = '400px';
                            infoBox.style.padding = '10px';
                            infoBox.style.backgroundColor = 'rgba(0, 0, 0, 0.8)';
                            infoBox.style.color = 'white';
                            infoBox.style.fontFamily = 'Arial, sans-serif';
                            infoBox.style.fontSize = '12px';
                            infoBox.style.zIndex = '9999';
                            infoBox.style.borderRadius = '5px 0 0 0';
                            infoBox.innerHTML = '<div style="font-weight:bold;font-size:14px;margin-bottom:5px;">Vulnerability Detected</div>' +
                                               '<div>Type: """ + vuln_type + """</div>' +
                                               '<div>Element: """ + element_type + (f" ({element_name})" if element_name else "") + """</div>' +
                                               '<div>Time: """ + datetime.now().strftime('%Y-%m-%d %H:%M:%S') + """</div>' +
                                               '<div>URL: """ + url[:50] + ("..." if len(url) > 50 else "") + """</div>' +
                                               """ + (f"'<div>Payload: {payload_display}</div>'" if payload else "") + """;
                            document.body.appendChild(infoBox);
                        """)
                        print(Fore.GREEN + f"[+] Added vulnerability information overlay")
                    except Exception as overlay_error:
                        print(Fore.YELLOW + f"[!] Could not add information overlay: {str(overlay_error)}")

                    # Wait a moment for the highlights to be applied
                    time.sleep(1)
                except Exception as highlight_error:
                    logger.error(f"Error highlighting vulnerability: {str(highlight_error)}")
                    print(Fore.YELLOW + f"[!] Error highlighting vulnerability: {str(highlight_error)}")

            # Take full page screenshot with error handling
            try:
                # First get the total height of the page
                total_height = driver.execute_script("return Math.max(document.body.scrollHeight, document.documentElement.scrollHeight, document.body.offsetHeight, document.documentElement.offsetHeight, document.body.clientHeight, document.documentElement.clientHeight);")

                # Set window size to capture everything
                driver.set_window_size(1920, total_height)

                # Save screenshot
                screenshot_path = os.path.join(output_dir, filename)
                driver.save_screenshot(screenshot_path)
                print(Fore.GREEN + f"[+] Raw screenshot saved to: {screenshot_path}")

                # Enhance the screenshot quality
                try:
                    img = Image.open(screenshot_path)

                    # Enhance contrast
                    enhancer = ImageEnhance.Contrast(img)
                    img = enhancer.enhance(1.2)

                    # Enhance sharpness
                    enhancer = ImageEnhance.Sharpness(img)
                    img = enhancer.enhance(1.5)

                    # Save enhanced image
                    img.save(screenshot_path, quality=95)
                    print(Fore.GREEN + f"[+] Enhanced screenshot quality")
                except Exception as img_error:
                    logger.error(f"Error enhancing screenshot: {str(img_error)}")
                    print(Fore.YELLOW + f"[!] Error enhancing screenshot: {str(img_error)}")
                    # Continue anyway, we still have the raw screenshot
            except Exception as screenshot_error:
                logger.error(f"Error taking screenshot: {str(screenshot_error)}")
                print(Fore.RED + f"[-] Error taking screenshot: {str(screenshot_error)}")
                if driver:
                    driver.quit()
                retry_count += 1
                time.sleep(2)  # Wait before retrying
                continue

            # Close driver
            try:
                driver.quit()
                print(Fore.GREEN + f"[+] Browser closed successfully")
            except Exception as quit_error:
                logger.error(f"Error closing browser: {str(quit_error)}")
                print(Fore.YELLOW + f"[!] Error closing browser: {str(quit_error)}")

            print(Fore.GREEN + f"[+] Screenshot captured successfully: {screenshot_path}")
            return screenshot_path

        except Exception as e:
            logger.error(f"Error in screenshot capture: {str(e)}")
            print(Fore.RED + f"[-] Error in screenshot capture: {str(e)}")

            # Clean up driver if it exists
            try:
                if driver:
                    driver.quit()
            except:
                pass

            # Increment retry counter
            retry_count += 1

            # Wait before retrying
            if retry_count < max_retries:
                wait_time = retry_count * 2  # Exponential backoff
                print(Fore.YELLOW + f"[!] Retrying in {wait_time} seconds...")
                time.sleep(wait_time)

    # If we've exhausted all retries, return None
    print(Fore.RED + f"[-] Failed to capture screenshot after {max_retries} attempts")
    return None
