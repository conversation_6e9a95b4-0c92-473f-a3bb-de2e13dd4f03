def crawl_website(url, max_depth=3, max_links=100, max_per_domain=50):
    """
    زحف الروابط بدءًا من URL باستخدام Selenium حصريًا.
    تم تعديل هذه الدالة لتستخدم crawl_with_selenium_only حصريًا.

    Args:
        url (str): عنوان URL الأولي لبدء الزحف
        max_depth (int): الحد الأقصى لعمق الزحف (غير مستخدم حاليًا)
        max_links (int): الحد الأقصى لعدد عناوين URL المراد استخراجها
        max_per_domain (int): الحد الأقصى لعدد عناوين URL لكل نطاق (غير مستخدم حاليًا)

    Returns:
        set: مجموعة الروابط المكتشفة
    """
    import os
    import sys
    from colorama import Fore

    # Add parent directory to path to import from 66.py
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.append(current_dir)

    # Import the optimized Selenium crawler
    from importlib.util import spec_from_file_location, module_from_spec

    # Import crawl_with_selenium_only from 66.py
    spec = spec_from_file_location("main_66", os.path.join(current_dir, "66.py"))
    main_66 = module_from_spec(spec)
    spec.loader.exec_module(main_66)
    crawl_with_selenium_only = main_66.crawl_with_selenium_only

    print(Fore.CYAN + f"[*] بدء الزحف من: {url}")
    print(Fore.YELLOW + f"[!] تنبيه: تم توجيه الطلب إلى crawl_with_selenium_only للزحف")

    # Use crawl_with_selenium_only exclusively
    links = crawl_with_selenium_only(
        url=url,
        max_links=max_links,
        max_depth=max_depth,
        max_per_domain=max_per_domain
    )

    # Convert list to set for consistency with original return type
    return set(links)
