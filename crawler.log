2025-05-20 01:08:46,377 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 01:08:46,572 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 01:09:07,908 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\tf_keras\src\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.

2025-05-20 01:09:08,722 - datasets - INFO - PyTorch version 2.6.0 available.
2025-05-20 01:09:08,724 - datasets - INFO - TensorFlow version 2.19.0 available.
2025-05-20 01:09:08,734 - datasets - INFO - JAX version 0.4.17 available.
2025-05-20 01:09:13,367 - scapy.runtime - WARNING - No libpcap provider available ! pcap won't be used
2025-05-20 01:09:13,700 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:512: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 01:09:13,700 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:516: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 01:09:27,058 - __main__ - INFO - Starting crawling with Selenium: https://www.flipkart.com
2025-05-20 01:09:27,059 - WDM - INFO - ====== WebDriver manager ======
2025-05-20 01:09:29,076 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-05-20 01:09:30,663 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-05-20 01:09:32,146 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-05-20 01:09:36,502 - WDM - INFO - WebDriver version 136.0.7103.94 selected
2025-05-20 01:09:36,505 - WDM - INFO - Modern chrome version https://storage.googleapis.com/chrome-for-testing-public/136.0.7103.94/win32/chromedriver-win32.zip
2025-05-20 01:09:36,505 - WDM - INFO - About to download new driver from https://storage.googleapis.com/chrome-for-testing-public/136.0.7103.94/win32/chromedriver-win32.zip
2025-05-20 01:09:37,390 - WDM - INFO - Driver downloading response is 200
2025-05-20 01:12:17,458 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-05-20 01:12:18,738 - WDM - INFO - Driver has been saved in cache [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.94]
2025-05-20 01:57:11,079 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 01:57:11,344 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 02:03:05,021 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 02:03:05,221 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 02:03:21,254 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\tf_keras\src\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.

2025-05-20 02:03:21,918 - datasets - INFO - PyTorch version 2.6.0 available.
2025-05-20 02:03:21,919 - datasets - INFO - TensorFlow version 2.19.0 available.
2025-05-20 02:03:21,941 - datasets - INFO - JAX version 0.4.17 available.
2025-05-20 02:03:27,505 - scapy.runtime - WARNING - No libpcap provider available ! pcap won't be used
2025-05-20 02:03:28,008 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:512: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 02:03:28,008 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:516: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 02:03:38,274 - __main__ - INFO - Starting crawling with Selenium: https://www.flipkart.com
2025-05-20 02:03:38,275 - WDM - INFO - ====== WebDriver manager ======
2025-05-20 02:03:40,191 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-05-20 02:03:40,440 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-05-20 02:03:40,769 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.94\chromedriver-win32/chromedriver.exe] found in cache
2025-05-20 02:06:41,393 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 02:06:41,685 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 02:07:04,761 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\tf_keras\src\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.

2025-05-20 02:07:05,632 - datasets - INFO - PyTorch version 2.6.0 available.
2025-05-20 02:07:05,632 - datasets - INFO - TensorFlow version 2.19.0 available.
2025-05-20 02:07:05,643 - datasets - INFO - JAX version 0.4.17 available.
2025-05-20 02:07:10,179 - scapy.runtime - WARNING - No libpcap provider available ! pcap won't be used
2025-05-20 02:07:10,523 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:512: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 02:07:10,523 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:516: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 02:08:44,567 - tensorflow - WARNING - From c:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 02:08:44,841 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 02:09:17,618 - tensorflow - WARNING - From c:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\tf_keras\src\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.

2025-05-20 02:09:18,731 - datasets - INFO - PyTorch version 2.6.0 available.
2025-05-20 02:09:18,738 - datasets - INFO - TensorFlow version 2.19.0 available.
2025-05-20 02:09:18,744 - datasets - INFO - JAX version 0.4.17 available.
2025-05-20 02:09:28,216 - scapy.runtime - WARNING - No libpcap provider available ! pcap won't be used
2025-05-20 02:09:28,989 - py.warnings - WARNING - c:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:512: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 02:09:28,989 - py.warnings - WARNING - c:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:516: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 02:09:38,871 - __main__ - INFO - Starting crawling with Selenium: https://www.flipkart.com
2025-05-20 02:09:38,873 - WDM - INFO - ====== WebDriver manager ======
2025-05-20 02:09:41,332 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-05-20 02:09:41,704 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-05-20 02:09:41,919 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.94\chromedriver-win32/chromedriver.exe] found in cache
2025-05-20 02:23:40,221 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 02:23:40,733 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 02:28:09,022 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 02:28:09,609 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 02:28:41,205 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\tf_keras\src\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.

2025-05-20 02:28:42,257 - datasets - INFO - PyTorch version 2.6.0 available.
2025-05-20 02:28:42,272 - datasets - INFO - TensorFlow version 2.19.0 available.
2025-05-20 02:28:42,278 - datasets - INFO - JAX version 0.4.17 available.
2025-05-20 02:28:49,935 - scapy.runtime - WARNING - No libpcap provider available ! pcap won't be used
2025-05-20 02:28:50,592 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:512: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 02:28:50,592 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:516: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 02:28:59,884 - __main__ - INFO - Starting crawling with Selenium: https://www.flipkart.com
2025-05-20 02:28:59,887 - WDM - INFO - ====== WebDriver manager ======
2025-05-20 02:29:02,686 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-05-20 02:29:02,939 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-05-20 02:29:03,243 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.94\chromedriver-win32/chromedriver.exe] found in cache
2025-05-20 02:35:24,726 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 02:35:24,961 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 02:35:47,357 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\tf_keras\src\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.

2025-05-20 02:35:48,257 - datasets - INFO - PyTorch version 2.6.0 available.
2025-05-20 02:35:48,257 - datasets - INFO - TensorFlow version 2.19.0 available.
2025-05-20 02:35:48,270 - datasets - INFO - JAX version 0.4.17 available.
2025-05-20 02:35:53,071 - scapy.runtime - WARNING - No libpcap provider available ! pcap won't be used
2025-05-20 02:35:53,418 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:512: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 02:35:53,418 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:516: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 02:37:58,193 - __main__ - INFO - Starting crawling with Selenium: https://www.flipkart.com
2025-05-20 02:37:58,193 - WDM - INFO - ====== WebDriver manager ======
2025-05-20 02:38:00,542 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-05-20 02:38:01,044 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-05-20 02:38:01,526 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.94\chromedriver-win32/chromedriver.exe] found in cache
2025-05-20 02:43:42,390 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 02:43:42,942 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 02:44:15,468 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\tf_keras\src\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.

2025-05-20 02:44:16,821 - datasets - INFO - PyTorch version 2.6.0 available.
2025-05-20 02:44:16,838 - datasets - INFO - TensorFlow version 2.19.0 available.
2025-05-20 02:44:16,844 - datasets - INFO - JAX version 0.4.17 available.
2025-05-20 02:44:25,550 - scapy.runtime - WARNING - No libpcap provider available ! pcap won't be used
2025-05-20 02:44:25,985 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:512: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 02:44:25,988 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:516: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 02:44:32,436 - __main__ - INFO - Starting crawling with Selenium: https://www.flipkart.com
2025-05-20 02:44:32,439 - WDM - INFO - ====== WebDriver manager ======
2025-05-20 02:44:35,068 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-05-20 02:44:35,373 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-05-20 02:44:35,622 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.94\chromedriver-win32/chromedriver.exe] found in cache
2025-05-20 16:26:05,646 - tensorflow - WARNING - From c:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 16:26:06,001 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 16:26:36,169 - tensorflow - WARNING - From c:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\tf_keras\src\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.

2025-05-20 16:26:36,980 - datasets - INFO - PyTorch version 2.6.0 available.
2025-05-20 16:26:36,988 - datasets - INFO - TensorFlow version 2.19.0 available.
2025-05-20 16:26:36,988 - datasets - INFO - JAX version 0.4.17 available.
2025-05-20 16:26:46,180 - scapy.runtime - WARNING - No libpcap provider available ! pcap won't be used
2025-05-20 16:26:46,651 - py.warnings - WARNING - c:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:512: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 16:26:46,651 - py.warnings - WARNING - c:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:516: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 16:27:03,751 - __main__ - INFO - Starting crawling with Selenium: https://www.flipkart.com
2025-05-20 16:27:03,752 - WDM - INFO - ====== WebDriver manager ======
2025-05-20 16:27:05,142 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-05-20 16:27:05,757 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-05-20 16:27:06,017 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.94\chromedriver-win32/chromedriver.exe] found in cache
2025-05-20 17:35:00,910 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 17:35:01,149 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 17:35:21,125 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\tf_keras\src\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.

2025-05-20 17:35:21,927 - datasets - INFO - PyTorch version 2.6.0 available.
2025-05-20 17:35:21,930 - datasets - INFO - TensorFlow version 2.19.0 available.
2025-05-20 17:35:21,957 - datasets - INFO - JAX version 0.4.17 available.
2025-05-20 17:36:55,778 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 17:36:56,031 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 17:37:17,732 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\tf_keras\src\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.

2025-05-20 17:37:19,072 - datasets - INFO - PyTorch version 2.6.0 available.
2025-05-20 17:37:19,085 - datasets - INFO - TensorFlow version 2.19.0 available.
2025-05-20 17:37:19,089 - datasets - INFO - JAX version 0.4.17 available.
2025-05-20 17:37:23,933 - scapy.runtime - WARNING - No libpcap provider available ! pcap won't be used
2025-05-20 17:37:24,616 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:512: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 17:37:24,618 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:516: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 17:37:39,131 - __main__ - ERROR - Error in start_new_scan function: crawl_with_selenium_only() takes from 1 to 4 positional arguments but 5 were given
2025-05-20 17:39:56,481 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 17:39:56,712 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 17:40:15,283 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\tf_keras\src\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.

2025-05-20 17:40:16,078 - datasets - INFO - PyTorch version 2.6.0 available.
2025-05-20 17:40:16,080 - datasets - INFO - TensorFlow version 2.19.0 available.
2025-05-20 17:40:16,084 - datasets - INFO - JAX version 0.4.17 available.
2025-05-20 17:40:21,546 - scapy.runtime - WARNING - No libpcap provider available ! pcap won't be used
2025-05-20 17:40:22,090 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:512: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 17:40:22,093 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:516: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 17:40:30,403 - __main__ - ERROR - Error in start_new_scan function: crawl_with_selenium_only() got an unexpected keyword argument 'num_workers'
2025-05-20 17:43:55,067 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 17:43:55,318 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 17:44:17,558 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\tf_keras\src\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.

2025-05-20 17:44:18,287 - datasets - INFO - PyTorch version 2.6.0 available.
2025-05-20 17:44:18,292 - datasets - INFO - TensorFlow version 2.19.0 available.
2025-05-20 17:44:18,293 - datasets - INFO - JAX version 0.4.17 available.
2025-05-20 17:44:23,003 - scapy.runtime - WARNING - No libpcap provider available ! pcap won't be used
2025-05-20 17:44:23,359 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:512: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 17:44:23,360 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:516: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 17:44:29,956 - __main__ - ERROR - Error in start_new_scan function: crawl_with_selenium_only() takes from 1 to 4 positional arguments but 5 were given
2025-05-20 17:48:39,639 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 17:48:39,862 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 17:49:05,367 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\tf_keras\src\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.

2025-05-20 17:49:06,226 - datasets - INFO - PyTorch version 2.6.0 available.
2025-05-20 17:49:06,231 - datasets - INFO - TensorFlow version 2.19.0 available.
2025-05-20 17:49:06,237 - datasets - INFO - JAX version 0.4.17 available.
2025-05-20 17:49:11,279 - scapy.runtime - WARNING - No libpcap provider available ! pcap won't be used
2025-05-20 17:49:11,595 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:512: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 17:49:11,596 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:516: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 17:51:39,324 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 17:51:39,644 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 17:54:50,882 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 17:54:51,265 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 17:57:54,235 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 17:57:54,495 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 17:58:16,942 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\tf_keras\src\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.

2025-05-20 17:58:18,094 - datasets - INFO - PyTorch version 2.6.0 available.
2025-05-20 17:58:18,098 - datasets - INFO - TensorFlow version 2.19.0 available.
2025-05-20 17:58:18,104 - datasets - INFO - JAX version 0.4.17 available.
2025-05-20 17:58:23,547 - scapy.runtime - WARNING - No libpcap provider available ! pcap won't be used
2025-05-20 17:58:23,956 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:512: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 17:58:23,956 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:516: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 18:00:29,832 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 18:00:30,054 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 18:00:49,351 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\tf_keras\src\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.

2025-05-20 18:00:50,050 - datasets - INFO - PyTorch version 2.6.0 available.
2025-05-20 18:00:50,055 - datasets - INFO - TensorFlow version 2.19.0 available.
2025-05-20 18:00:50,059 - datasets - INFO - JAX version 0.4.17 available.
2025-05-20 18:00:54,477 - scapy.runtime - WARNING - No libpcap provider available ! pcap won't be used
2025-05-20 18:00:54,856 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:512: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 18:00:54,858 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:516: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 18:01:00,326 - __main__ - INFO - Starting crawling with Selenium: https://www.flipkart.com
2025-05-20 18:01:00,327 - WDM - INFO - ====== WebDriver manager ======
2025-05-20 18:01:02,484 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-05-20 18:01:03,289 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-05-20 18:01:03,792 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.94\chromedriver-win32/chromedriver.exe] found in cache
2025-05-20 18:05:37,962 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 18:05:38,142 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 18:05:56,931 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\tf_keras\src\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.

2025-05-20 18:05:57,666 - datasets - INFO - PyTorch version 2.6.0 available.
2025-05-20 18:05:57,677 - datasets - INFO - TensorFlow version 2.19.0 available.
2025-05-20 18:05:57,680 - datasets - INFO - JAX version 0.4.17 available.
2025-05-20 18:06:02,241 - scapy.runtime - WARNING - No libpcap provider available ! pcap won't be used
2025-05-20 18:06:02,608 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:512: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 18:06:02,608 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:516: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 18:07:52,647 - __main__ - INFO - Starting crawling with Selenium: https://www.flipkart.com
2025-05-20 18:07:52,647 - WDM - INFO - ====== WebDriver manager ======
2025-05-20 18:07:55,863 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-05-20 18:07:56,103 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-05-20 18:07:56,878 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.94\chromedriver-win32/chromedriver.exe] found in cache
2025-05-20 18:33:29,927 - tensorflow - WARNING - From c:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 18:33:30,227 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 18:34:12,947 - tensorflow - WARNING - From c:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\tf_keras\src\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.

2025-05-20 18:34:14,374 - datasets - INFO - PyTorch version 2.6.0 available.
2025-05-20 18:34:14,379 - datasets - INFO - TensorFlow version 2.19.0 available.
2025-05-20 18:34:14,386 - datasets - INFO - JAX version 0.4.17 available.
2025-05-20 18:34:23,254 - scapy.runtime - WARNING - No libpcap provider available ! pcap won't be used
2025-05-20 18:34:23,750 - py.warnings - WARNING - c:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:512: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 18:34:23,750 - py.warnings - WARNING - c:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:516: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 18:34:28,544 - __main__ - INFO - Starting crawling with Selenium: https://www.flipkart.com
2025-05-20 18:34:28,545 - WDM - INFO - ====== WebDriver manager ======
2025-05-20 18:34:31,063 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-05-20 18:34:31,306 - WDM - INFO - Get LATEST chromedriver version for google-chrome
2025-05-20 18:34:31,575 - WDM - INFO - Driver [C:\Users\<USER>\.wdm\drivers\chromedriver\win64\136.0.7103.94\chromedriver-win32/chromedriver.exe] found in cache
2025-05-20 18:51:58,639 - tensorflow - WARNING - From c:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 18:51:59,041 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 18:52:48,224 - tensorflow - WARNING - From c:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\tf_keras\src\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.

2025-05-20 18:52:49,950 - datasets - INFO - PyTorch version 2.6.0 available.
2025-05-20 18:52:49,956 - datasets - INFO - TensorFlow version 2.19.0 available.
2025-05-20 18:52:49,961 - datasets - INFO - JAX version 0.4.17 available.
2025-05-20 18:53:04,241 - scapy.runtime - WARNING - No libpcap provider available ! pcap won't be used
2025-05-20 18:53:05,600 - py.warnings - WARNING - c:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:512: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 18:53:05,600 - py.warnings - WARNING - c:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:516: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 19:00:10,102 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 19:00:10,418 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 19:00:47,837 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\tf_keras\src\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.

2025-05-20 19:00:48,927 - datasets - INFO - PyTorch version 2.6.0 available.
2025-05-20 19:00:48,936 - datasets - INFO - TensorFlow version 2.19.0 available.
2025-05-20 19:00:48,941 - datasets - INFO - JAX version 0.4.17 available.
2025-05-20 19:00:57,256 - scapy.runtime - WARNING - No libpcap provider available ! pcap won't be used
2025-05-20 19:00:58,655 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:512: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 19:00:58,658 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:516: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 19:03:40,809 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\alibi\explainers\cem.py:35: The name tf.Session is deprecated. Please use tf.compat.v1.Session instead.

2025-05-20 19:03:41,170 - art.config - INFO - set ART_DATA_PATH to C:\Users\<USER>\.art\data
2025-05-20 19:04:18,808 - tensorflow - WARNING - From C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\tf_keras\src\losses.py:2976: The name tf.losses.sparse_softmax_cross_entropy is deprecated. Please use tf.compat.v1.losses.sparse_softmax_cross_entropy instead.

2025-05-20 19:04:20,161 - datasets - INFO - PyTorch version 2.6.0 available.
2025-05-20 19:04:20,174 - datasets - INFO - TensorFlow version 2.19.0 available.
2025-05-20 19:04:20,185 - datasets - INFO - JAX version 0.4.17 available.
2025-05-20 19:04:26,440 - scapy.runtime - WARNING - No libpcap provider available ! pcap won't be used
2025-05-20 19:04:27,661 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:512: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

2025-05-20 19:04:27,670 - py.warnings - WARNING - C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\site-packages\scapy\layers\ipsec.py:516: CryptographyDeprecationWarning: TripleDES has been moved to cryptography.hazmat.decrepit.ciphers.algorithms.TripleDES and will be removed from this module in 48.0.0.
  cipher=algorithms.TripleDES,

