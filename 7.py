import os
import time
import csv
import requests
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options
from webdriver_manager.chrome import ChromeDriverManager
from PIL import Image
import joblib
from datetime import datetime
from bs4 import BeautifulSoup
from selenium.common.exceptions import TimeoutException
from urllib3.util.retry import Retry
from requests.adapters import HTTPAdapter

# إنشاء مجلدات لحفظ النتائج
os.makedirs("results1", exist_ok=True)
os.makedirs("screenshots1", exist_ok=True)

# تهيئة متصفح Chrome باستخدام Selenium
def setup_browser():
    chrome_options = Options()
    chrome_options.add_argument("--headless")
    chrome_options.add_argument("--disable-gpu")
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    chrome_options.add_argument("--remote-debugging-port=9222")
    chrome_options.add_argument("--disable-blink-features=AutomationControlled")
    chrome_options.add_argument("--ignore-certificate-errors")
    chrome_options.add_argument("--allow-insecure-localhost")
    chrome_options.add_argument("--disable-web-security")

    driver = webdriver.Chrome(
        service=Service(ChromeDriverManager().install()),
        options=chrome_options
    )
    driver.set_page_load_timeout(180)  # زيادة مهلة القراءة
    return driver

# التقاط لقطة شاشة
def take_screenshot(driver, filename):
    screenshot_path = f"screenshots1/{filename}.png"
    try:
        driver.save_screenshot(screenshot_path)
        print(f"تم حفظ لقطة الشاشة: {screenshot_path}")
        return screenshot_path
    except Exception as e:
        print(f"تعذر حفظ لقطة الشاشة {filename}: {e}")
        return ""

# حفظ استجابة الخادم
def save_server_response(response_text, filename):
    response_path = f"results1/{filename}.html"
    try:
        if response_text:  # تأكد من أن البيانات غير فارغة
            with open(response_path, "w", encoding="utf-8") as file:
                file.write(response_text)
            print(f"تم حفظ استجابة الخادم: {response_path}")
            return response_path
        else:
            print(f"استجابة الخادم فارغة لـ {filename}.")
            return ""
    except Exception as e:
        print(f"تعذر حفظ استجابة الخادم {filename}: {e}")
        return ""

# تحميل النماذج والمتجهات
def load_models_and_vectorizers():
    filenames = [
        ("vectorizer", "exploitdb_vectorizer_full.pkl"),
        ("model", "exploitdb_model_full.pkl"),
        ("model0", "vuln_model.pk1"),
        ("vectorizer0", "Vectorizer.pk1"),
        ("model01", "vuln_model0.pk1"),
        ("vectorizer01", "Vectorizer0.pk1"),
        ("model5", "vuln_model5.pk1"),
        ("vectorizer5", "Vectorizer5.pk1"),
        ("model00", "vuln_model00.pk1"),
        ("vectorizer00", "Vectorizer00.pk1"),
        ("classified_model", "classified_vulnerabilities_model.pkl"),
        ("classified_vectorizer", "classified_vulnerabilities_vectorizer.pkl"),
        ("classified_model99", "classified_vulnerabilities_model99.pkl"),
        ("classified_vectorizer99", "classified_vulnerabilities_vectorizer99.pkl"),
        ("classified_model22", "classified_vulnerabilities_model22.pkl"),
        ("classified_vectorizer22", "classified_vulnerabilities_vectorizer22.pkl"),
        ( "final_dataset00_model",'final_dataset00.pkl'),
        ( "final_dataset00_vectorizer",'final_dataset00_Vectorizer1.pkl'),
        ("github_payloads2_model", 'github_payloads2.pkl'),
        ( "github_payloads2_vectorizer",'github_payloads2_Vectorizer1.pkl'),
        ("github_payloads1_model",'github_payloads1.pkl'),
        ( "github_payloads1_vectorizer",'github_payloads1_Vectorizer1.pkl')
    ]
    return {name: joblib.load(file) for name, file in filenames}

# توليد البايلودات
def generate_payload(models_and_vectorizers, vulnerability_type):
    vectorizer = models_and_vectorizers.get(f"vectorizer_{vulnerability_type.lower()}")
    model = models_and_vectorizers.get(f"model_{vulnerability_type.lower()}")
    if vectorizer and model:
        return model.predict(vectorizer.transform([vulnerability_type]))[0]
    return ""

# استخراج طريقة الاختبار
SPECIALIZED_SITES = [
    "https://owasp.org/www-community/vulnerabilities/",
    "https://nvd.nist.gov/",
    "https://cve.mitre.org/",
    "https://www.exploit-db.com/",
    #"https://www.securityfocus.com/",
    "https://www.cvedetails.com/",
    "https://vuldb.com/",
    "https://www.rapid7.com/db/",
    "https://www.acunetix.com/vulnerabilities/",
]

def get_method_from_external_source(vulnerability_type):
    for site in SPECIALIZED_SITES:
        try:
            search_url = f"{site}{vulnerability_type}"
            response = requests.get(search_url, timeout=10)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                page_text = soup.get_text().lower()
                if "get" in page_text:
                    return "GET"
                elif "post" in page_text:
                    return "POST"
        except Exception as e:
            print(f"حدث خطأ أثناء البحث في {site}: {e}")
    return "GET"

# التحقق من الموقع
def is_site_up(url):
    try:
        response = requests.get(url, timeout=10)
        if response.status_code == 200:
            return True
        print(f"الموقع {url} غير متاح. الكود: {response.status_code}")
    except requests.exceptions.RequestException as e:
        print(f"فشل في الوصول إلى {url}: {e}")
    return False

# اختبار الثغرات
def test_vulnerability(url, vulnerability_type, driver, models_and_vectorizers):
    print(f"جارٍ اختبار ثغرة {vulnerability_type} على {url}...")

    if not is_site_up(url):
        return {"result": "فشل", "url": url, "vulnerability_type": vulnerability_type}

    try:
        driver.get(url)
    except TimeoutException:
        print(f"تم تجاوز الوقت المحدد للوصول إلى {url}.")
        return {"result": "فشل", "url": url, "vulnerability_type": vulnerability_type}
    except Exception as e:
        print(f"حدث خطأ غير متوقع: {e}")
        return {"result": "فشل", "url": url, "vulnerability_type": vulnerability_type}

    payload = generate_payload(models_and_vectorizers, vulnerability_type)
    method = get_method_from_external_source(vulnerability_type)

    test_details = {
        "url": url,
        "vulnerability_type": vulnerability_type,
        "payload": payload,
        "method": method,
        "result": "فشل",
        "screenshot": "",
        "server_response": "",
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
    }

    if method == "GET":
        test_url = f"{url}?input={payload}" if "?" in url else f"{url}/{payload}"
        driver.get(test_url)
    elif method == "POST":
        headers = {"Content-Type": "application/xml"} if vulnerability_type == "XXE" else {}
        session = requests.Session()
        retries = Retry(total=5, backoff_factor=0.1, status_forcelist=[500, 502, 503, 504])
        session.mount('http://', HTTPAdapter(max_retries=retries))
        session.mount('https://', HTTPAdapter(max_retries=retries))
        response = session.post(url, data=payload, headers=headers, timeout=10)
        test_details["result"] = "نجاح" if "root:" in response.text else "فشل"
        test_details["server_response"] = save_server_response(response.text, f"{vulnerability_type}_{url.replace('/', '_')}")
        return test_details

    time.sleep(2)
    screenshot_path = take_screenshot(driver, f"{vulnerability_type}_{url.replace('/', '_')}")
    test_details["screenshot"] = screenshot_path
    test_details["result"] = "نجاح" if "error" not in driver.page_source.lower() else "فشل"
    return test_details

# إنشاء تقرير
def create_report(test_results):
    report_path = f"results1/report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
    try:
        with open(report_path, "w", newline="", encoding="utf-8") as file:
            writer = csv.DictWriter(file, fieldnames=test_results[0].keys())
            writer.writeheader()
            writer.writerows(test_results)
        print(f"تم إنشاء التقرير: {report_path}")
    except Exception as e:
        print(f"تعذر إنشاء التقرير: {e}")

# الدالة الرئيسية
if __name__ == "__main__":
    models_and_vectorizers = load_models_and_vectorizers()
    with open("vulnerability_report1.csv", "r", encoding="utf-8") as file:
        vulnerabilities = list(csv.DictReader(file))

    test_results = []
    for vuln in vulnerabilities:
        driver = setup_browser()
        test_details = test_vulnerability(vuln["Direct Link"], vuln["Prediction"], driver, models_and_vectorizers)
        test_results.append(test_details)
        driver.quit()

    create_report(test_results)