import asyncio
from fixed import crawl_with_selenium_only_sync

# تشغيل اختبار دالة الزحف المحسنة
test_url = "https://example.com"
print("="*50)
print("بدء اختبار دالة الزحف المحسنة...")
print(f"URL للاختبار: {test_url}")
print("="*50)

# استدعاء دالة الزحف المتزامنة (wrapper للدالة الأصلية)
links = crawl_with_selenium_only_sync(
    url=test_url,
    max_depth=2,  # عمق منخفض للاختبار السريع
    max_links=20,  # عدد قليل من الروابط للاختبار
    max_per_domain=10,
    num_workers=2
)

print("="*50)
print(f"تم العثور على {len(links)} روابط.")
print("الروابط التي تم العثور عليها:")
for i, link in enumerate(links, 1):
    print(f"{i}. {link}")
print("="*50)
print("اكتمل الاختبار بنجاح")
