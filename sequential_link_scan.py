"""
Sequential Link Scanning Module

This module provides the sequential_link_scan function for comprehensive vulnerability scanning
of a single link with enhanced evidence collection including screenshots, request/response logging,
and detailed vulnerability reporting.
"""

import os
import re
import json
import time
import asyncio
import logging
import hashlib
from datetime import datetime
from urllib.parse import urlparse, parse_qs
from bs4 import BeautifulSoup
import requests
from colorama import Fore, init

# Import custom modules
try:
    from vulnerability_scanner_functions import test_forms_for_vulnerabilities, test_url_parameters_for_vulnerabilities
except ImportError:
    print(Fore.RED + "[-] Error: vulnerability_scanner_functions module not found")
    raise

# Import enhanced vulnerability evidence module
try:
    from vulnerability_evidence_enhanced import log_vulnerability_enhanced, log_vulnerability_async
    print(Fore.GREEN + "[+] Successfully imported enhanced vulnerability evidence module")
except ImportError:
    try:
        from vulnerability_evidence import log_vulnerability
        print(Fore.GREEN + "[+] Successfully imported vulnerability_evidence module")

        # Create async wrapper for the original function
        async def log_vulnerability_async(vulnerability, url, evidence_dir, session_id=None):
            return log_vulnerability(vulnerability, url, evidence_dir, session_id)

    except ImportError:
        print(Fore.YELLOW + "[!] Warning: vulnerability evidence modules not found. Using fallback implementation.")

        def log_vulnerability(vulnerability, url, evidence_dir, session_id=None):
            """Fallback implementation for log_vulnerability"""
            print(Fore.YELLOW + f"[!] Enhanced vulnerability logging disabled")
            return vulnerability

        async def log_vulnerability_async(vulnerability, url, evidence_dir, session_id=None):
            """Async fallback implementation for log_vulnerability"""
            print(Fore.YELLOW + f"[!] Enhanced vulnerability logging disabled")
            return vulnerability

try:
    from capture_screenshot import capture_screenshot_sync
except ImportError:
    print(Fore.YELLOW + "[!] Warning: capture_screenshot module not found. Screenshot functionality will be disabled.")
    def capture_screenshot_sync(url, output_dir, filename, include_vulnerability_info=None):
        print(Fore.YELLOW + f"[!] Screenshot capture disabled: {url}")
        return None

try:
    from log_request_response import log_http_request_response, generate_vulnerability_summary
except ImportError:
    print(Fore.YELLOW + "[!] Warning: log_request_response module not found. Request/response logging will be disabled.")
    def log_http_request_response(url, method, headers, params, data, payload, response, vuln_type, vuln_subtype, param_name, evidence_dir):
        print(Fore.YELLOW + f"[!] Request/response logging disabled: {url}")
        return None, None

    def generate_vulnerability_summary(vulnerabilities, url):
        summary = f"\n{Fore.CYAN}[*] ======== VULNERABILITY SUMMARY FOR {url} ========\n\n"
        summary += f"{Fore.CYAN}[*] Total vulnerabilities: {len(vulnerabilities)}\n"
        return summary

# Initialize colorama
init(autoreset=True)

# Configure logging
logger = logging.getLogger(__name__)

# Global constants
REQUEST_TIMEOUT = 10  # seconds
MAX_RETRIES = 3
VERIFY_SSL = False
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Connection': 'keep-alive',
    'Upgrade-Insecure-Requests': '1'
}

async def sequential_link_scan(url, output_path=None, payload_file=None, session_id=None):
    """
    Perform a comprehensive sequential vulnerability scan on a single link with detailed evidence collection.

    This function:
    1. Extracts all forms and URL parameters from the page
    2. Tests each form input for vulnerabilities
    3. Tests each URL parameter for vulnerabilities
    4. Captures screenshots for evidence
    5. Logs HTTP requests and responses
    6. Generates detailed reports

    Args:
        url (str): The URL to scan
        output_path (str, optional): Directory to save evidence and reports
        payload_file (str, optional): Path to JSON file with custom payloads
        session_id (str, optional): Session ID for organizing evidence

    Returns:
        list: List of detected vulnerabilities
    """
    print(Fore.CYAN + f"\n[*] ======== SEQUENTIAL LINK SCAN: {url} ========")

    # Create a unique scan ID or use the provided session_id
    if not session_id:
        scan_id = hashlib.md5(f"{url}_{datetime.now().isoformat()}".encode()).hexdigest()[:8]
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    else:
        scan_id = session_id
        timestamp = session_id

    # Set up output directory
    if not output_path:
        output_path = os.path.join("scan_results", f"scan_{timestamp}_{scan_id}")

    # Create enhanced evidence directory structure
    evidence_base_dir = os.path.join(output_path, "evidence")
    reports_dir = os.path.join(output_path, "reports")

    # Create session-specific evidence directories
    evidence_dir = os.path.join(evidence_base_dir, scan_id)
    screenshots_dir = os.path.join(evidence_dir, "screenshots")
    forms_dir = os.path.join(evidence_dir, "forms")
    requests_dir = os.path.join(evidence_dir, "requests")
    responses_dir = os.path.join(evidence_dir, "responses")

    # Create all directories
    for directory in [evidence_base_dir, evidence_dir, reports_dir, screenshots_dir, forms_dir, requests_dir, responses_dir]:
        os.makedirs(directory, exist_ok=True)

    print(Fore.GREEN + f"[+] Created evidence directories in {evidence_dir}")

    # Initialize vulnerabilities list
    vulnerabilities = []

    try:
        # 1. Fetch the page
        print(Fore.CYAN + f"[*] Fetching page: {url}")

        for attempt in range(MAX_RETRIES):
            try:
                response = requests.get(
                    url,
                    headers=HEADERS,
                    timeout=REQUEST_TIMEOUT,
                    verify=VERIFY_SSL
                )
                response.raise_for_status()
                break
            except requests.RequestException as e:
                if attempt < MAX_RETRIES - 1:
                    print(Fore.YELLOW + f"[!] Request failed (attempt {attempt+1}/{MAX_RETRIES}): {str(e)}")
                    time.sleep(2)
                else:
                    print(Fore.RED + f"[-] Failed to fetch page after {MAX_RETRIES} attempts: {str(e)}")
                    return []

        # 2. Take initial screenshot
        print(Fore.CYAN + f"[*] Capturing initial screenshot")
        initial_screenshot = await asyncio.to_thread(
            capture_screenshot_sync,
            url,
            screenshots_dir,
            f"initial_{scan_id}.png"
        )

        if initial_screenshot:
            print(Fore.GREEN + f"[+] Initial screenshot saved: {initial_screenshot}")

        # 3. Extract page elements
        print(Fore.CYAN + f"[*] Extracting page elements")
        page_elements = extract_page_elements(response.text, url)

        print(Fore.GREEN + f"[+] Found {len(page_elements['forms'])} forms and {len(page_elements['url_params'])} URL parameters")

        # 4. Test forms for vulnerabilities
        if page_elements['forms']:
            print(Fore.CYAN + f"[*] Testing {len(page_elements['forms'])} forms for vulnerabilities")
            form_vulnerabilities = await test_forms_for_vulnerabilities(
                url=url,
                forms=page_elements['forms'],
                evidence_dir=evidence_dir,
                payload_file=payload_file
            )

            # Log comprehensive evidence for each form vulnerability
            if form_vulnerabilities:
                print(Fore.GREEN + f"[+] Found {len(form_vulnerabilities)} form vulnerabilities")

                # Process each vulnerability with enhanced evidence collection
                for i, vuln in enumerate(form_vulnerabilities):
                    print(Fore.CYAN + f"[*] Processing form vulnerability {i+1}/{len(form_vulnerabilities)}...")

                    # Log comprehensive vulnerability evidence using async function
                    enhanced_vuln = await log_vulnerability_async(vuln, url, evidence_dir, scan_id)
                    form_vulnerabilities[i] = enhanced_vuln

                vulnerabilities.extend(form_vulnerabilities)
            else:
                print(Fore.YELLOW + f"[!] No form vulnerabilities found")

        # 5. Test URL parameters for vulnerabilities
        if page_elements['url_params']:
            print(Fore.CYAN + f"[*] Testing URL parameters for vulnerabilities")
            param_vulnerabilities = await test_url_parameters_for_vulnerabilities(
                url=url,
                params=page_elements['url_params'],
                evidence_dir=evidence_dir,
                payload_file=payload_file
            )

            # Log comprehensive evidence for each parameter vulnerability
            if param_vulnerabilities:
                print(Fore.GREEN + f"[+] Found {len(param_vulnerabilities)} URL parameter vulnerabilities")

                # Process each vulnerability with enhanced evidence collection
                for i, vuln in enumerate(param_vulnerabilities):
                    print(Fore.CYAN + f"[*] Processing parameter vulnerability {i+1}/{len(param_vulnerabilities)}...")

                    # Log comprehensive vulnerability evidence using async function
                    enhanced_vuln = await log_vulnerability_async(vuln, url, evidence_dir, scan_id)
                    param_vulnerabilities[i] = enhanced_vuln

                vulnerabilities.extend(param_vulnerabilities)
            else:
                print(Fore.YELLOW + f"[!] No URL parameter vulnerabilities found")

        # 6. Generate vulnerability summary
        if vulnerabilities:
            summary = generate_vulnerability_summary(vulnerabilities, url)
            print(summary)

            # Save summary to file
            summary_file = os.path.join(reports_dir, f"vulnerability_summary_{scan_id}.txt")
            with open(summary_file, 'w', encoding='utf-8') as f:
                # Remove ANSI color codes for file output
                clean_summary = re.sub(r'\x1b\[\d+m', '', summary)
                f.write(clean_summary)

            print(Fore.GREEN + f"[+] Vulnerability summary saved to: {summary_file}")

            # Save detailed JSON report
            json_report = os.path.join(reports_dir, f"vulnerability_report_{scan_id}.json")
            with open(json_report, 'w', encoding='utf-8') as f:
                # Create a serializable version of the vulnerabilities
                serializable_vulns = []
                for vuln in vulnerabilities:
                    serializable_vuln = {k: v for k, v in vuln.items() if k != 'response'}
                    serializable_vulns.append(serializable_vuln)

                json.dump({
                    'url': url,
                    'scan_id': scan_id,
                    'timestamp': datetime.now().isoformat(),
                    'total_vulnerabilities': len(vulnerabilities),
                    'vulnerabilities': serializable_vulns
                }, f, indent=2)

            print(Fore.GREEN + f"[+] Detailed vulnerability report saved to: {json_report}")

        # 7. Generate HTML report if available
        try:
            from html_report_66 import generate_detailed_html_report
            html_report = os.path.join(reports_dir, f"vulnerability_report_{scan_id}.html")

            html_content = generate_detailed_html_report({
                'url': url,
                'scan_id': scan_id,
                'timestamp': datetime.now().isoformat(),
                'total_vulnerabilities': len(vulnerabilities),
                'vulnerabilities': vulnerabilities
            }, evidence_dir)

            with open(html_report, 'w', encoding='utf-8') as f:
                f.write(html_content)

            print(Fore.GREEN + f"[+] HTML vulnerability report saved to: {html_report}")
        except ImportError:
            print(Fore.YELLOW + f"[!] HTML report generator not available")

        print(Fore.CYAN + f"[*] ======== SEQUENTIAL LINK SCAN COMPLETED ========")
        return vulnerabilities

    except Exception as e:
        logger.error(f"Error in sequential link scan: {str(e)}")
        print(Fore.RED + f"[-] Error in sequential link scan: {str(e)}")
        return []

def extract_page_elements(html_content, url):
    """
    Extract forms and URL parameters from a page.

    Args:
        html_content (str): HTML content of the page
        url (str): URL of the page

    Returns:
        dict: Dictionary with 'forms' and 'url_params' keys
    """
    # Parse HTML
    soup = BeautifulSoup(html_content, 'html.parser')

    # Extract URL parameters
    parsed_url = urlparse(url)
    url_params = parse_qs(parsed_url.query)

    # Extract forms
    forms = []
    for form_element in soup.find_all('form'):
        form = {
            'action': form_element.get('action', ''),
            'method': form_element.get('method', 'get'),
            'id': form_element.get('id', ''),
            'name': form_element.get('name', ''),
            'inputs': []
        }

        # Extract inputs
        for input_element in form_element.find_all(['input', 'textarea', 'select']):
            input_type = input_element.get('type', 'text') if input_element.name == 'input' else input_element.name

            input_field = {
                'name': input_element.get('name', ''),
                'type': input_type,
                'value': input_element.get('value', ''),
                'id': input_element.get('id', ''),
                'required': input_element.has_attr('required')
            }

            form['inputs'].append(input_field)

        forms.append(form)

    return {
        'forms': forms,
        'url_params': url_params
    }
