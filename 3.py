import os
import joblib
import requests
from lxml import html
from urllib.parse import urljoin, urlparse, urlunparse
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Embedding, LSTM, Dense, Conv1D, MaxPooling1D, Flatten
from tensorflow.keras.preprocessing.text import Tokenizer
from tensorflow.keras.preprocessing.sequence import pad_sequences
from sklearn.feature_extraction.text import TfidfVectorizer
from nltk.tokenize import word_tokenize
from nltk.stem import WordNetLemmatizer
from PIL import Image, ImageDraw, ImageFont
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
import tensorflow as tf
import time
import unittest
from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
from colorama import Fore, Style, init
from stable_baselines3 import PPO
import gym
from gym import spaces
from sklearn.ensemble import IsolationForest
import subprocess
import dns.resolver
import shap
import torch
import networkx as nx
import pytorch_lightning as pl
from alibi.explainers import Counterfactual
import seaborn as sns
import matplotlib.pyplot as plt
from art.attacks.evasion import FastGradientMethod
from art.estimators.classification import TensorFlowV2Classifier
import h5py
from pennylane import numpy as np
import pennylane as qml
from transformers import BertTokenizer, BertForSequenceClassification, pipeline
from gtts import gTTS
import speech_recognition as sr
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import gradio as gr
import tempfile
import brian2
from sklearn.svm import OneClassSVM
from stable_baselines3.common.env_util import make_vec_env
from stable_baselines3.common.vec_env import DummyVecEnv
from stable_baselines3.common.callbacks import BaseCallback
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common import results_plotter
from stable_baselines3.common.results_plotter import load_results, ts2xy
import ast
import astor
import json
import numpy as np
from transformers import Trainer, TrainingArguments
from datasets import Dataset
import optuna
from googletrans import Translator
from functools import lru_cache
import redis.asyncio as redis
import dask
import ray
from dask.distributed import Client
from ray import tune
from ray.tune.schedulers import ASHAScheduler
from ray.tune.integration.keras import TuneReportCallback

# Disable TensorFlow oneDNN warnings
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'

# Initialize colorama
init(autoreset=True)

# Disable TensorFlow warnings
tf.get_logger().setLevel('ERROR')

# Logging setup
logging.basicConfig(filename='vulnerability_scanner.log', level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')

# Headers for HTTP requests
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
}

# Common paths for scanning
COMMON_PATHS = [
    "/admin", "/login", "/wp-admin", "/config", "/backup", "/api", "/test", "/secret",
    "/robots.txt", "/.git", "/.env", "/.htaccess", "/.htpasswd", "/phpinfo.php"
]

# Redis caching
redis_client = redis.Redis(host='localhost', port=6379, db=0)

# Function to load models using joblib with caching
@lru_cache(maxsize=128)
def load_model(file_name):
    print(Fore.YELLOW + f"[*] Loading model: {file_name}")
    try:
        return joblib.load(file_name)
    except Exception as e:
        logging.error(f"Error loading model: {str(e)}")
        print(Fore.RED + f"[-] Error loading model: {str(e)}")
        return None

# Load all models and vectorizers with caching
models_and_vectorizers = {
    "vectorizer": load_model('exploitdb_vectorizer_full.pkl'),
    "model": load_model('exploitdb_model_full.pkl'),
    "model0": load_model('vuln_model.pk1'),
    "vectorizer0": load_model('Vectorizer.pk1'),
    "model01": load_model('vuln_model0.pk1'),
    "vectorizer01": load_model('Vectorizer0.pk1'),
    "model5": load_model('vuln_model5.pk1'),
    "vectorizer5": load_model('Vectorizer5.pk1'),
    "model00": load_model('vuln_model00.pk1'),
    "vectorizer00": load_model('Vectorizer00.pk1'),
    "classified_model": load_model('classified_vulnerabilities_model.pkl'),
    "classified_vectorizer": load_model('classified_vulnerabilities_vectorizer.pkl'),
    "classified_model99": load_model('classified_vulnerabilities_model99.pkl'),
    "classified_vectorizer99": load_model('classified_vulnerabilities_vectorizer99.pkl'),
    "final_dataset00_model": load_model('final_dataset00.pkl'),
    "final_dataset00_vectorizer": load_model('final_dataset00_Vectorizer1.pkl'),
    "github_payloads2_model": load_model('github_payloads2.pkl'),
    "github_payloads2_vectorizer": load_model('github_payloads2_Vectorizer1.pkl'),
    "github_payloads1_model": load_model('github_payloads1.pkl'),
    "github_payloads1_vectorizer": load_model('github_payloads1_Vectorizer1.pkl')
}

# Text preprocessing with caching
@lru_cache(maxsize=1024)
def process_text(content):
    print(Fore.CYAN + "[*] Processing text for vulnerabilities...")
    tokens = word_tokenize(content)
    lemmatizer = WordNetLemmatizer()
    lemmas = [lemmatizer.lemmatize(token) for token in tokens]
    return " ".join(lemmas)

# Load LSTM model with caching
@lru_cache(maxsize=1)
def load_lstm_model():
    print(Fore.YELLOW + "[*] Loading LSTM model...")
    model = Sequential()
    model.add(Embedding(input_dim=5000, output_dim=64))
    model.add(LSTM(64))
    model.add(Dense(1, activation='sigmoid'))
    return model

# Load CNN model for text analysis with caching
@lru_cache(maxsize=1)
def load_cnn_model():
    print(Fore.YELLOW + "[*] Loading CNN model...")
    model = Sequential()
    model.add(Embedding(input_dim=5000, output_dim=64))
    model.add(Conv1D(filters=64, kernel_size=3, activation='relu'))
    model.add(MaxPooling1D(pool_size=2))
    model.add(Flatten())
    model.add(Dense(1, activation='sigmoid'))
    return model

# Fine-tune BERT model on custom data with Data Augmentation and Hyperparameter Tuning
def fine_tune_bert_model(train_texts, train_labels):
    print(Fore.YELLOW + "[*] Fine-tuning BERT model on custom data with Data Augmentation and Hyperparameter Tuning...")
    tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')
    model = BertForSequenceClassification.from_pretrained('bert-base-uncased')

    # Data Augmentation using Back Translation
    def back_translation(texts):
        translator = Translator()
        augmented_texts = []
        for text in texts:
            try:
                translated = translator.translate(text, src='en', dest='fr').text
                back_translated = translator.translate(translated, src='fr', dest='en').text
                augmented_texts.append(back_translated)
            except:
                augmented_texts.append(text)
        return augmented_texts

    augmented_texts = back_translation(train_texts)

    # Prepare dataset
    train_encodings = tokenizer(augmented_texts, truncation=True, padding=True, max_length=128, return_tensors="pt")
    train_dataset = Dataset.from_dict({
        'input_ids': train_encodings['input_ids'].tolist(),
        'attention_mask': train_encodings['attention_mask'].tolist(),
        'labels': train_labels
    })

    # Hyperparameter Optimization using Optuna
    def objective(trial):
        lr = trial.suggest_float("lr", 1e-5, 1e-3, log=True)
        batch_size = trial.suggest_categorical("batch_size", [8, 16, 32])
        args = TrainingArguments(
            output_dir='./results',
            num_train_epochs=3,
            per_device_train_batch_size=batch_size,
            learning_rate=lr,
            save_steps=10,
            save_total_limit=2,
            logging_dir='./logs',
        )
        trainer = Trainer(
            model=model,
            args=args,
            train_dataset=train_dataset,
        )
        trainer.train()
        return trainer.evaluate()

    study = optuna.create_study(direction="maximize")
    study.optimize(objective, n_trials=10)
    best_params = study.best_params
    print(Fore.GREEN + f"[+] Best Hyperparameters: {best_params}")

    # Train with best parameters
    final_args = TrainingArguments(
        output_dir='./results',
        num_train_epochs=3,
        per_device_train_batch_size=best_params['batch_size'],
        learning_rate=best_params['lr'],
        save_steps=10,
        save_total_limit=2,
        logging_dir='./logs',
    )
    final_trainer = Trainer(
        model=model,
        args=final_args,
        train_dataset=train_dataset,
    )
    final_trainer.train()
    return model

# Quantum Neural Networks (QNNs) with PennyLane
def quantum_neural_network_example():
    print(Fore.YELLOW + "[*] Running Quantum Neural Network example with PennyLane...")
    dev = qml.device("default.qubit", wires=2)

    @qml.qnode(dev)
    def circuit(inputs, weights):
        qml.RX(inputs[0], wires=0)
        qml.RY(inputs[1], wires=1)
        qml.CNOT(wires=[0, 1])
        qml.Rot(*weights[0], wires=0)
        qml.Rot(*weights[1], wires=1)
        qml.CNOT(wires=[0, 1])
        return [qml.expval(qml.PauliZ(i)) for i in range(2)]

    inputs = np.array([0.54, 0.12])
    weights = np.random.rand(2, 3)
    result = circuit(inputs, weights)
    print(Fore.GREEN + f"[+] Quantum Neural Network Result: {result}")

# Counterfactual Explanations using Alibi
def generate_counterfactual_explanation(model, input_data):
    print(Fore.YELLOW + "[*] Generating Counterfactual Explanation using Alibi...")
    cf = Counterfactual(model, shape=(1, len(input_data)))
    explanation = cf.explain(input_data)
    if explanation.cf is not None:
        print(Fore.GREEN + f"[+] Counterfactual Explanation: {explanation.cf}")
    else:
        print(Fore.RED + "[-] No Counterfactual Explanation found.")

# Graph Neural Networks (GNNs) for Vulnerability Analysis
def graph_neural_network_analysis(vulnerabilities):
    print(Fore.YELLOW + "[*] Analyzing vulnerabilities using Graph Neural Networks (GNNs)...")
    G = nx.Graph()
    for vuln in vulnerabilities:
        G.add_node(vuln["line_number"], content=vuln["line_content"], prediction=vuln["prediction"])
    for i in range(len(vulnerabilities) - 1):
        G.add_edge(vulnerabilities[i]["line_number"], vulnerabilities[i + 1]["line_number"])
    adjacency_matrix = nx.to_numpy_array(G)
    print(Fore.GREEN + f"[+] Graph Neural Network Analysis Completed. Adjacency Matrix: {adjacency_matrix}")

# Reinforcement Learning for Exploitation
def reinforcement_learning_exploitation():
    print(Fore.YELLOW + "[*] Running Reinforcement Learning for Exploitation...")
    env = make_vec_env(lambda: gym.make('CartPole-v1'), n_envs=4)
    model = PPO('MlpPolicy', env, verbose=1)
    model.learn(total_timesteps=10000)
    print(Fore.GREEN + "[+] Reinforcement Learning for Exploitation Completed.")

# Parallel Processing for Performance Improvement
def parallel_processing_example(tasks):
    print(Fore.YELLOW + "[*] Running Parallel Processing example...")
    with ThreadPoolExecutor(max_workers=10) as executor:
        futures = [executor.submit(task) for task in tasks]
        for future in as_completed(futures):
            result = future.result()
            print(Fore.GREEN + f"[+] Parallel Processing Result: {result}")

# Caching Mechanisms for Performance Improvement
def caching_mechanism_example():
    print(Fore.YELLOW + "[*] Running Caching Mechanism example...")
    @lru_cache(maxsize=128)
    def expensive_function(x):
        return x * x

    result = expensive_function(10)
    print(Fore.GREEN + f"[+] Caching Mechanism Result: {result}")

# Custom Heuristics for Business Logic Vulnerabilities
def detect_business_logic_vulnerabilities(vulnerabilities):
    print(Fore.YELLOW + "[*] Detecting Business Logic Vulnerabilities using Custom Heuristics...")
    business_logic_vulnerabilities = []
    for vuln in vulnerabilities:
        if "business logic" in vuln["prediction"].lower() or "zero-day" in vuln["prediction"].lower() or "human error" in vuln["prediction"].lower():
            business_logic_vulnerabilities.append(vuln)
    print(Fore.GREEN + f"[+] Detected {len(business_logic_vulnerabilities)} Business Logic Vulnerabilities.")
    return business_logic_vulnerabilities

# Human-in-the-Loop Systems for Complex Vulnerability Detection
def human_in_the_loop_system(vulnerabilities):
    print(Fore.YELLOW + "[*] Running Human-in-the-Loop System for Complex Vulnerability Detection...")
    for vuln in vulnerabilities:
        print(f"Review the following vulnerability: {vuln['line_content']} (Prediction: {vuln['prediction']})")
        user_input = input("Is this a valid vulnerability? (yes/no): ").strip().lower()
        if user_input == "yes":
            print(Fore.GREEN + "[+] Vulnerability confirmed by human expert.")
        else:
            print(Fore.RED + "[-] Vulnerability rejected by human expert.")

# Test exploitation (Proof of Concept)
def test_exploit(url, payload):
    print(Fore.MAGENTA + f"[*] Testing exploit on URL: {url} with payload: {payload}")
    try:
        if not isinstance(payload, str):
            payload = str(payload)
        
        response = requests.get(url, headers=HEADERS, params=payload, timeout=30)
        if response.status_code == 200:
            if str(payload) in response.text:
                print(Fore.GREEN + "[+] Exploit simulation successful (payload reflected).")
                return True, "Exploit simulation successful (payload reflected).", dict(response.request.headers), dict(response.headers), response.text
            else:
                print(Fore.RED + "[-] Exploit simulation failed (payload not reflected).")
                return False, "Exploit simulation failed (payload not reflected).", dict(response.request.headers), dict(response.headers), response.text
    except Exception as e:
        logging.error(f"Error testing exploit: {str(e)}")
        print(Fore.RED + f"[-] Error testing exploit: {str(e)}")
        return False, "Exploit simulation failed.", None, None, None

# Fetch CVE details from NVD
def get_cve_details(cve_id):
    print(Fore.CYAN + f"[*] Fetching CVE details for: {cve_id}")
    if not cve_id.startswith("CVE-"):
        print(Fore.RED + f"[-] Invalid CVE ID: {cve_id}")
        return None
    url = f"https://services.nvd.nist.gov/rest/json/cve/1.0/{cve_id}"
    try:
        response = requests.get(url)
        if response.status_code == 200:
            return response.json()
    except Exception as e:
        logging.error(f"Error fetching CVE details: {str(e)}")
        print(Fore.RED + f"[-] Error fetching CVE details: {str(e)}")
        return None

# Fetch details from Exploit-DB
def get_exploit_db_details(query):
    print(Fore.CYAN + f"[*] Fetching Exploit-DB details for: {query}")
    url = f"https://www.exploit-db.com/search?q={query}"
    try:
        response = requests.get(url)
        if response.status_code == 200:
            return response.text
    except Exception as e:
        logging.error(f"Error fetching Exploit-DB details: {str(e)}")
        print(Fore.RED + f"[-] Error fetching Exploit-DB details: {str(e)}")
        return None

# Identify vulnerabilities using all models
def find_vulnerabilities(content, url):
    print(Fore.CYAN + "[*] Identifying vulnerabilities in the content...")
    vulnerabilities = []
    lines = content.splitlines()
    processed_lines = [process_text(line) for line in lines]
    
    # Load LSTM model
    model_lstm = load_lstm_model()
    
    # Load CNN model
    model_cnn = load_cnn_model()
    
    # Load Tokenizer
    tokenizer = Tokenizer(num_words=5000)
    tokenizer.fit_on_texts(processed_lines)
    sequences = tokenizer.texts_to_sequences(processed_lines)
    padded_sequences = pad_sequences(sequences, maxlen=100)
    
    # Predict using LSTM
    predictions_lstm = model_lstm.predict(padded_sequences)
    
    # Predict using CNN
    predictions_cnn = model_cnn.predict(padded_sequences)
    
    for i, (line, prediction_lstm, prediction_cnn) in enumerate(zip(lines, predictions_lstm, predictions_cnn)):
        predictions = {}
        for key, model in models_and_vectorizers.items():
            if "vectorizer" in key:
                continue
            vectorizer_key = key.replace("model", "vectorizer")
            if vectorizer_key in models_and_vectorizers:
                vectorizer = models_and_vectorizers[vectorizer_key]
                try:
                    vectorized_input = vectorizer.transform([processed_lines[i]])
                    prediction = model.predict(vectorized_input)[0]
                    predictions[key] = prediction
                except Exception as e:
                    logging.error(f"Error predicting with model {key}: {str(e)}")
                    print(Fore.RED + f"[-] Error predicting with model {key}: {str(e)}")
                    continue
        
        predictions["lstm_model"] = "Vulnerable" if prediction_lstm > 0.5 else "Safe"
        predictions["cnn_model"] = "Vulnerable" if prediction_cnn > 0.5 else "Safe"
        
        for key, prediction in predictions.items():
            if prediction != "Safe":
                print(Fore.RED + f"[!] Vulnerability detected by model: {key}")
                try:
                    cve_details = get_cve_details(str(prediction))  # Ensure prediction is str
                    exploit_success, exploit_result, request_details, response_details, response_content = test_exploit(url, str(prediction))  # Ensure payload is str
                    exploit_method = determine_exploit_method(prediction)  # Determine exploit method automatically
                    vulnerabilities.append({
                        "line_number": i + 1,
                        "line_content": line.strip(),
                        "prediction": prediction,
                        "model_used": key,
                        "proof_of_concept": f"Exploitation Example for {prediction}",
                        "severity": cve_details.get("impact", {}).get("baseMetricV3", {}).get("cvssV3", {}).get("baseSeverity", "Unknown") if cve_details else "Unknown",
                        "description": cve_details.get("cve", {}).get("description", {}).get("description_data", [{}])[0].get("value", "No description available") if cve_details else "No description available",
                        "direct_link": f"{url}#line-{i + 1}",
                        "full_context": f"Vulnerability found in line {i + 1}: {line.strip()}",
                        "exploit_result": exploit_result,
                        "request_details": request_details,
                        "response_details": response_details,
                        "response_content": response_content,
                        "payload": str(prediction),  # Add payload used
                        "exploit_method": exploit_method  # Add exploit method
                    })
                except Exception as e:
                    logging.error(f"Error processing vulnerability: {str(e)}")
                    print(Fore.RED + f"[-] Error processing vulnerability: {str(e)}")
            else:
                print(Fore.GREEN + f"[+] No vulnerability detected by model: {key}")
    return vulnerabilities

# Determine exploit method automatically based on prediction
def determine_exploit_method(prediction):
    if "XSS" in prediction:
        return "Reflected XSS"
    elif "SQL" in prediction:
        return "SQL Injection"
    elif "RCE" in prediction:
        return "Remote Code Execution"
    elif "LFI" in prediction:
        return "Local File Inclusion"
    elif "RFI" in prediction:
        return "Remote File Inclusion"
    else:
        return "Other"

# Capture screenshot using Selenium
def capture_screenshot(url, output_path, filename):
    print(Fore.CYAN + f"[*] Capturing screenshot for URL: {url}")
    try:
        options = webdriver.ChromeOptions()
        options.add_argument('--headless')
        driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
        driver.get(url)
        screenshot_path = os.path.join(output_path, filename)
        driver.save_screenshot(screenshot_path)
        driver.quit()
        print(Fore.GREEN + f"[+] Screenshot saved at: {screenshot_path}")
        return screenshot_path
    except Exception as e:
        logging.error(f"Error capturing screenshot: {str(e)}")
        print(Fore.RED + f"[-] Error capturing screenshot: {str(e)}")
        return None

# Generate annotated image for vulnerabilities
def capture_vulnerability_image(line, line_number, prediction, model_used, url, filename, output_path, exploit_result=None, request_details=None, response_details=None, severity=None, description=None, payload=None, exploit_method=None):
    print(Fore.CYAN + f"[*] Generating vulnerability image for line: {line_number}")
    try:
        image_width = 1200  # Increased width to accommodate more text
        image_height = 800  # Increased height to accommodate more text
        font_size = 16
        image = Image.new("RGB", (image_width, image_height), color="white")
        draw = ImageDraw.Draw(image)
        try:
            font = ImageFont.truetype("arial.ttf", font_size)
            bold_font = ImageFont.truetype("arial.ttf", font_size + 2)
        except IOError:
            font = ImageFont.load_default()
            bold_font = ImageFont.load_default()

        y = 10  # Starting y-coordinate for text

        # Draw the title
        draw.text((10, y), f"Vulnerability Found on Line {line_number}", fill="red", font=bold_font)
        y += 40

        # Draw the vulnerability details
        draw.text((10, y), f"Type: {prediction}", fill="blue", font=font)
        y += 30
        draw.text((10, y), f"Model Used: {model_used}", fill="green", font=font)
        y += 30
        draw.text((10, y), f"Severity: {severity}", fill="black", font=font)
        y += 30
        draw.text((10, y), f"Description: {description}", fill="black", font=font)
        y += 30
        draw.text((10, y), f"Payload: {payload}", fill="black", font=font)
        y += 30
        draw.text((10, y), f"Exploit Method: {exploit_method}", fill="black", font=font)
        y += 30
        draw.text((10, y), f"Code: {line}", fill="black", font=font)
        y += 30
        draw.text((10, y), "This code can be exploited! Please review the following details:", fill="red", font=font)
        y += 30
        draw.text((10, y), f"Proof of Concept: Exploitation Example for {prediction}", fill="black", font=font)
        y += 30
        draw.text((10, y), f"Direct Link: {url}#line-{line_number}", fill="black", font=font)
        y += 30

        if exploit_result:
            draw.text((10, y), f"Exploit Result: {exploit_result}", fill="black", font=font)
            y += 30
        if request_details:
            draw.text((10, y), f"Request Details: {json.dumps(request_details, indent=2)}", fill="black", font=font)
            y += 30
        if response_details:
            draw.text((10, y), f"Response Details: {json.dumps(response_details, indent=2)}", fill="black", font=font)
            y += 30

        # Save the image
        image.save(os.path.join(output_path, filename))
        print(Fore.GREEN + f"[+] Vulnerability image saved at: {os.path.join(output_path, filename)}")
    except Exception as e:
        logging.error(f"Error generating image: {str(e)}")
        print(Fore.RED + f"[-] Error generating image: {str(e)}")

# Subdomain Enumeration
def enumerate_subdomains(domain):
    print(Fore.CYAN + f"[*] Enumerating subdomains for domain: {domain}")
    subdomains = set()
    try:
        answers = dns.resolver.resolve(domain, 'NS')
        for rdata in answers:
            subdomains.add(str(rdata.target))
    except Exception as e:
        logging.error(f"Error enumerating subdomains: {str(e)}")
        print(Fore.RED + f"[-] Error enumerating subdomains: {str(e)}")
    return list(subdomains)

# Clean URL function to remove fragments and query parameters
def clean_url(url):
    parsed_url = urlparse(url)
    cleaned_url = urlunparse((parsed_url.scheme, parsed_url.netloc, parsed_url.path, '', '', ''))
    return cleaned_url

# Fetch all links from a given URL
def fetch_links(base_url, visited=set(), depth=0, max_depth=3):
    print(Fore.CYAN + f"[*] Fetching links from URL: {base_url} (Depth: {depth})")
    cleaned_base_url = clean_url(base_url)
    if depth > max_depth or cleaned_base_url in visited:
        return visited
    visited.add(cleaned_base_url)
    try:
        response = requests.get(base_url, headers=HEADERS, timeout=30)
        tree = html.fromstring(response.content)
        links = tree.xpath('//a/@href')
        filtered_links = [
            link for link in links
            if not link.startswith('#') and not link.startswith('javascript:') and not link.endswith('.pdf')
        ]
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = []
            for link in filtered_links:
                full_url = urljoin(base_url, link)
                cleaned_full_url = clean_url(full_url)
                if urlparse(cleaned_full_url).netloc == urlparse(cleaned_base_url).netloc:
                    futures.append(executor.submit(fetch_links, full_url, visited, depth + 1, max_depth))
            for future in as_completed(futures):
                future.result()
    except Exception as e:
        logging.error(f"Error fetching links from URL: {base_url} - {str(e)}")
        print(Fore.RED + f"[-] Error fetching links from URL: {base_url} - {str(e)}")
    return visited

# Convert CaseInsensitiveDict to regular dict
def convert_headers(headers):
    if headers is None:
        return None
    return dict(headers)

# Scan all links for vulnerabilities
def scan_links(links, output_path, vulnerabilities=[]):
    print(Fore.YELLOW + "[*] Scanning links for vulnerabilities...")
    business_logic_vulnerabilities = []  # New list for Business Logic Vulnerabilities
    for link in links:
        cleaned_link = clean_url(link)
        try:
            response = requests.get(link, headers=HEADERS, timeout=30)
            vulns = find_vulnerabilities(response.text, link)
            vulnerabilities.extend(vulns)
            # Check for Business Logic Vulnerabilities
            for vuln in vulns:
                if "business logic" in vuln["prediction"].lower() or "zero-day" in vuln["prediction"].lower() or "human error" in vuln["prediction"].lower():
                    business_logic_vulnerabilities.append(vuln)
        except Exception as e:
            logging.error(f"Error processing URL: {link} - {str(e)}")
            print(Fore.RED + f"[-] Error processing URL: {link} - {str(e)}")
        
        # Capture screenshot
        capture_screenshot(link, output_path, f"vuln_{hash(link)}.png")
        
        # Generate annotated image for vulnerabilities
        for vuln in vulns:
            capture_vulnerability_image(
                line=vuln['line_content'],
                line_number=vuln['line_number'],
                prediction=vuln['prediction'],
                model_used=vuln['model_used'],
                url=link,
                filename=f"annotated_vuln_{hash(link)}_{vuln['line_number']}.png",
                output_path=output_path,
                exploit_result=vuln.get('exploit_result'),
                request_details=vuln.get('request_details'),
                response_details=vuln.get('response_details'),
                severity=vuln.get('severity'),
                description=vuln.get('description'),
                payload=vuln.get('payload'),
                exploit_method=vuln.get('exploit_method')
            )
    
    # Save Business Logic Vulnerabilities to a separate JSON file
    if business_logic_vulnerabilities:
        save_business_logic_vulnerabilities(business_logic_vulnerabilities, os.path.join("business_logic", "business_logic_vulnerabilities.json"))
    
    return vulnerabilities

# Save Business Logic Vulnerabilities to JSON
def save_business_logic_vulnerabilities(vulnerabilities, filename):
    print(Fore.YELLOW + f"[*] Saving Business Logic Vulnerabilities to {filename}...")
    try:
        # Ensure directory exists
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        with open(filename, 'w', encoding='utf-8') as file:
            json.dump(vulnerabilities, file, indent=4, default=str)
        print(Fore.GREEN + f"[+] Business Logic Vulnerabilities saved to {filename}")
    except Exception as e:
        logging.error(f"Error saving Business Logic Vulnerabilities: {str(e)}")
        print(Fore.RED + f"[-] Error saving Business Logic Vulnerabilities: {str(e)}")

# Save scan state
def save_scan_state(vulnerabilities, output_path, links, current_link_index):
    state = {
        "vulnerabilities": vulnerabilities,
        "output_path": output_path,
        "links": list(links),
        "current_link_index": current_link_index
    }
    os.makedirs("scan_states", exist_ok=True)
    joblib.dump(state, os.path.join("scan_states", "scan_state.pkl"))
    print(Fore.GREEN + f"[+] Scan state saved. Last scanned link index: {current_link_index}")

# Load scan state
def load_scan_state():
    state_path = os.path.join("scan_states", "scan_state.pkl")
    if os.path.exists(state_path):
        state = joblib.load(state_path)
        # Ensure the index is within bounds
        if state["current_link_index"] >= len(state["links"]):
            state["current_link_index"] = len(state["links"]) - 1
        print(Fore.GREEN + f"[+] Scan state loaded. Resuming from link {state['current_link_index'] + 1}/{len(state['links'])}")
        return state
    else:
        print(Fore.RED + "[-] No saved scan state found.")
        return None

# Save vulnerabilities to JSON
def save_to_json(vulnerabilities, filename):
    print(Fore.YELLOW + f"[*] Saving vulnerabilities to {filename}...")
    try:
        # Ensure directory exists
        os.makedirs("reports", exist_ok=True)
        
        # Convert all objects to serializable format
        serializable_vulns = []
        for vuln in vulnerabilities:
            serializable_vuln = vuln.copy()
            # Convert headers to dict if they exist
            if 'request_details' in serializable_vuln:
                serializable_vuln['request_details'] = convert_headers(serializable_vuln['request_details'])
            if 'response_details' in serializable_vuln:
                serializable_vuln['response_details'] = convert_headers(serializable_vuln['response_details'])
            serializable_vulns.append(serializable_vuln)
        
        file_path = os.path.join("reports", filename)
        with open(file_path, 'w', encoding='utf-8') as file:
            json.dump(serializable_vulns, file, indent=4, default=str)
        print(Fore.GREEN + f"[+] Vulnerabilities saved to {file_path}")
    except Exception as e:
        logging.error(f"Error saving vulnerabilities: {str(e)}")
        print(Fore.RED + f"[-] Error saving vulnerabilities: {str(e)}")

# Generate PDF report
def generate_pdf_report(vulnerabilities, filename):
    print(Fore.YELLOW + f"[*] Generating PDF report: {filename}...")
    try:
        from reportlab.lib.pagesizes import letter
        from reportlab.pdfgen import canvas
        from reportlab.lib.utils import ImageReader
        
        # Ensure directory exists
        os.makedirs("reports", exist_ok=True)
        
        file_path = os.path.join("reports", filename)
        c = canvas.Canvas(file_path, pagesize=letter)
        c.drawString(100, 750, "Vulnerability Report")
        y = 730
        for vuln in vulnerabilities:
            c.drawString(100, y, f"Line {vuln['line_number']}: {vuln['line_content']}")
            y -= 20
            # Add screenshot if available
            if vuln.get('screenshot_path'):
                img = ImageReader(vuln['screenshot_path'])
                c.drawImage(img, 100, y - 100, width=400, height=200)
                y -= 220  # Adjust position for next item
            if y < 50:
                c.showPage()
                y = 750
        c.save()
        print(Fore.GREEN + f"[+] PDF report saved as {file_path}")
    except Exception as e:
        logging.error(f"Error generating PDF report: {str(e)}")
        print(Fore.RED + f"[-] Error generating PDF report: {str(e)}")

# Create output folders
def create_output_folders():
    folders = [
        "reports",
        "screenshots",
        "business_logic",
        "zero_day",
        "human_errors",
        "registration_panels",
        "logs",
        "scan_states"
    ]
    
    for folder in folders:
        os.makedirs(folder, exist_ok=True)

# Save full report
def save_full_report(vulnerabilities, filename="full_report.json"):
    print(Fore.YELLOW + f"[*] Saving full report to {filename}...")
    try:
        # Ensure directory exists
        os.makedirs("reports", exist_ok=True)
        
        file_path = os.path.join("reports", filename)
        # Convert all objects to serializable format
        serializable_vulns = []
        for vuln in vulnerabilities:
            serializable_vuln = vuln.copy()
            # Convert headers to dict if they exist
            if 'request_details' in serializable_vuln:
                serializable_vuln['request_details'] = convert_headers(serializable_vuln['request_details'])
            if 'response_details' in serializable_vuln:
                serializable_vuln['response_details'] = convert_headers(serializable_vuln['response_details'])
            serializable_vulns.append(serializable_vuln)
        
        with open(file_path, 'w', encoding='utf-8') as file:
            json.dump(serializable_vulns, file, indent=4, default=str)
        print(Fore.GREEN + f"[+] Full report saved to {file_path}")
    except Exception as e:
        logging.error(f"Error saving full report: {str(e)}")
        print(Fore.RED + f"[-] Error saving full report: {str(e)}")

# Main execution
if __name__ == "__main__":
    def start_scan(url, resume=False):
        create_output_folders()  # Create necessary folders
        output_path = "screenshots"
        vulnerabilities = []
        links = set()
        current_link_index = 0

        if resume:
            state = load_scan_state()
            if state:
                vulnerabilities = state["vulnerabilities"]
                output_path = state["output_path"]
                links = set(state["links"])
                current_link_index = state["current_link_index"]
                print(Fore.YELLOW + f"[*] Resuming scan from link {current_link_index + 1}/{len(links)}")
            else:
                print(Fore.RED + "[-] Cannot resume scan. Starting a new scan.")
                resume = False

        if not resume:
            print(Fore.YELLOW + "[*] Starting a new scan.")
            try:
                # Validate URL before making a request
                parsed_url = urlparse(url)
                if not parsed_url.scheme:
                    url = "https://" + url  # Add default scheme if missing

                # Step 1: Fetch all links if not resuming
                links = fetch_links(url)
                print(Fore.GREEN + f"[+] Found {len(links)} links.")
            except Exception as e:
                logging.error(f"Error during initial scan setup: {str(e)}")
                print(Fore.RED + f"[-] Error during initial scan setup: {str(e)}")
                return

        # Step 2: Scan links for vulnerabilities
        links_list = list(links)
        for i in range(current_link_index, len(links_list)):
            link = links_list[i]
            print(Fore.CYAN + f"[*] Scanning link {i + 1}/{len(links_list)}: {link}")
            try:
                response = requests.get(link, headers=HEADERS, timeout=30)
                vulns = find_vulnerabilities(response.text, link)
                vulnerabilities.extend(vulns)
            except Exception as e:
                logging.error(f"Error processing URL: {link} - {str(e)}")
                print(Fore.RED + f"[-] Error processing URL: {link} - {str(e)}")
            
            # Capture screenshot
            capture_screenshot(link, output_path, f"vuln_{hash(link)}.png")
            
            # Generate annotated image for vulnerabilities
            for vuln in vulns:
                capture_vulnerability_image(
                    line=vuln['line_content'],
                    line_number=vuln['line_number'],
                    prediction=vuln['prediction'],
                    model_used=vuln['model_used'],
                    url=link,
                    filename=f"annotated_vuln_{hash(link)}_{vuln['line_number']}.png",
                    output_path=output_path,
                    exploit_result=vuln.get('exploit_result'),
                    request_details=vuln.get('request_details'),
                    response_details=vuln.get('response_details'),
                    severity=vuln.get('severity'),
                    description=vuln.get('description'),
                    payload=vuln.get('payload'),
                    exploit_method=vuln.get('exploit_method')
                )
            
            # Save scan state after each link
            save_scan_state(vulnerabilities, output_path, links, i)

        # Step 3: Save results to JSON, PDF, and full report
        if vulnerabilities:
            save_to_json(vulnerabilities, "vulnerability_report1.json")
            generate_pdf_report(vulnerabilities, "vulnerability_report1.pdf")
            save_full_report(vulnerabilities)  # Save full report
            print(Fore.GREEN + f"[+] Reports saved. Screenshots are saved in '{output_path}'.")
        else:
            print(Fore.GREEN + "[+] No vulnerabilities or anomalies found.")

    def interact():
        while True:
            try:
                print("مرحبًا! أنا هنا لمساعدتك في فحص الثغرات الأمنية. هل تريد بدء فحص جديد (new) أو استئناف فحص سابق (resume)؟")
                user_input = input("Enter your response (new/resume): ").strip().lower()
                if user_input == "new":
                    url = input("Enter the URL to check: ").strip()
                    print(f"تم بدء الفحص للموقع {url}.")
                    start_scan(url)
                elif user_input == "resume":
                    url = input("Enter the URL to resume scanning: ").strip()
                    print(f"تم استئناف الفحص للموقع {url}.")
                    start_scan(url, resume=True)
                else:
                    print("عذراً، لم أفهم ما تقصد. هل يمكنك توضيح ذلك؟")
            except KeyboardInterrupt:
                print(Fore.YELLOW + "[*] Interaction interrupted.")
                break
            except Exception as e:
                logging.error(f"Error during interaction: {str(e)}")
                print(Fore.RED + f"[-] Error during interaction: {str(e)}")

    # Start interaction
    interact()