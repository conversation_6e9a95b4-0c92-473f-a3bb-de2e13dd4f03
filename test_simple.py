#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("🚀 بدء اختبار البرنامج...")

try:
    import os
    print("✅ تم استيراد os بنجاح")

    import sys
    print("✅ تم استيراد sys بنجاح")

    import json
    print("✅ تم استيراد json بنجاح")

    import time
    print("✅ تم استيراد time بنجاح")

    from urllib.parse import urlparse
    print("✅ تم استيراد urllib.parse بنجاح")

    print("\n🎉 جميع المكتبات الأساسية تعمل بشكل صحيح!")

    # محاكاة الزحف البسيط
    def simple_crawl_test(url):
        print(f"\n=== اختبار الزحف البسيط ===")
        print(f"🔍 فحص URL: {url}")

        if not url.startswith('http'):
            url = 'https://www.example.com'
            print(f"تم تعيين URL افتراضي: {url}")

        parsed = urlparse(url)
        print(f"النطاق: {parsed.netloc}")
        print(f"المسار: {parsed.path}")

        # محاكاة العثور على روابط
        mock_links = [
            f"{url}/page1",
            f"{url}/page2",
            f"{url}/about",
            f"{url}/contact"
        ]

        print(f"تم العثور على {len(mock_links)} رابط:")
        for link in mock_links:
            print(f"  - {link}")

        return mock_links

    # اختبار دالة بسيطة
    def test_main():
        print("\n=== اختبار القائمة الرئيسية ===")
        print("مرحباً بك في أداة فحص أمان الويب المتقدمة")
        print("الرجاء اختيار أحد الخيارات التالية:")
        print("[1] بدء فحص جديد (إعادة تعيين البيئة)")
        print("[2] استئناف الفحص السابق (المتابعة من آخر رابط)")
        print("[3] فحص متقدم (Business Logic & Zero-Day)")
        print("[4] الخروج من البرنامج")

        choice = input("اختيارك: ")
        print(f"تم اختيار: {choice}")

        if choice == "1":
            print("✅ تم اختيار بدء فحص جديد")
            url = input("أدخل عنوان URL للفحص: ")
            print(f"✅ تم إدخال URL: {url}")

            # اختبار الزحف
            links = simple_crawl_test(url)

            # محاكاة حفظ النتائج
            os.makedirs('test_results', exist_ok=True)
            with open('test_results/links.json', 'w', encoding='utf-8') as f:
                json.dump(links, f, ensure_ascii=False, indent=2)
            print(f"✅ تم حفظ النتائج في test_results/links.json")

            # محاكاة مراحل الفحص
            print("\n=== بدء مراحل الفحص ===")
            print("🔄 المرحلة 1: الفحص التقليدي")
            time.sleep(1)
            print("✅ انتهت المرحلة 1")

            print("🔄 المرحلة 2: الفحص المتقدم")
            time.sleep(1)
            print("✅ انتهت المرحلة 2")

            print("🔄 المرحلة 3: الفحص السلوكي")
            time.sleep(1)
            print("✅ انتهت المرحلة 3")

            print("🔄 المرحلة 4: إنشاء التقارير")
            time.sleep(1)
            print("✅ انتهت المرحلة 4")

            print("\n🎉 تم إكمال جميع مراحل الفحص بنجاح!")

        elif choice == "2":
            print("✅ تم اختيار استئناف الفحص السابق")
            if os.path.exists('test_results/links.json'):
                with open('test_results/links.json', 'r', encoding='utf-8') as f:
                    links = json.load(f)
                print(f"تم تحميل {len(links)} رابط من الفحص السابق")
            else:
                print("لم يتم العثور على نتائج سابقة")

        elif choice == "3":
            print("✅ تم اختيار الفحص المتقدم")
            url = input("أدخل عنوان URL للفحص المتقدم: ")
            print(f"بدء الفحص المتقدم لـ {url}")

        elif choice == "4":
            print("✅ تم اختيار الخروج")
            print("شكراً لاستخدامك أداة فحص أمان الويب")
        else:
            print("⚠️ اختيار غير صالح")

    # تشغيل الاختبار
    test_main()

except ImportError as e:
    print(f"❌ خطأ في استيراد المكتبة: {str(e)}")
except Exception as e:
    print(f"❌ خطأ عام: {str(e)}")
    import traceback
    traceback.print_exc()
