
يرجى تعديل كود أداة الفحص الأمني وفقًا للتفاصيل التالية، مع الأخذ بعين الاعتبار توضيح المهام والمشكلات المطلوبة وتوزيعها بطريقة منظمة تجعل التنفيذ دقيقًا وسلسًا:

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📌 أولًا: إزالة النماذج غير المستخدمة بالكامل من الكود
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
❌ النماذج التالية يجب حذفها **حذفًا نهائيًا من الكود** (وليس فقط من الفحص المتقدم):

   - `business_logic_model`
   - `registration_model`
   - `human_error_model`
   - `zero_day_model`

✅ أي استخدام لهذه النماذج في أي مكان من الكود سواء في الاستدعاء أو التهيئة أو التنبؤ يجب أن يُزال تمامًا.

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📌 ثانيًا: تنظيم الفحص التقليدي (Traditional Scan)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🔹 يعتمد على **نماذج ML** مدربة سابقًا.
🔹 لكل رابط يتم اختباره عبر نموذج واحد أو أكثر بحسب عدد الـ features.
🔹 يجب:
   - التأكد من توافق `input_data` مع توقعات النموذج (عدد الخصائص).
   - في حال التعارض، تجاوز النموذج مع طباعة تنبيه.
   - الحصول على التوقع (نوع الثغرة المحتمل).
   - تنفيذ **اختبار عملي حقيقي** باستخدام payload فعلي لتأكيد الثغرة.

📸 **التقاط صورة الشاشة عند تأكيد الثغرة**:
   - يتم حفظ صورة الشاشة لحالة الموقع بعد إدخال الـ payload.
   - يُظهر فيها التأثير الفعلي أو الاستجابة.
   - تُحفظ الصورة داخل مجلد:
     ```
     reports/traditional/[نوع_الثغرة]/[اسم_الملف].png
     ```
   - اسم الصورة يكون بالشكل:
     ```
     traditional_XSS_impact_001.png
     ```

📄 **تسجيل تقرير نصي لكل نتيجة**:
   - يتم تسجيل تقرير نصي لكل رابط في ملف:
     ```
     reports/traditional/[نوع_الثغرة]/report.txt
     ```
   - يحتوي التقرير على:
     - الرابط.
     - النموذج المستخدم.
     - نوع الثغرة المتوقع.
     - الـ payload المُستخدم.
     - نتيجة التأثير (تم التأكيد أو لا).
     - اسم الصورة المرافقة.

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📌 ثالثًا: تنظيم الفحص المتقدم (Advanced Scan)
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🔹 لا يعتمد على أي نموذج.
🔹 يعتمد فقط على **تحليل حي وديناميكي للاستجابات**.

الخطوات:
1. إرسال عدة payloads خاصة بثغرات مختلفة (XSS, SQLi, CSRF...).
2. تحليل النتائج (الانعكاس، الخطأ، التفاعل).
3. عند وجود تأثير يدل على وجود ثغرة:
   - يتم تأكيدها.
   - التقاط صورة للشاشة.
   - حفظ الصورة في:
     ```
     reports/advanced/[نوع_الثغرة]/[اسم_الملف].png
     ```
   - مثال للاسم:
     ```
     advanced_SQLi_impact_002.png
     ```

📄 **تقرير نصي بنفس الطريقة السابقة**:
   - يحتوي على:
     - الرابط.
     - الـ payload المُستخدم.
     - نوع الثغرة.
     - حالة التأكيد.
     - اسم الصورة المرافقة.

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📌 رابعًا: عرض النتائج أثناء الفحص بشكل حي
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
كل عملية فحص تظهر في الشاشة بهذا الشكل:

[] URL: https://example.com/login.php
[] Phase: Traditional Scan
[+] Model: classified_model predicted → SQL Injection
[+] Payload Used: ' OR '1'='1
[+] Vulnerability Confirmed: ✅
[+] Screenshot Saved: traditional_SQLi_impact_001.png

نسخ
تحرير

أو للفحص المتقدم:

[] URL: https://target.com/profile?id=5
[] Phase: Advanced Scan
[+] Payload Used: <script>alert(1)</script>
[+] Reflected → XSS confirmed
[+] Screenshot Saved: advanced_XSS_impact_002.png

markdown
نسخ
تحرير

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
📌 خامسًا: تنظيم المجلدات والتقارير
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
- جميع التقارير يتم حفظها ضمن مجلد `reports/` ويتم تقسيمه إلى:
reports/
├── traditional/
│ ├── XSS/
│ │ ├── traditional_XSS_impact_001.png
│ │ └── report.txt
│ └── SQLi/
│ ├── traditional_SQLi_impact_002.png
│ └── report.txt
└── advanced/
├── XSS/
│ ├── advanced_XSS_impact_001.png
│ └── report.txt
└── CSRF/
├── advanced_CSRF_impact_001.png
└── report.txt

diff
نسخ
تحرير

- الصور محفوظة بالأسماء الدالة (اسم الفحص، نوع الثغرة، رقم تسلسلي).
- التقارير يجب أن تكون واضحة، ومنظمة لكل رابط وثغرة.

━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
🎯 الهدف النهائي:
━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━
تكون الأداة دقيقة، عملية، منظمة، وتُنتج تقارير شاملة لكل اختبار، تشمل:
- الرابط المستهدف.
- نوع الفحص.
- نوع الثغرة.
- الـ payload المستخدم.
- النتيجة والتأكيد.
- صورة التأثير.

يرجى تنفيذ التعديلات بكل دقة، مع مراعاة قابلية التوسع والتنظيم السليم للملفات والنتائج.
