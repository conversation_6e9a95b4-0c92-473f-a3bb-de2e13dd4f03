import os
import joblib
import requests
from lxml import html
from urllib.parse import urljoin, urlparse, urlunparse, parse_qs
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Embedding, LSTM, Dense, Conv1D, MaxPooling1D, Flatten
from tensorflow.keras.preprocessing.text import Tokenizer
from tensorflow.keras.preprocessing.sequence import pad_sequences
from sklearn.feature_extraction.text import TfidfVectorizer
from nltk.tokenize import word_tokenize
from nltk.stem import WordNetLemmatizer
from PIL import Image, ImageDraw, ImageFont, ImageChops, ImageEnhance
from concurrent.futures import ThreadPoolExecutor, as_completed, ProcessPoolExecutor
import logging
import tensorflow as tf
import time
import unittest
from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
from colorama import Fore, Style, init
from stable_baselines3 import PPO
import gym
from gym import spaces
from sklearn.ensemble import IsolationForest
import subprocess
import dns.resolver
import shap
import torch
import networkx as nx
import pytorch_lightning as pl
from alibi.explainers import Counterfactual
import seaborn as sns
import matplotlib.pyplot as plt
from art.attacks.evasion import FastGradientMethod
from art.estimators.classification import TensorFlowV2Classifier
import h5py
from pennylane import numpy as np
import pennylane as qml
from transformers import BertTokenizer, BertForSequenceClassification, pipeline
from gtts import gTTS
import speech_recognition as sr
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import gradio as gr
import tempfile
import brian2
from sklearn.svm import OneClassSVM
from stable_baselines3.common.env_util import make_vec_env
from stable.baselines3.common.vec_env import DummyVecEnv
from stable_baselines3.common.callbacks import BaseCallback
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common import results_plotter
from stable_baselines3.common.results_plotter import load_results, ts2xy
import ast
import astor
import json
import numpy as np
from transformers import Trainer, TrainingArguments
from datasets import Dataset
import optuna
from googletrans import Translator
from functools import lru_cache
import redis.asyncio as redis
import dask
import ray
from dask.distributed import Client
from ray import tune
from ray.tune.schedulers import ASHAScheduler
from ray.tune.integration.keras import TuneReportCallback
from multiprocessing import cpu_count
from requests.adapters import HTTPAdapter
from requests.packages.urllib3.util.retry import Retry
from cachetools import cached, TTLCache
import brotli
import zlib
import difflib
from bs4 import BeautifulSoup
from skimage.metrics import structural_similarity as ssim
import cv2
import pytesseract
from datetime import datetime
from faker import Faker
from selenium.webdriver.common.action_chains import ActionChains
from selenium.webdriver.common.keys import Keys
import pandas as pd
from tqdm import tqdm
from urllib3.exceptions import InsecureRequestWarning
from requests.packages.urllib3 import disable_warnings
import re

# تعطيل تحذيرات SSL
disable_warnings(InsecureRequestWarning)

# تهيئة Faker لإنشاء بيانات وهمية
fake = Faker()

# تحسين إعدادات البيئة
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
os.environ['OMP_NUM_THREADS'] = str(cpu_count())
os.environ['TF_NUM_INTEROP_THREADS'] = str(cpu_count())
os.environ['TF_NUM_INTRAOP_THREADS'] = str(cpu_count())
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

# تهيئة colorama
init(autoreset=True)

# تعطيل تحذيرات TensorFlow
tf.get_logger().setLevel('ERROR')
tf.autograph.set_verbosity(0)

# إعدادات التسجيل
logging.basicConfig(
    filename='vulnerability_scanner.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filemode='w'
)
logger = logging.getLogger(__name__)

# إعدادات جلسة HTTP مع إعادة المحاولة والضغط
session = requests.Session()
retry = Retry(
    total=5,
    backoff_factor=0.1,
    status_forcelist=[500, 502, 503, 504]
)
adapter = HTTPAdapter(
    max_retries=retry,
    pool_connections=100,
    pool_maxsize=100
)
session.mount('http://', adapter)
session.mount('https://', adapter)

# إعدادات التخزين المؤقت
cache = TTLCache(maxsize=2048, ttl=3600)

# عناوين HTTP المحسنة
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Accept-Encoding": "gzip, deflate, br",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1"
}

# مسارات شائعة للفحص مع إضافات جديدة
COMMON_PATHS = [
    "/admin", "/login", "/wp-admin", "/config", "/backup", "/api", "/test", "/secret",
    "/robots.txt", "/.git", "/.env", "/.htaccess", "/.htpasswd", "/phpinfo.php",
    "/register", "/signup", "/password-reset", "/profile", "/dashboard", "/console",
    "/debug", "/management", "/phpMyAdmin", "/dbadmin", "/administrator", "/.well-known",
    "/beta", "/dev", "/internal", "/staging", "/sandbox", "/payment", "/checkout",
    "/transaction", "/order", "/cart", "/account", "/settings", "/admin-console"
]

# ثغرات منطق العمل المتقدمة
ADVANCED_BUSINESS_LOGIC_CHECKS = [
    "price_manipulation", "auth_bypass", "condition_bypass", "role_escalation",
    "workflow_bypass", "payment_skip", "parameter_tampering", "idor"
]

# اختبارات التسجيل المتقدمة
ADVANCED_REGISTRATION_CHECKS = [
    "duplicate_email", "weak_password_policy", "missing_captcha",
    "email_injection", "username_enumeration", "account_takeover"
]

# ثغرات غير تقليدية
NON_TRADITIONAL_VULNS = [
    "cors_misconfig", "bola", "ssrf", "business_logic_injection",
    "user_impersonation", "privilege_escalation", "idor"
]

# تكوين Redis للتخزين المؤقت
try:
    redis_client = redis.Redis(
        host='localhost',
        port=6379,
        db=0,
        decode_responses=True
    )
    redis_client.ping()  # Test connection
except redis.ConnectionError:
    redis_client = None
    logger.warning("Redis connection failed, falling back to in-memory cache")

# وظائف فحص ثغرات منطق العمل المتقدمة
def check_business_logic_vulns(url, driver):
    """
    فحص ثغرات منطق العمل المتقدمة مثل:
    - تغيير الأسعار عبر API
    - تجاوز المصادقة
    - تخطي الشروط
    - تعديل role_id أو user_type
    """
    results = []
    
    # اختبار تغيير الأسعار
    price_tamper_test = {
        'test_name': 'price_manipulation',
        'payloads': ['price=0', 'price=-1', 'discount=100']
    }
    
    # اختبار تجاوز المصادقة
    auth_bypass_test = {
        'test_name': 'auth_bypass',
        'payloads': ['admin=true', 'authenticated=1', 'is_admin=yes']
    }
    
    # اختبار تعديل الصلاحيات
    role_escalation_test = {
        'test_name': 'role_escalation',
        'payloads': ['role_id=1', 'user_type=admin', 'privilege=superuser']
    }
    
    # تنفيذ الاختبارات
    for test in [price_tamper_test, auth_bypass_test, role_escalation_test]:
        for payload in test['payloads']:
            test_url = f"{url}?{payload}" if "?" not in url else f"{url}&{payload}"
            try:
                driver.get(test_url)
                screenshot = take_screenshot(driver, f"{test['test_name']}_{payload}")
                results.append({
                    'test_name': test['test_name'],
                    'payload': payload,
                    'result': 'نجاح' if 'error' not in driver.page_source.lower() else 'فشل',
                    'screenshot': screenshot
                })
            except Exception as e:
                logger.error(f"فشل اختبار {test['test_name']}: {str(e)}")
                results.append({
                    'test_name': test['test_name'],
                    'payload': payload,
                    'result': 'فشل',
                    'error': str(e)
                })
    
    return results

# وظائف فحص تجاوز سير العمل
def check_workflow_bypass(url, driver):
    """
    فحص تجاوز سير العمل مثل:
    - الوصول المباشر إلى endpoints
    - تخطي مراحل الدفع
    """
    results = []
    
    # اختبار الوصول المباشر
    direct_access_test = {
        'test_name': 'direct_access',
        'endpoints': ['/admin', '/dashboard', '/payment/success']
    }
    
    # اختبار تخطي مراحل الدفع
    payment_skip_test = {
        'test_name': 'payment_skip',
        'endpoints': ['/checkout/success', '/order/complete']
    }
    
    # تنفيذ الاختبارات
    for test in [direct_access_test, payment_skip_test]:
        for endpoint in test['endpoints']:
            test_url = f"{url}{endpoint}"
            try:
                driver.get(test_url)
                screenshot = take_screenshot(driver, f"{test['test_name']}_{endpoint}")
                results.append({
                    'test_name': test['test_name'],
                    'endpoint': endpoint,
                    'result': 'نجاح' if 'error' not in driver.page_source.lower() else 'فشل',
                    'screenshot': screenshot
                })
            except Exception as e:
                logger.error(f"فشل اختبار {test['test_name']}: {str(e)}")
                results.append({
                    'test_name': test['test_name'],
                    'endpoint': endpoint,
                    'result': 'فشل',
                    'error': str(e)
                })
    
    return results

# وظائف فحص اختبارات التسجيل
def check_registration_vulns(url, driver):
    """
    فحص ثغرات التسجيل مثل:
    - CAPTCHA غير محمية
    - إرسال نفس البريد مرتين
    - كلمات مرور ضعيفة
    """
    results = []
    
    # اختبار CAPTCHA
    captcha_test = {
        'test_name': 'captcha_bypass',
        'payloads': ['captcha=1234', 'captcha=bypass', 'g-recaptcha-response=test']
    }
    
    # اختبار البريد المكرر
    duplicate_email_test = {
        'test_name': 'duplicate_email',
        'payloads': ['email=<EMAIL>', 'email=<EMAIL>']
    }
    
    # اختبار كلمات المرور الضعيفة
    weak_password_test = {
        'test_name': 'weak_password',
        'payloads': ['password=123456', 'password=password', 'password=qwerty']
    }
    
    # تنفيذ الاختبارات
    for test in [captcha_test, duplicate_email_test, weak_password_test]:
        for payload in test['payloads']:
            test_url = f"{url}register?{payload}" if "?" not in url else f"{url}register&{payload}"
            try:
                driver.get(test_url)
                screenshot = take_screenshot(driver, f"{test['test_name']}_{payload}")
                results.append({
                    'test_name': test['test_name'],
                    'payload': payload,
                    'result': 'نجاح' if 'error' not in driver.page_source.lower() else 'فشل',
                    'screenshot': screenshot
                })
            except Exception as e:
                logger.error(f"فشل اختبار {test['test_name']}: {str(e)}")
                results.append({
                    'test_name': test['test_name'],
                    'payload': payload,
                    'result': 'فشل',
                    'error': str(e)
                })
    
    return results

# وظائف فحص الثغرات غير التقليدية
def check_non_traditional_vulns(url, driver):
    """
    فحص الثغرات غير التقليدية مثل:
    - CORS misconfiguration
    - Broken Object Level Authorization (BOLA)
    - Server-Side Business Flaws
    """
    results = []
    
    # اختبار CORS misconfiguration
    cors_test = {
        'test_name': 'cors_misconfig',
        'headers': {
            'Origin': 'https://attacker.com',
            'Access-Control-Request-Method': 'GET',
            'Access-Control-Request-Headers': 'X-Requested-With'
        }
    }
    
    # اختبار BOLA
    bola_test = {
        'test_name': 'bola',
        'endpoints': ['/api/user/1', '/api/order/123', '/api/admin/1']
    }
    
    # تنفيذ اختبار CORS
    try:
        response = requests.options(url, headers=cors_test['headers'])
        if 'Access-Control-Allow-Origin' in response.headers and response.headers['Access-Control-Allow-Origin'] == '*':
            results.append({
                'test_name': cors_test['test_name'],
                'result': 'نجاح',
                'vulnerability': 'CORS misconfiguration found - allows any origin'
            })
    except Exception as e:
        logger.error(f"فشل اختبار CORS: {str(e)}")
    
    # تنفيذ اختبار BOLA
    for endpoint in bola_test['endpoints']:
        test_url = f"{url}{endpoint}"
        try:
            driver.get(test_url)
            screenshot = take_screenshot(driver, f"{bola_test['test_name']}_{endpoint}")
            results.append({
                'test_name': bola_test['test_name'],
                'endpoint': endpoint,
                'result': 'نجاح' if 'error' not in driver.page_source.lower() else 'فشل',
                'screenshot': screenshot
            })
        except Exception as e:
            logger.error(f"فشل اختبار BOLA: {str(e)}")
    
    return results

# تحسين تحميل النماذج مع التخزين المؤقت المتعدد المستويات
@cached(cache)
@lru_cache(maxsize=128)
def load_model(file_name):
    logger.info(f"Loading model: {file_name}")
    print(Fore.YELLOW + f"[*] Loading model: {file_name}")
    try:
        # تحسين تحميل النماذج الكبيرة
        if os.path.getsize(file_name) > 100 * 1024 * 1024:  # للنماذج الأكبر من 100MB
            with open(file_name, 'rb') as f:
                if file_name.endswith('.h5'):
                    model = tf.keras.models.load_model(f)
                else:
                    model = joblib.load(f)
        else:
            if file_name.endswith('.h5'):
                model = tf.keras.models.load_model(file_name)
            else:
                model = joblib.load(file_name)
        return model
    except Exception as e:
        logger.error(f"Error loading model {file_name}: {str(e)}")
        print(Fore.RED + f"[-] Error loading model: {str(e)}")
        return None

# تحميل جميع النماذج والمتجهات مع التخزين المؤقت
models_and_vectorizers = {
    "vectorizer": load_model('exploitdb_vectorizer_full.pkl'),
    "model": load_model('exploitdb_model_full.pkl'),
    "model0": load_model('vuln_model.pk1'),
    "vectorizer0": load_model('Vectorizer.pk1'),
    "model01": load_model('vuln_model0.pk1'),
    "vectorizer01": load_model('Vectorizer0.pk1'),
    "model5": load_model('vuln_model5.pk1'),
    "vectorizer5": load_model('Vectorizer5.pk1'),
    "model00": load_model('vuln_model00.pk1'),
    "vectorizer00": load_model('Vectorizer00.pk1'),
    "classified_model": load_model('classified_vulnerabilities_model.pkl'),
    "classified_vectorizer": load_model('classified_vulnerabilities_vectorizer.pkl'),
    "classified_model99": load_model('classified_vulnerabilities_model99.pkl'),
    "classified_vectorizer99": load_model('classified_vulnerabilities_vectorizer99.pkl'),
    "final_dataset00_model": load_model('final_dataset00.pkl'),
    "final_dataset00_vectorizer": load_model('final_dataset00_Vectorizer1.pkl'),
    "github_payloads2_model": load_model('github_payloads2.pkl'),
    "github_payloads2_vectorizer": load_model('github_payloads2_Vectorizer1.pkl'),
    "github_payloads1_model": load_model('github_payloads1.pkl'),
    "github_payloads1_vectorizer": load_model('github_payloads1_Vectorizer1.pkl')
}

# معالجة النص مع التخزين المؤقت المتعدد المستويات
@cached(cache)
@lru_cache(maxsize=1024)
def process_text(content):
    logger.info("Processing text for vulnerabilities")
    print(Fore.CYAN + "[*] Processing text for vulnerabilities...")
    try:
        tokens = word_tokenize(content.lower())
        lemmatizer = WordNetLemmatizer()
        lemmas = [lemmatizer.lemmatize(token) for token in tokens if token.isalpha()]
        return " ".join(lemmas)
    except Exception as e:
        logger.error(f"Error processing text: {str(e)}")
        return content

# تحسين تحميل نموذج LSTM
@cached(cache)
@lru_cache(maxsize=1)
def load_lstm_model():
    logger.info("Loading LSTM model")
    print(Fore.YELLOW + "[*] Loading LSTM model...")
    try:
        model = Sequential()
        model.add(Embedding(input_dim=5000, output_dim=64))
        model.add(LSTM(64))
        model.add(Dense(1, activation='sigmoid'))
        model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])
        return model
    except Exception as e:
        logger.error(f"Error loading LSTM model: {str(e)}")
        return None

# تحسين تحميل نموذج CNN
@cached(cache)
@lru_cache(maxsize=1)
def load_cnn_model():
    logger.info("Loading CNN model")
    print(Fore.YELLOW + "[*] Loading CNN model...")
    try:
        model = Sequential()
        model.add(Embedding(input_dim=5000, output_dim=64))
        model.add(Conv1D(filters=64, kernel_size=3, activation='relu'))
        model.add(MaxPooling1D(pool_size=2))
        model.add(Flatten())
        model.add(Dense(1, activation='sigmoid'))
        model.compile(optimizer='adam', loss='binary_crossentropy', metrics=['accuracy'])
        return model
    except Exception as e:
        logger.error(f"Error loading CNN model: {str(e)}")
        return None

# تحسين ضبط نموذج BERT
def fine_tune_bert_model(train_texts, train_labels):
    logger.info("Fine-tuning BERT model")
    print(Fore.YELLOW + "[*] Fine-tuning BERT model on custom data with Data Augmentation and Hyperparameter Tuning...")
    
    try:
        tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')
        model = BertForSequenceClassification.from_pretrained('bert-base-uncased')
        
        # تحسين الترجمة العكسية
        def back_translation(texts):
            translator = Translator()
            augmented_texts = []
            with ThreadPoolExecutor(max_workers=4) as executor:
                futures = []
                for text in texts:
                    futures.append(executor.submit(
                        lambda t: translator.translate(
                            translator.translate(t, src='en', dest='fr').text,
                            src='fr', dest='en'
                        ).text,
                        text
                    ))
                for future in as_completed(futures):
                    try:
                        augmented_texts.append(future.result())
                    except:
                        augmented_texts.append(text)
            return augmented_texts

        augmented_texts = back_translation(train_texts)

        # تحضير مجموعة البيانات
        train_encodings = tokenizer(
            augmented_texts,
            truncation=True,
            padding=True,
            max_length=128,
            return_tensors="pt"
        )
        
        train_dataset = Dataset.from_dict({
            'input_ids': train_encodings['input_ids'].tolist(),
            'attention_mask': train_encodings['attention_mask'].tolist(),
            'labels': train_labels
        })

        # تحسين معايير التدريب
        def objective(trial):
            lr = trial.suggest_float("lr", 1e-5, 1e-3, log=True)
            batch_size = trial.suggest_categorical("batch_size", [8, 16, 32])
            num_epochs = trial.suggest_int("num_epochs", 1, 5)
            
            args = TrainingArguments(
                output_dir='./results',
                num_train_epochs=num_epochs,
                per_device_train_batch_size=batch_size,
                learning_rate=lr,
                save_steps=10_000,
                save_total_limit=2,
                logging_dir='./logs',
                logging_steps=500,
                evaluation_strategy="steps",
                eval_steps=500,
                warmup_steps=500,
                weight_decay=0.01,
                fp16=True,
                gradient_accumulation_steps=2
            )
            
            trainer = Trainer(
                model=model,
                args=args,
                train_dataset=train_dataset,
            )
            
            trainer.train()
            return trainer.evaluate()

        study = optuna.create_study(direction="maximize")
        study.optimize(objective, n_trials=10, timeout=3600)
        best_params = study.best_params
        logger.info(f"Best Hyperparameters: {best_params}")
        print(Fore.GREEN + f"[+] Best Hyperparameters: {best_params}")

        # التدريب باستخدام أفضل المعايير
        final_args = TrainingArguments(
            output_dir='./results',
            num_train_epochs=best_params.get('num_epochs', 3),
            per_device_train_batch_size=best_params.get('batch_size', 16),
            learning_rate=best_params.get('lr', 2e-5),
            save_steps=10_000,
            save_total_limit=2,
            logging_dir='./logs',
            logging_steps=500,
            evaluation_strategy="steps",
            eval_steps=500,
            warmup_steps=500,
            weight_decay=0.01,
            fp16=True,
            gradient_accumulation_steps=2
        )
        
        final_trainer = Trainer(
            model=model,
            args=final_args,
            train_dataset=train_dataset,
        )
        
        final_trainer.train()
        return model
    except Exception as e:
        logger.error(f"Error fine-tuning BERT model: {str(e)}")
        return None

# تحسين الشبكات العصبية الكمومية
def quantum_neural_network_example():
    logger.info("Running Quantum Neural Network example")
    print(Fore.YELLOW + "[*] Running Quantum Neural Network example with PennyLane...")
    
    try:
        dev = qml.device("default.qubit", wires=2)

        @qml.qnode(dev)
        def circuit(inputs, weights):
            qml.RX(inputs[0], wires=0)
            qml.RY(inputs[1], wires=1)
            qml.CNOT(wires=[0, 1])
            qml.Rot(*weights[0], wires=0)
            qml.Rot(*weights[1], wires=1)
            qml.CNOT(wires=[0, 1])
            return [qml.expval(qml.PauliZ(i)) for i in range(2)]

        inputs = np.array([0.54, 0.12])
        weights = np.random.rand(2, 3)
        result = circuit(inputs, weights)
        logger.info(f"Quantum Neural Network Result: {result}")
        print(Fore.GREEN + f"[+] Quantum Neural Network Result: {result}")
        return result
    except Exception as e:
        logger.error(f"Error in quantum neural network: {str(e)}")
        return None

# تحسين التفسيرات المضادة للواقع
def generate_counterfactual_explanation(model, input_data):
    logger.info("Generating Counterfactual Explanation")
    print(Fore.YELLOW + "[*] Generating Counterfactual Explanation using Alibi...")
    
    try:
        cf = Counterfactual(model, shape=(1, len(input_data)))
        explanation = cf.explain(input_data)
        if explanation.cf is not None:
            logger.info(f"Counterfactual Explanation: {explanation.cf}")
            print(Fore.GREEN + f"[+] Counterfactual Explanation: {explanation.cf}")
            return explanation.cf
        else:
            logger.warning("No Counterfactual Explanation found")
            print(Fore.RED + "[-] No Counterfactual Explanation found.")
            return None
    except Exception as e:
        logger.error(f"Error generating counterfactual: {str(e)}")
        return None

# تحسين تحليل الشبكات العصبية البيانية
def graph_neural_network_analysis(vulnerabilities):
    logger.info("Analyzing vulnerabilities using GNNs")
    print(Fore.YELLOW + "[*] Analyzing vulnerabilities using Graph Neural Networks (GNNs)...")
    
    try:
        G = nx.Graph()
        for vuln in vulnerabilities:
            G.add_node(vuln["line_number"], content=vuln["line_content"], prediction=vuln["prediction"])
        
        for i in range(len(vulnerabilities) - 1):
            G.add_edge(vulnerabilities[i]["line_number"], vulnerabilities[i + 1]["line_number"])
        
        adjacency_matrix = nx.to_numpy_array(G)
        logger.info(f"GNN Analysis Completed. Adjacency Matrix: {adjacency_matrix}")
        print(Fore.GREEN + f"[+] Graph Neural Network Analysis Completed. Adjacency Matrix: {adjacency_matrix}")
        return adjacency_matrix
    except Exception as e:
        logger.error(f"Error in GNN analysis: {str(e)}")
        return None

# تحسين التعلم المعزز للاستغلال
def reinforcement_learning_exploitation():
    logger.info("Running Reinforcement Learning for Exploitation")
    print(Fore.YELLOW + "[*] Running Reinforcement Learning for Exploitation...")
    
    try:
        env = make_vec_env(lambda: gym.make('CartPole-v1'), n_envs=4)
        model = PPO('MlpPolicy', env, verbose=1, device='auto')
        model.learn(total_timesteps=10000, progress_bar=True)
        logger.info("Reinforcement Learning for Exploitation Completed")
        print(Fore.GREEN + "[+] Reinforcement Learning for Exploitation Completed.")
        return model
    except Exception as e:
        logger.error(f"Error in reinforcement learning: {str(e)}")
        return None

# تحسين المعالجة المتوازية
def parallel_processing_example(tasks):
    logger.info("Running Parallel Processing example")
    print(Fore.YELLOW + "[*] Running Parallel Processing example...")
    
    try:
        results = []
        with ProcessPoolExecutor(max_workers=cpu_count()) as executor:
            futures = [executor.submit(task) for task in tasks]
            for future in as_completed(futures):
                try:
                    result = future.result()
                    results.append(result)
                    logger.info(f"Parallel Processing Result: {result}")
                    print(Fore.GREEN + f"[+] Parallel Processing Result: {result}")
                except Exception as e:
                    logger.error(f"Error in parallel task: {str(e)}")
        return results
    except Exception as e:
        logger.error(f"Error in parallel processing: {str(e)}")
        return None

# تحسين آليات التخزين المؤقت
def caching_mechanism_example():
    logger.info("Running Caching Mechanism example")
    print(Fore.YELLOW + "[*] Running Caching Mechanism example...")
    
    try:
        @lru_cache(maxsize=1024)
        def expensive_function(x):
            return x * x

        result = expensive_function(10)
        logger.info(f"Caching Mechanism Result: {result}")
        print(Fore.GREEN + f"[+] Caching Mechanism Result: {result}")
        return result
    except Exception as e:
        logger.error(f"Error in caching mechanism: {str(e)}")
        return None

# تحسين اكتشاف ثغرات منطق الأعمال
def detect_business_logic_vulnerabilities(vulnerabilities):
    logger.info("Detecting Business Logic Vulnerabilities")
    print(Fore.YELLOW + "[*] Detecting Business Logic Vulnerabilities using Custom Heuristics...")
    
    try:
        business_logic_vulnerabilities = [
            vuln for vuln in vulnerabilities
            if any(keyword in vuln["prediction"].lower()
                for keyword in ["business logic", "zero-day", "human error"])
        ]
        
        logger.info(f"Detected {len(business_logic_vulnerabilities)} Business Logic Vulnerabilities")
        print(Fore.GREEN + f"[+] Detected {len(business_logic_vulnerabilities)} Business Logic Vulnerabilities.")
        return business_logic_vulnerabilities
    except Exception as e:
        logger.error(f"Error detecting business logic vulnerabilities: {str(e)}")
        return []

# تحسين أنظمة التدخل البشري
def human_in_the_loop_system(vulnerabilities):
    logger.info("Running Human-in-the-Loop System")
    print(Fore.YELLOW + "[*] Running Human-in-the-Loop System for Complex Vulnerability Detection...")
    
    try:
        confirmed_vulnerabilities = []
        for vuln in vulnerabilities:
            print(f"Review the following vulnerability: {vuln['line_content']} (Prediction: {vuln['prediction']})")
            user_input = input("Is this a valid vulnerability? (yes/no): ").strip().lower()
            if user_input == "yes":
                logger.info(f"Vulnerability confirmed by human expert: {vuln}")
                print(Fore.GREEN + "[+] Vulnerability confirmed by human expert.")
                confirmed_vulnerabilities.append(vuln)
            else:
                logger.info(f"Vulnerability rejected by human expert: {vuln}")
                print(Fore.RED + "[-] Vulnerability rejected by human expert.")
        return confirmed_vulnerabilities
    except Exception as e:
        logger.error(f"Error in human-in-the-loop system: {str(e)}")
        return vulnerabilities

# تحسين اختبار الاستغلال
def test_exploit(url, payload):
    logger.info(f"Testing exploit on URL: {url} with payload: {payload}")
    print(Fore.MAGENTA + f"[*] Testing exploit on URL: {url} with payload: {payload}")
    
    try:
        if not isinstance(payload, str):
            payload = str(payload)
        
        response = session.get(
            url,
            headers=HEADERS,
            params=payload,
            timeout=30,
            allow_redirects=False
        )
        
        if response.status_code == 200:
            if str(payload) in response.text:
                logger.info("Exploit simulation successful (payload reflected)")
                print(Fore.GREEN + "[+] Exploit simulation successful (payload reflected).")
                return True, "Exploit simulation successful (payload reflected).", dict(response.request.headers), dict(response.headers), response.text
            else:
                logger.info("Exploit simulation failed (payload not reflected)")
                print(Fore.RED + "[-] Exploit simulation failed (payload not reflected).")
                return False, "Exploit simulation failed (payload not reflected).", dict(response.request.headers), dict(response.headers), response.text
    except Exception as e:
        logger.error(f"Error testing exploit: {str(e)}")
        print(Fore.RED + f"[-] Error testing exploit: {str(e)}")
        return False, "Exploit simulation failed.", None, None, None

# تحسين جلب تفاصيل CVE
@cached(cache)
def get_cve_details(cve_id):
    logger.info(f"Fetching CVE details for: {cve_id}")
    print(Fore.CYAN + f"[*] Fetching CVE details for: {cve_id}")
    
    if not cve_id.startswith("CVE-"):
        logger.error(f"Invalid CVE ID: {cve_id}")
        print(Fore.RED + f"[-] Invalid CVE ID: {cve_id}")
        return None
    
    url = f"https://services.nvd.nist.gov/rest/json/cve/1.0/{cve_id}"
    try:
        response = session.get(url, timeout=10)
        if response.status_code == 200:
            return response.json()
        else:
            logger.error(f"Failed to fetch CVE details: HTTP {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"Error fetching CVE details: {str(e)}")
        print(Fore.RED + f"[-] Error fetching CVE details: {str(e)}")
        return None

# تحسين جلب تفاصيل Exploit-DB
@cached(cache)
def get_exploit_db_details(query):
    logger.info(f"Fetching Exploit-DB details for: {query}")
    print(Fore.CYAN + f"[*] Fetching Exploit-DB details for: {query}")
    
    url = f"https://www.exploit-db.com/search?q={query}"
    try:
        response = session.get(url, timeout=10)
        if response.status_code == 200:
            return response.text
        else:
            logger.error(f"Failed to fetch Exploit-DB details: HTTP {response.status_code}")
            return None
    except Exception as e:
        logger.error(f"Error fetching Exploit-DB details: {str(e)}")
        print(Fore.RED + f"[-] Error fetching Exploit-DB details: {str(e)}")
        return None

# تحسين اكتشاف الثغرات باستخدام جميع النماذج
def find_vulnerabilities(content, url):
    logger.info("Identifying vulnerabilities in content")
    print(Fore.CYAN + "[*] Identifying vulnerabilities in the content...")
    
    try:
        vulnerabilities = []
        lines = content.splitlines()
        
        # معالجة متوازية للأسطر
        with ThreadPoolExecutor(max_workers=cpu_count()) as executor:
            processed_lines = list(executor.map(process_text, lines))
        
        # تحميل النماذج
        model_lstm = load_lstm_model()
        model_cnn = load_cnn_model()
        
        # تحميل Tokenizer
        tokenizer = Tokenizer(num_words=5000)
        tokenizer.fit_on_texts(processed_lines)
        sequences = tokenizer.texts_to_sequences(processed_lines)
        padded_sequences = pad_sequences(sequences, maxlen=100)
        
        # التنبؤ باستخدام LSTM و CNN
        predictions_lstm = model_lstm.predict(padded_sequences, batch_size=32, verbose=0)
        predictions_cnn = model_cnn.predict(padded_sequences, batch_size=32, verbose=0)
        
        # معالجة كل سطر لاكتشاف الثغرات
        for i, (line, prediction_lstm, prediction_cnn) in enumerate(zip(lines, predictions_lstm, predictions_cnn)):
            predictions = {}
            
            # التنبؤ باستخدام جميع النماذج الأخرى
            for key, model in models_and_vectorizers.items():
                if "vectorizer" in key:
                    continue
                
                vectorizer_key = key.replace("model", "vectorizer")
                if vectorizer_key in models_and_vectorizers:
                    vectorizer = models_and_vectorizers[vectorizer_key]
                    try:
                        vectorized_input = vectorizer.transform([processed_lines[i]])
                        prediction = model.predict(vectorized_input)[0]
                        predictions[key] = prediction
                    except Exception as e:
                        logger.error(f"Error predicting with model {key}: {str(e)}")
                        continue
            
            predictions["lstm_model"] = "Vulnerable" if prediction_lstm > 0.5 else "Safe"
            predictions["cnn_model"] = "Vulnerable" if prediction_cnn > 0.5 else "Safe"
            
            # جمع النتائج الإيجابية
            for key, prediction in predictions.items():
                if prediction != "Safe":
                    logger.warning(f"Vulnerability detected by model {key}")
                    print(Fore.RED + f"[!] Vulnerability detected by model: {key}")
                    
                    try:
                        cve_details = get_cve_details(str(prediction))
                        exploit_success, exploit_result, request_details, response_details, response_content = test_exploit(url, str(prediction))
                        exploit_method = determine_exploit_method(prediction)
                        
                        vulnerabilities.append({
                            "line_number": i + 1,
                            "line_content": line.strip(),
                            "prediction": prediction,
                            "model_used": key,
                            "proof_of_concept": f"Exploitation Example for {prediction}",
                            "severity": cve_details.get("impact", {}).get("baseMetricV3", {}).get("cvssV3", {}).get("baseSeverity", "Unknown") if cve_details else "Unknown",
                            "description": cve_details.get("cve", {}).get("description", {}).get("description_data", [{}])[0].get("value", "No description available") if cve_details else "No description available",
                            "direct_link": f"{url}#line-{i + 1}",
                            "full_context": f"Vulnerability found in line {i + 1}: {line.strip()}",
                            "exploit_result": exploit_result,
                            "request_details": request_details,
                            "response_details": response_details,
                            "response_content": response_content,
                            "payload": str(prediction),
                            "exploit_method": exploit_method
                        })
                    except Exception as e:
                        logger.error(f"Error processing vulnerability: {str(e)}")
        
        return vulnerabilities
    except Exception as e:
        logger.error(f"Error finding vulnerabilities: {str(e)}")
        return []

# تحسين تحديد طريقة الاستغلال
def determine_exploit_method(prediction):
    prediction_str = str(prediction).upper()
    if "XSS" in prediction_str:
        return "Reflected XSS"
    elif "SQL" in prediction_str:
        return "SQL Injection"
    elif "RCE" in prediction_str:
        return "Remote Code Execution"
    elif "LFI" in prediction_str:
        return "Local File Inclusion"
    elif "RFI" in prediction_str:
        return "Remote File Inclusion"
    else:
        return "Other"

# تحسين التقاط لقطات الشاشة
def capture_screenshot(url, output_path, filename):
    logger.info(f"Capturing screenshot for URL: {url}")
    print(Fore.CYAN + f"[*] Capturing screenshot for URL: {url}")
    
    try:
        options = webdriver.ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        
        driver = webdriver.Chrome(
            service=Service(ChromeDriverManager().install()),
            options=options
        )
        
        driver.get(url)
        time.sleep(2)  # انتظار تحميل الصفحة
        
        screenshot_path = os.path.join(output_path, filename)
        driver.save_screenshot(screenshot_path)
        driver.quit()
        
        logger.info(f"Screenshot saved at: {screenshot_path}")
        print(Fore.GREEN + f"[+] Screenshot saved at: {screenshot_path}")
        return screenshot_path
    except Exception as e:
        logger.error(f"Error capturing screenshot: {str(e)}")
        print(Fore.RED + f"[-] Error capturing screenshot: {str(e)}")
        return None

# تحسين إنشاء صور الثغرات المشروحة
def capture_vulnerability_image(line, line_number, prediction, model_used, url, filename, output_path, exploit_result=None, request_details=None, response_details=None, severity=None, description=None, payload=None, exploit_method=None):
    logger.info(f"Generating vulnerability image for line: {line_number}")
    print(Fore.CYAN + f"[*] Generating vulnerability image for line: {line_number}")
    
    try:
        image_width = 1200
        image_height = 800
        font_size = 16
        
        image = Image.new("RGB", (image_width, image_height), color="white")
        draw = ImageDraw.Draw(image)
        
        try:
            font = ImageFont.truetype("arial.ttf", font_size)
            bold_font = ImageFont.truetype("arialbd.ttf", font_size + 2)
        except IOError:
            font = ImageFont.load_default()
            bold_font = ImageFont.load_default()
        
        y = 10
        
        # رسم العنوان
        draw.text((10, y), f"Vulnerability Found on Line {line_number}", fill="red", font=bold_font)
        y += 40
        
        # رسم تفاصيل الثغرة
        details = [
            f"Type: {prediction}",
            f"Model Used: {model_used}",
            f"Severity: {severity}",
            f"Description: {description[:100]}..." if description else "Description: None",
            f"Payload: {payload[:100]}..." if payload else "Payload: None",
            f"Exploit Method: {exploit_method}",
            f"Code: {line[:200]}..." if line else "Code: None",
            "This code can be exploited! Please review the following details:",
            f"Proof of Concept: Exploitation Example for {prediction}",
            f"Direct Link: {url}#line-{line_number}"
        ]
        
        for detail in details:
            draw.text((10, y), detail, fill="black", font=font)
            y += 30
        
        if exploit_result:
            draw.text((10, y), f"Exploit Result: {exploit_result}", fill="black", font=font)
            y += 30
        
        if request_details:
            draw.text((10, y), "Request Details:", fill="blue", font=font)
            y += 30
            for k, v in request_details.items():
                draw.text((20, y), f"{k}: {v}", fill="black", font=font)
                y += 20
        
        if response_details:
            draw.text((10, y), "Response Details:", fill="blue", font=font)
            y += 30
            for k, v in response_details.items():
                draw.text((20, y), f"{k}: {v}", fill="black", font=font)
                y += 20
        
        # حفظ الصورة
        os.makedirs(output_path, exist_ok=True)
        image_path = os.path.join(output_path, filename)
        image.save(image_path)
        
        logger.info(f"Vulnerability image saved at: {image_path}")
        print(Fore.GREEN + f"[+] Vulnerability image saved at: {image_path}")
        return image_path
    except Exception as e:
        logger.error(f"Error generating image: {str(e)}")
        print(Fore.RED + f"[-] Error generating image: {str(e)}")
        return None

# تحسين تعداد النطاقات الفرعية
def enumerate_subdomains(domain):
    logger.info(f"Enumerating subdomains for domain: {domain}")
    print(Fore.CYAN + f"[*] Enumerating subdomains for domain: {domain}")
    
    try:
        subdomains = set()
        resolver = dns.resolver.Resolver()
        resolver.timeout = 5
        resolver.lifetime = 5
        
        # استعلامات متوازية لأنواع السجلات المختلفة
        record_types = ['A', 'AAAA', 'MX', 'NS', 'TXT', 'CNAME']
        
        with ThreadPoolExecutor(max_workers=len(record_types)) as executor:
            futures = []
            for record_type in record_types:
                futures.append(executor.submit(
                    lambda rt: [str(answer) for answer in resolver.resolve(domain, rt)],
                    record_type
                ))
            
            for future in as_completed(futures):
                try:
                    subdomains.update(future.result())
                except Exception:
                    continue
        
        return list(subdomains)
    except Exception as e:
        logger.error(f"Error enumerating subdomains: {str(e)}")
        print(Fore.RED + f"[-] Error enumerating subdomains: {str(e)}")
        return []

# تحسين تنظيف URL
def clean_url(url):
    parsed_url = urlparse(url)
    cleaned_url = urlunparse((
        parsed_url.scheme,
        parsed_url.netloc.lower(),
        parsed_url.path,
        '', '', ''
    ))
    return cleaned_url.rstrip('/')

# تحسين جلب الروابط
def fetch_links(base_url, visited=None, depth=0, max_depth=3):
    if visited is None:
        visited = set()
    
    logger.info(f"Fetching links from URL: {base_url} (Depth: {depth})")
    print(Fore.CYAN + f"[*] Fetching links from URL: {base_url} (Depth: {depth})")
    
    cleaned_base_url = clean_url(base_url)
    if depth > max_depth or cleaned_base_url in visited:
        return visited
    
    visited.add(cleaned_base_url)
    
    try:
        response = session.get(
            base_url,
            headers=HEADERS,
            timeout=30,
            allow_redirects=False
        )
        
        if response.status_code != 200:
            return visited
        
        tree = html.fromstring(response.content)
        links = tree.xpath('//a/@href')
        
        # تصفية الروابط غير المرغوب فيها
        filtered_links = [
            link for link in links
            if not (
                link.startswith('#') or
                link.startswith('javascript:') or
                link.endswith(('.pdf', '.jpg', '.png', '.gif', '.zip', '.exe'))
            )
        ]
        
        # معالجة الروابط بشكل متوازي
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = []
            for link in filtered_links:
                full_url = urljoin(base_url, link)
                cleaned_full_url = clean_url(full_url)
                
                if urlparse(cleaned_full_url).netloc == urlparse(cleaned_base_url).netloc:
                    futures.append(executor.submit(
                        fetch_links,
                        full_url,
                        visited,
                        depth + 1,
                        max_depth
                    ))
            
            for future in as_completed(futures):
                try:
                    future.result()
                except Exception:
                    continue
        
        return visited
    except Exception as e:
        logger.error(f"Error fetching links from URL: {base_url} - {str(e)}")
        print(Fore.RED + f"[-] Error fetching links from URL: {base_url} - {str(e)}")
        return visited

# تحويل رؤوس HTTP
def convert_headers(headers):
    if headers is None:
        return None
    return dict(headers)

# تحسين فحص الروابط
def scan_links(links, output_path, vulnerabilities=None):
    if vulnerabilities is None:
        vulnerabilities = []
    
    logger.info("Scanning links for vulnerabilities")
    print(Fore.YELLOW + "[*] Scanning links for vulnerabilities...")
    
    business_logic_vulnerabilities = []
    links_list = list(links)
    
    # معالجة الروابط بشكل متوازي
    with ThreadPoolExecutor(max_workers=min(10, cpu_count())) as executor:
        futures = []
        for link in links_list:
            futures.append(executor.submit(
                process_link,
                link,
                output_path
            ))
        
        for future in as_completed(futures):
            try:
                link_vulns, link_bl_vulns = future.result()
                vulnerabilities.extend(link_vulns)
                business_logic_vulnerabilities.extend(link_bl_vulns)
            except Exception as e:
                logger.error(f"Error processing link: {str(e)}")
    
    # حفظ ثغرات منطق الأعمال إذا وجدت
    if business_logic_vulnerabilities:
        save_business_logic_vulnerabilities(
            business_logic_vulnerabilities,
            os.path.join("business_logic", "business_logic_vulnerabilities.json")
        )
    
    return vulnerabilities

# وظيفة مساعدة لمعالجة كل رابط
def process_link(link, output_path):
    link_vulns = []
    link_bl_vulns = []
    
    try:
        response = session.get(
            link,
            headers=HEADERS,
            timeout=30,
            allow_redirects=False
        )
        
        if response.status_code == 200:
            vulns = find_vulnerabilities(response.text, link)
            link_vulns.extend(vulns)
            
            # التحقق من ثغرات منطق الأعمال
            for vuln in vulns:
                if "business logic" in vuln["prediction"].lower() or "zero-day" in vuln["prediction"].lower() or "human error" in vuln["prediction"].lower():
                    link_bl_vulns.append(vuln)
            
            # التقاط لقطة الشاشة
            capture_screenshot(link, output_path, f"vuln_{hash(link)}.png")
            
            # إنشاء صور الثغرات المشروحة
            for vuln in vulns:
                capture_vulnerability_image(
                    line=vuln['line_content'],
                    line_number=vuln['line_number'],
                    prediction=vuln['prediction'],
                    model_used=vuln['model_used'],
                    url=link,
                    filename=f"annotated_vuln_{hash(link)}_{vuln['line_number']}.png",
                    output_path=output_path,
                    exploit_result=vuln.get('exploit_result'),
                    request_details=vuln.get('request_details'),
                    response_details=vuln.get('response_details'),
                    severity=vuln.get('severity'),
                    description=vuln.get('description'),
                    payload=vuln.get('payload'),
                    exploit_method=vuln.get('exploit_method')
                )
    except Exception as e:
        logger.error(f"Error processing URL: {link} - {str(e)}")
    
    return link_vulns, link_bl_vulns

# تحسين حفظ ثغرات منطق الأعمال
def save_business_logic_vulnerabilities(vulnerabilities, filename):
    logger.info(f"Saving Business Logic Vulnerabilities to {filename}")
    print(Fore.YELLOW + f"[*] Saving Business Logic Vulnerabilities to {filename}...")
    
    try:
        os.makedirs(os.path.dirname(filename), exist_ok=True)
        
        with open(filename, 'w', encoding='utf-8') as file:
            json.dump(vulnerabilities, file, indent=4, default=str)
        
        logger.info(f"Business Logic Vulnerabilities saved to {filename}")
        print(Fore.GREEN + f"[+] Business Logic Vulnerabilities saved to {filename}")
    except Exception as e:
        logger.error(f"Error saving Business Logic Vulnerabilities: {str(e)}")
        print(Fore.RED + f"[-] Error saving Business Logic Vulnerabilities: {str(e)}")

# تحسين حفظ حالة الفحص
def save_scan_state(vulnerabilities, output_path, links, current_link_index):
    logger.info(f"Saving scan state at index {current_link_index}")
    
    try:
        state = {
            "vulnerabilities": vulnerabilities,
            "output_path": output_path,
            "links": list(links),
            "current_link_index": current_link_index,
            "timestamp": time.time()
        }
        
        os.makedirs("scan_states", exist_ok=True)
        state_path = os.path.join("scan_states", "scan_state.pkl")
        
        with open(state_path, 'wb') as f:
            joblib.dump(state, f, compress=('zlib', 6))
        
        logger.info(f"Scan state saved to {state_path}")
        print(Fore.GREEN + f"[+] Scan state saved. Last scanned link index: {current_link_index}")
    except Exception as e:
        logger.error(f"Error saving scan state: {str(e)}")
        print(Fore.RED + f"[-] Error saving scan state: {str(e)}")

# تحسين تحميل حالة الفحص
def load_scan_state():
    logger.info("Loading scan state")
    
    try:
        state_path = os.path.join("scan_states", "scan_state.pkl")
        if os.path.exists(state_path):
            with open(state_path, 'rb') as f:
                state = joblib.load(f)
            
            if state["current_link_index"] >= len(state["links"]):
                state["current_link_index"] = len(state["links"]) - 1
            
            logger.info(f"Scan state loaded. Resuming from link {state['current_link_index'] + 1}/{len(state['links'])}")
            print(Fore.GREEN + f"[+] Scan state loaded. Resuming from link {state['current_link_index'] + 1}/{len(state['links'])}")
            return state
        else:
            logger.warning("No saved scan state found")
            print(Fore.RED + "[-] No saved scan state found.")
            return None
    except Exception as e:
        logger.error(f"Error loading scan state: {str(e)}")
        return None

# تحسين حفظ النتائج إلى JSON
def save_to_json(vulnerabilities, filename):
    logger.info(f"Saving vulnerabilities to {filename}")
    print(Fore.YELLOW + f"[*] Saving vulnerabilities to {filename}...")
    
    try:
        os.makedirs("reports", exist_ok=True)
        
        serializable_vulns = []
        for vuln in vulnerabilities:
            serializable_vuln = vuln.copy()
            serializable_vuln['request_details'] = convert_headers(vuln.get('request_details'))
            serializable_vuln['response_details'] = convert_headers(vuln.get('response_details'))
            serializable_vulns.append(serializable_vuln)
        
        file_path = os.path.join("reports", filename)
        
        with open(file_path, 'w', encoding='utf-8') as file:
            json.dump(serializable_vulns, file, indent=4, ensure_ascii=False, default=str)
        
        logger.info(f"Vulnerabilities saved to {file_path}")
        print(Fore.GREEN + f"[+] Vulnerabilities saved to {file_path}")
    except Exception as e:
        logger.error(f"Error saving vulnerabilities: {str(e)}")
        print(Fore.RED + f"[-] Error saving vulnerabilities: {str(e)}")

# تحسين إنشاء تقرير PDF
def generate_pdf_report(vulnerabilities, filename):
    logger.info(f"Generating PDF report: {filename}")
    print(Fore.YELLOW + f"[*] Generating PDF report: {filename}...")
    
    try:
        from reportlab.lib.pagesizes import letter
        from reportlab.pdfgen import canvas
        from reportlab.lib.utils import ImageReader
        from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image
        from reportlab.lib.styles import getSampleStyleSheet
        from reportlab.lib.units import inch
        
        os.makedirs("reports", exist_ok=True)
        file_path = os.path.join("reports", filename)
        
        doc = SimpleDocTemplate(file_path, pagesize=letter)
        styles = getSampleStyleSheet()
        story = []
        
        # عنوان التقرير
        title = Paragraph("Vulnerability Report", styles['Title'])
        story.append(title)
        story.append(Spacer(1, 0.25 * inch))
        
        # إضافة كل ثغرة إلى التقرير
        for i, vuln in enumerate(vulnerabilities, 1):
            # تفاصيل الثغرة
            vuln_title = Paragraph(f"Vulnerability #{i}: {vuln['prediction']} (Severity: {vuln.get('severity', 'Unknown')})", styles['Heading2'])
            story.append(vuln_title)
            
            details = [
                f"Line {vuln['line_number']}: {vuln['line_content']}",
                f"Model Used: {vuln['model_used']}",
                f"Description: {vuln.get('description', 'No description available')}",
                f"Exploit Method: {vuln.get('exploit_method', 'Unknown')}",
                f"Payload: {vuln.get('payload', 'None')}",
                f"Direct Link: {vuln.get('direct_link', 'None')}"
            ]
            
            for detail in details:
                story.append(Paragraph(detail, styles['Normal']))
                story.append(Spacer(1, 0.1 * inch))
            
            # إضافة لقطة الشاشة إذا كانت متاحة
            screenshot_path = os.path.join("screenshots", f"vuln_{hash(vuln.get('direct_link', ''))}.png")
            if os.path.exists(screenshot_path):
                img = Image(screenshot_path, width=6 * inch, height=4.5 * inch)
                story.append(img)
                story.append(Spacer(1, 0.25 * inch))
            
            story.append(Spacer(1, 0.5 * inch))
        
        doc.build(story)
        logger.info(f"PDF report saved as {file_path}")
        print(Fore.GREEN + f"[+] PDF report saved as {file_path}")
    except Exception as e:
        logger.error(f"Error generating PDF report: {str(e)}")
        print(Fore.RED + f"[-] Error generating PDF report: {str(e)}")

# تحسين إنشاء مجلدات الإخراج
def create_output_folders():
    logger.info("Creating output folders")
    
    folders = [
        "reports",
        "screenshots",
        "business_logic",
        "zero_day",
        "human_errors",
        "registration_panels",
        "logs",
        "scan_states"
    ]
    
    for folder in folders:
        try:
            os.makedirs(folder, exist_ok=True)
        except Exception as e:
            logger.error(f"Error creating folder {folder}: {str(e)}")

# تحسين حفظ التقرير الكامل
def save_full_report(vulnerabilities, filename="full_report.json"):
    logger.info(f"Saving full report to {filename}")
    print(Fore.YELLOW + f"[*] Saving full report to {filename}...")
    
    try:
        os.makedirs("reports", exist_ok=True)
        file_path = os.path.join("reports", filename)
        
        serializable_vulns = []
        for vuln in vulnerabilities:
            serializable_vuln = vuln.copy()
            serializable_vuln['request_details'] = convert_headers(vuln.get('request_details'))
            serializable_vuln['response_details'] = convert_headers(vuln.get('response_details'))
            serializable_vulns.append(serializable_vuln)
        
        with open(file_path, 'w', encoding='utf-8') as file:
            json.dump(serializable_vulns, file, indent=4, ensure_ascii=False, default=str)
        
        logger.info(f"Full report saved to {file_path}")
        print(Fore.GREEN + f"[+] Full report saved to {file_path}")
    except Exception as e:
        logger.error(f"Error saving full report: {str(e)}")
        print(Fore.RED + f"[-] Error saving full report: {str(e)}")

# ==============================================
# تحسينات جديدة لنظام التقاط الصور (screenshots)
# ==============================================

def capture_screenshot_enhanced(url, output_path, filename, actions=None):
    """
    تقنية متقدمة لالتقاط لقطات الشاشة مع إمكانية تنفيذ إجراءات قبل الالتقاط
    """
    print(Fore.CYAN + f"[*] Capturing enhanced screenshot for URL: {url}")
    try:
        options = webdriver.ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        options.add_argument('--ignore-certificate-errors')
        
        driver = webdriver.Chrome(
            service=Service(ChromeDriverManager().install()),
            options=options
        )
        
        driver.get(url)
        time.sleep(3)  # انتظار تحميل الصفحة
        
        # تنفيذ إجراءات إضافية إذا تم توفيرها
        if actions:
            for action in actions:
                try:
                    if action['type'] == 'click':
                        element = WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((By.XPATH, action['xpath']))
                        )
                        element.click()
                        time.sleep(2)
                    elif action['type'] == 'input':
                        element = WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((By.XPATH, action['xpath'])))
                        element.clear()
                        element.send_keys(action['text'])
                        time.sleep(1)
                except Exception as e:
                    logger.warning(f"Failed to perform action {action}: {str(e)}")
        
        # التقاط الصورة الكاملة للصفحة
        total_height = driver.execute_script("return document.body.scrollHeight")
        viewport_height = driver.execute_script("return window.innerHeight")
        driver.set_window_size(1920, total_height)
        
        screenshot_path = os.path.join(output_path, filename)
        driver.save_screenshot(screenshot_path)
        
        # معالجة الصورة لتحسين الجودة
        try:
            img = Image.open(screenshot_path)
            
            # تحسين التباين
            enhancer = ImageEnhance.Contrast(img)
            img = enhancer.enhance(1.5)
            
            # تحسين الحدة
            enhancer = ImageEnhance.Sharpness(img)
            img = enhancer.enhance(2.0)
            
            # حفظ الصورة المحسنة
            img.save(screenshot_path, quality=95)
        except Exception as e:
            logger.warning(f"Image enhancement failed: {str(e)}")
        
        driver.quit()
        
        print(Fore.GREEN + f"[+] Enhanced screenshot saved at: {screenshot_path}")
        return screenshot_path
    except Exception as e:
        logger.error(f"Error capturing enhanced screenshot: {str(e)}")
        print(Fore.RED + f"[-] Error capturing enhanced screenshot: {str(e)}")
        return None

def compare_screenshots(img1_path, img2_path, output_path):
    """
    مقارنة بين لقطتين شاشة لاكتشاف التغييرات
    """
    try:
        img1 = cv2.imread(img1_path)
        img2 = cv2.imread(img2_path)
        
        # تحويل إلى تدرج الرمادي
        gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)
        
        # حساب الفروق البنيوية
        score, diff = ssim(gray1, gray2, full=True)
        diff = (diff * 255).astype("uint8")
        
        # تحديد المناطق المختلفة
        thresh = cv2.threshold(diff, 0, 255, cv2.THRESH_BINARY_INV | cv2.THRESH_OTSU)[1]
        contours = cv2.findContours(thresh.copy(), cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        contours = contours[0] if len(contours) == 2 else contours[1]
        
        # رسم المستطيلات حول المناطق المختلفة
        for c in contours:
            area = cv2.contourArea(c)
            if area > 100:  # تجاهل التغييرات الصغيرة
                x, y, w, h = cv2.boundingRect(c)
                cv2.rectangle(img1, (x, y), (x + w, y + h), (36, 255, 12), 2)
                cv2.rectangle(img2, (x, y), (x + w, y + h), (36, 255, 12), 2)
        
        # حفظ النتائج
        comparison_path = os.path.join(output_path, f"comparison_{os.path.basename(img1_path)}")
        cv2.imwrite(comparison_path, img2)
        
        return comparison_path, score
    except Exception as e:
        logger.error(f"Error comparing screenshots: {str(e)}")
        return None, 0

def extract_text_from_screenshot(image_path):
    """
    استخراج النص من لقطة الشاشة باستخدام OCR
    """
    try:
        pytesseract.pytesseract.tesseract_cmd = r'C:\Program Files\Tesseract-OCR\tesseract.exe'  # تحديث المسار حسب الحاجة
        text = pytesseract.image_to_string(Image.open(image_path))
        return text.strip()
    except Exception as e:
        logger.error(f"Error extracting text from screenshot: {str(e)}")
        return ""

# ==============================================
# تحسينات جديدة لكشف ثغرات منطق الأعمال (business_logic)
# ==============================================

def detect_business_logic_vulnerabilities_enhanced(vulnerabilities, url):
    """
    كشف متقدم لثغرات منطق الأعمال
    """
    print(Fore.YELLOW + "[*] Detecting Advanced Business Logic Vulnerabilities...")
    
    business_logic_vulns = []
    
    try:
        # تحليل تدفق العمل
        workflow_analysis = analyze_workflow(url)
        
        # اختبار تغيير المعلمات
        param_tampering_results = test_parameter_tampering(url)
        
        # اختبار التلاعب بالسعر
        price_manipulation_results = test_price_manipulation(url)
        
        # اختبار التصعيد غير المصرح به للصلاحيات
        privilege_escalation_results = test_privilege_escalation(url)
        
        # اختبار التلاعب بالحالة
        state_manipulation_results = test_state_manipulation(url)
        
        # تحليل تدفق العمل
        if workflow_analysis.get('is_vulnerable'):
            business_logic_vulns.append({
                "type": "Business Logic",
                "severity": "High",
                "description": "Workflow bypass vulnerability detected",
                "details": workflow_analysis,
                "url": url
            })
        
        # إضافة نتائج اختبارات التلاعب
        business_logic_vulns.extend(param_tampering_results)
        business_logic_vulns.extend(price_manipulation_results)
        business_logic_vulns.extend(privilege_escalation_results)
        business_logic_vulns.extend(state_manipulation_results)
        
        # تحليل الثغرات الموجودة مسبقاً
        for vuln in vulnerabilities:
            if "business logic" in vuln["prediction"].lower():
                business_logic_vulns.append({
                    "type": "Business Logic",
                    "severity": vuln.get("severity", "Medium"),
                    "description": vuln.get("description", "Business logic vulnerability detected"),
                    "details": vuln,
                    "url": url
                })
        
        print(Fore.GREEN + f"[+] Detected {len(business_logic_vulns)} Business Logic Vulnerabilities.")
        return business_logic_vulns
    
    except Exception as e:
        logger.error(f"Error detecting business logic vulnerabilities: {str(e)}")
        return []

def analyze_workflow(url):
    """
    تحليل تدفق العمل لاكتشاف الثغرات المنطقية
    """
    try:
        # محاكاة تدفق العمل الطبيعي
        normal_flow = simulate_normal_workflow(url)
        
        # محاولة تجاوز خطوات
        bypass_attempt = bypass_workflow_steps(url)
        
        # مقارنة النتائج
        if normal_flow['result'] == bypass_attempt['result']:
            return {
                'is_vulnerable': True,
                'description': 'Workflow bypass possible',
                'normal_flow': normal_flow,
                'bypass_attempt': bypass_attempt
            }
        
        return {'is_vulnerable': False}
    except Exception as e:
        logger.error(f"Error analyzing workflow: {str(e)}")
        return {'is_vulnerable': False, 'error': str(e)}

def test_parameter_tampering(url):
    """
    اختبار التلاعب بالمعلمات
    """
    results = []
    try:
        parsed = urlparse(url)
        params = parse_qs(parsed.query)
        
        for param in params:
            # اختبار حقن قيم غير متوقعة
            test_values = [
                "1' OR '1'='1",
                "-1",
                "999999999999999999",
                "<script>alert(1)</script>",
                "../../../etc/passwd"
            ]
            
            for value in test_values:
                tampered_params = params.copy()
                tampered_params[param] = value
                
                tampered_url = parsed._replace(query=None).geturl()
                tampered_url += "?" + "&".join(f"{k}={v[0]}" for k, v in tampered_params.items())
                
                response = session.get(tampered_url, headers=HEADERS, timeout=10)
                
                if response.status_code == 200:
                    if "error" not in response.text.lower():
                        results.append({
                            'type': 'Parameter Tampering',
                            'severity': 'Medium',
                            'description': f'Parameter {param} is vulnerable to tampering',
                            'parameter': param,
                            'test_value': value,
                            'url': tampered_url,
                            'response_status': response.status_code
                        })
    except Exception as e:
        logger.error(f"Error testing parameter tampering: {str(e)}")
    
    return results

def test_price_manipulation(url):
    """
    اختبار التلاعب بالأسعار (لصفحات الدفع)
    """
    results = []
    try:
        if "cart" in url.lower() or "checkout" in url.lower():
            response = session.get(url, headers=HEADERS, timeout=10)
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # البحث عن حقول الأسعار
            price_fields = soup.find_all(
                lambda tag: tag.name == 'input' and 
                ('price' in tag.get('id', '').lower() or 
                 'price' in tag.get('name', '').lower() or 
                 'amount' in tag.get('id', '').lower() or 
                 'amount' in tag.get('name', '').lower())
            )
            
            for field in price_fields:
                original_value = field.get('value', '')
                if original_value and original_value.replace('.', '').isdigit():
                    # اختبار تغيير السعر
                    tampered_value = str(float(original_value) * 0.1)  # خفض السعر إلى 10%
                    
                    # إعداد بيانات POST
                    form = field.find_parent('form')
                    if form:
                        form_data = {}
                        for input_tag in form.find_all('input'):
                            if input_tag.get('name'):
                                if input_tag == field:
                                    form_data[input_tag['name']] = tampered_value
                                else:
                                    form_data[input_tag['name']] = input_tag.get('value', '')
                        
                        # إرسال الطلب
                        action = form.get('action', url)
                        if not action.startswith('http'):
                            action = urljoin(url, action)
                        
                        response = session.post(
                            action,
                            data=form_data,
                            headers=HEADERS,
                            timeout=10
                        )
                        
                        if response.status_code == 200:
                            if "success" in response.text.lower():
                                results.append({
                                    'type': 'Price Manipulation',
                                    'severity': 'Critical',
                                    'description': f'Price can be manipulated from {original_value} to {tampered_value}',
                                    'field_name': field.get('name', ''),
                                    'original_value': original_value,
                                    'tampered_value': tampered_value,
                                    'url': url
                                })
    except Exception as e:
        logger.error(f"Error testing price manipulation: {str(e)}")
    
    return results

# ==============================================
# تحسينات جديدة لكشف الثغرات غير المعروفة (zero_day)
# ==============================================

def detect_zero_day_vulnerabilities(url, content):
    """
    كشف الثغرات غير المعروفة باستخدام تقنيات متقدمة
    """
    print(Fore.YELLOW + "[*] Detecting Zero-Day Vulnerabilities...")
    
    zero_day_vulns = []
    
    try:
        # تحليل السلوك غير الطبيعي
        anomaly_results = detect_anomalies(content)
        
        # تحليل الذاكرة
        memory_analysis_results = analyze_memory_patterns(content)
        
        # تحليل تدفق التحكم
        control_flow_results = analyze_control_flow(content)
        
        # التعلم غير المتوقع
        unexpected_learning_results = detect_unexpected_learning(content)
        
        # إضافة النتائج الإيجابية
        if anomaly_results.get('is_anomalous'):
            zero_day_vulns.append({
                'type': 'Zero-Day',
                'severity': 'High',
                'description': 'Anomalous behavior detected',
                'details': anomaly_results,
                'url': url
            })
        
        if memory_analysis_results.get('is_suspicious'):
            zero_day_vulns.append({
                'type': 'Zero-Day',
                'severity': 'High',
                'description': 'Suspicious memory patterns detected',
                'details': memory_analysis_results,
                'url': url
            })
        
        zero_day_vulns.extend(control_flow_results)
        zero_day_vulns.extend(unexpected_learning_results)
        
        print(Fore.GREEN + f"[+] Detected {len(zero_day_vulns)} potential Zero-Day Vulnerabilities.")
        return zero_day_vulns
    
    except Exception as e:
        logger.error(f"Error detecting zero-day vulnerabilities: {str(e)}")
        return []

def detect_anomalies(content):
    """
    اكتشاف السلوك غير الطبيعي باستخدام Isolation Forest
    """
    try:
        # تحويل المحتوى إلى متجهات عددية
        vectorizer = TfidfVectorizer(max_features=100)
        X = vectorizer.fit_transform([content])
        
        # تدريب نموذج Isolation Forest
        clf = IsolationForest(n_estimators=100, contamination=0.1)
        clf.fit(X.toarray())
        
        # التنبؤ بالشذوذ
        pred = clf.predict(X.toarray())
        
        return {
            'is_anomalous': pred[0] == -1,
            'anomaly_score': clf.decision_function(X.toarray())[0],
            'features': vectorizer.get_feature_names_out().tolist()
        }
    except Exception as e:
        logger.error(f"Error detecting anomalies: {str(e)}")
        return {'is_anomalous': False}

def analyze_memory_patterns(content):
    """
    تحليل أنماط الذاكرة لاكتشاف الثغرات
    """
    try:
        # البحث عن أنماط الذاكرة الخطيرة
        dangerous_patterns = [
            r'\b(malloc|calloc|realloc|free)\(',
            r'\b(strcpy|strcat|sprintf|gets)\(',
            r'\b(memcpy|memmove|memset)\(',
            r'\b(printf|fprintf|sprintf|snprintf)\(',
            r'\b(system|exec|popen)\('
        ]
        
        found_patterns = []
        for pattern in dangerous_patterns:
            if re.search(pattern, content):
                found_patterns.append(pattern)
        
        return {
            'is_suspicious': len(found_patterns) > 3,
            'found_patterns': found_patterns,
            'pattern_count': len(found_patterns)
        }
    except Exception as e:
        logger.error(f"Error analyzing memory patterns: {str(e)}")
        return {'is_suspicious': False}

# ==============================================
# تحسينات جديدة لكشف الأخطاء البشرية (human_errors)
# ==============================================

def detect_human_errors(url, content):
    """
    كشف الأخطاء البشرية في التطبيقات
    """
    print(Fore.YELLOW + "[*] Detecting Human Errors...")
    
    human_errors = []
    
    try:
        # كشف معلومات التصحيح المكشوفة
        debug_info = detect_exposed_debug_info(content)
        
        # كشف رسائل الأخطاء التفصيلية
        verbose_errors = detect_verbose_error_messages(content)
        
        # كشف بيانات الاعتماد الصلبة
        hardcoded_creds = detect_hardcoded_credentials(content)
        
        # كشف إعدادات التهيئة غير الآمنة
        insecure_configs = detect_insecure_configurations(content)
        
        # إضافة النتائج
        if debug_info:
            human_errors.append({
                'type': 'Human Error',
                'severity': 'Medium',
                'description': 'Exposed debug information found',
                'details': debug_info,
                'url': url
            })
        
        if verbose_errors:
            human_errors.append({
                'type': 'Human Error',
                'severity': 'Low',
                'description': 'Verbose error messages found',
                'details': verbose_errors,
                'url': url
            })
        
        human_errors.extend(hardcoded_creds)
        human_errors.extend(insecure_configs)
        
        print(Fore.GREEN + f"[+] Detected {len(human_errors)} Human Errors.")
        return human_errors
    
    except Exception as e:
        logger.error(f"Error detecting human errors: {str(e)}")
        return []

def detect_exposed_debug_info(content):
    """
    اكتشاف معلومات التصحيح المكشوفة
    """
    debug_patterns = [
        r'DEBUG\s*=\s*True',
        r'APP_DEBUG\s*=\s*True',
        r'debug\s*=\s*true',
        r'display_errors\s*=\s*On',
        r'error_reporting\s*\(E_ALL\)',
        r'X-Debug-.*',
        r'<!--\s*DEBUG\s*-->',
        r'debugger;',
        r'console\.log\(.*\)'
    ]
    
    found = []
    for pattern in debug_patterns:
        if re.search(pattern, content, re.IGNORECASE):
            found.append(pattern)
    
    return {
        'found_patterns': found,
        'is_detected': len(found) > 0
    }

def detect_hardcoded_credentials(content):
    """
    اكتشاف بيانات الاعتماد الصلبة في الكود
    """
    credential_patterns = [
        r'password\s*=\s*[\'"].*?[\'"]',
        r'api_key\s*=\s*[\'"].*?[\'"]',
        r'secret\s*=\s*[\'"].*?[\'"]',
        r'aws_access_key_id\s*=\s*[\'"].*?[\'"]',
        r'aws_secret_access_key\s*=\s*[\'"].*?[\'"]',
        r'database_password\s*=\s*[\'"].*?[\'"]',
        r'<connectionString>.*?Password=.*?</connectionString>'
    ]
    
    results = []
    for pattern in credential_patterns:
        matches = re.findall(pattern, content, re.IGNORECASE)
        for match in matches:
            results.append({
                'type': 'Human Error',
                'severity': 'Critical',
                'description': 'Hardcoded credentials found',
                'details': match,
                'pattern': pattern
            })
    
    return results

# ==============================================
# تحسينات جديدة لفحص لوحات التسجيل (registration_panels)
# ==============================================

def test_registration_panels(url):
    """
    فحص متقدم للوحات التسجيل
    """
    print(Fore.YELLOW + "[*] Testing Registration Panels...")
    
    vulns = []
    
    try:
        # البحث عن صفحة التسجيل
        registration_url = find_registration_page(url)
        
        if registration_url:
            # اختبار تسجيل مستخدمين متطابقين
            duplicate_reg_results = test_duplicate_registration(registration_url)
            
            # اختبار حقن SQL في التسجيل
            sql_inj_results = test_sql_injection_registration(registration_url)
            
            # اختبار XSS في التسجيل
            xss_results = test_xss_registration(registration_url)
            
            # اختبار التحقق من البريد الإلكتروني
            email_verification_results = test_email_verification(registration_url)
            
            # اختبار كلمات المرور الضعيفة
            weak_password_results = test_weak_password_policy(registration_url)
            
            # جمع النتائج
            vulns.extend(duplicate_reg_results)
            vulns.extend(sql_inj_results)
            vulns.extend(xss_results)
            vulns.extend(email_verification_results)
            vulns.extend(weak_password_results)
        
        print(Fore.GREEN + f"[+] Found {len(vulns)} vulnerabilities in registration panels.")
        return vulns
    
    except Exception as e:
        logger.error(f"Error testing registration panels: {str(e)}")
        return []

def find_registration_page(base_url):
    """
    البحث عن صفحة التسجيل في الموقع
    """
    try:
        common_paths = [
            '/register', '/signup', '/create-account',
            '/registration', '/sign-up', '/join'
        ]
        
        for path in common_paths:
            url = urljoin(base_url, path)
            response = session.get(url, headers=HEADERS, timeout=10)
            
            if response.status_code == 200:
                if any(word in response.text.lower() for word in ['register', 'sign up', 'create account']):
                    return url
        
        # إذا لم يتم العثور على المسار الشائع، البحث في الصفحة الرئيسية
        response = session.get(base_url, headers=HEADERS, timeout=10)
        soup = BeautifulSoup(response.text, 'html.parser')
        
        for link in soup.find_all('a', href=True):
            if any(word in link.text.lower() for word in ['register', 'sign up']):
                return urljoin(base_url, link['href'])
        
        return None
    except Exception as e:
        logger.error(f"Error finding registration page: {str(e)}")
        return None

def test_duplicate_registration(registration_url):
    """
    اختبار إمكانية تسجيل مستخدمين متطابقين
    """
    results = []
    fake_user = {
        'username': fake.user_name(),
        'email': fake.email(),
        'password': fake.password()
    }
    
    try:
        # الحصول على نموذج التسجيل
        response = session.get(registration_url, headers=HEADERS, timeout=10)
        soup = BeautifulSoup(response.text, 'html.parser')
        form = soup.find('form')
        
        if form:
            # تحديد حقول النموذج
            form_data = {}
            for input_tag in form.find_all('input'):
                if input_tag.get('name') and input_tag.get('type') != 'submit':
                    if 'user' in input_tag.get('name', '').lower():
                        form_data[input_tag['name']] = fake_user['username']
                    elif 'email' in input_tag.get('name', '').lower():
                        form_data[input_tag['name']] = fake_user['email']
                    elif 'pass' in input_tag.get('name', '').lower():
                        form_data[input_tag['name']] = fake_user['password']
                    else:
                        form_data[input_tag['name']] = 'test'
            
            # إرسال النموذج الأول
            action = form.get('action', registration_url)
            if not action.startswith('http'):
                action = urljoin(registration_url, action)
            
            response1 = session.post(
                action,
                data=form_data,
                headers=HEADERS,
                timeout=10
            )
            
            # إرسال النموذج الثاني بنفس البيانات
            response2 = session.post(
                action,
                data=form_data,
                headers=HEADERS,
                timeout=10
            )
            
            # التحقق من إمكانية التسجيل المكرر
            if response1.status_code == 200 and response2.status_code == 200:
                if "success" in response1.text.lower() and "success" in response2.text.lower():
                    results.append({
                        'type': 'Registration',
                        'severity': 'High',
                        'description': 'Duplicate registration allowed',
                        'details': 'Same user can be registered multiple times',
                        'url': registration_url
                    })
    except Exception as e:
        logger.error(f"Error testing duplicate registration: {str(e)}")
    
    return results

# ==============================================
# الوظيفة الرئيسية المحسنة
# ==============================================

def enhanced_scan(url, resume=False):
    """
    فحص محسن مع التوسعات الجديدة
    """
    create_output_folders()
    output_path = "screenshots"
    vulnerabilities = []
    links = set()
    current_link_index = 0
    
    if resume:
        state = load_scan_state()
        if state:
            vulnerabilities = state["vulnerabilities"]
            output_path = state["output_path"]
            links = set(state["links"])
            current_link_index = state["current_link_index"]
            print(Fore.YELLOW + f"[*] Resuming scan from link {current_link_index + 1}/{len(links)}")
        else:
            print(Fore.RED + "[-] Cannot resume scan. Starting a new scan.")
            resume = False
    
    if not resume:
        print(Fore.YELLOW + "[*] Starting a new enhanced scan.")
        try:
            parsed_url = urlparse(url)
            if not parsed_url.scheme:
                url = "https://" + url
            
            links = fetch_links(url)
            print(Fore.GREEN + f"[+] Found {len(links)} links.")
        except Exception as e:
            logger.error(f"Error during initial scan setup: {str(e)}")
            print(Fore.RED + f"[-] Error during initial scan setup: {str(e)}")
            return
    
    links_list = list(links)
    total_links = len(links_list)
    
    for i in range(current_link_index, total_links):
        link = links_list[i]
        print(Fore.CYAN + f"[*] Scanning link {i + 1}/{total_links}: {link}")
        
        try:
            response = session.get(link, headers=HEADERS, timeout=30)
            content = response.text
            
            # الفحص الأساسي للثغرات
            vulns = find_vulnerabilities(content, link)
            vulnerabilities.extend(vulns)
            
            # الفحص المتقدم للثغرات غير المعروفة
            zero_day_vulns = detect_zero_day_vulnerabilities(link, content)
            vulnerabilities.extend(zero_day_vulns)
            
            # كشف الأخطاء البشرية
            human_error_vulns = detect_human_errors(link, content)
            vulnerabilities.extend(human_error_vulns)
            
            # فحص لوحات التسجيل إذا كانت الصفحة الحالية هي صفحة تسجيل
            if "register" in link.lower() or "signup" in link.lower():
                registration_vulns = test_registration_panels(link)
                vulnerabilities.extend(registration_vulns)
            
            # التقاط لقطة شاشة محسنة
            actions = [
                {'type': 'input', 'xpath': '//input[@name="username"]', 'text': 'testuser'},
                {'type': 'input', 'xpath': '//input[@name="email"]', 'text': '<EMAIL>'},
                {'type': 'click', 'xpath': '//button[@type="submit"]'}
            ]
            capture_screenshot_enhanced(link, output_path, f"enhanced_{hash(link)}.png", actions)
            
            # حفظ حالة الفحص
            save_scan_state(vulnerabilities, output_path, links, i)
            
        except Exception as e:
            logger.error(f"Error processing URL: {link} - {str(e)}")
            print(Fore.RED + f"[-] Error processing URL: {link} - {str(e)}")
    
    # تحليل ثغرات منطق الأعمال بعد الانتهاء من جميع الروابط
    business_logic_vulns = detect_business_logic_vulnerabilities_enhanced(vulnerabilities, url)
    vulnerabilities.extend(business_logic_vulns)
    
    if vulnerabilities:
        save_to_json(vulnerabilities, "enhanced_vulnerability_report.json")
        generate_pdf_report(vulnerabilities, "enhanced_vulnerability_report.pdf")
        save_full_report(vulnerabilities, "enhanced_full_report.json")
        print(Fore.GREEN + f"[+] Enhanced reports saved. Screenshots are saved in '{output_path}'.")
    else:
        print(Fore.GREEN + "[+] No vulnerabilities or anomalies found.")

# ==============================================
# واجهة المستخدم المحسنة
# ==============================================

def enhanced_interact():
    """
    واجهة مستخدم محسنة مع خيارات الفحص المتقدم
    """
    while True:
        try:
            print("\n" + "="*50)
            print(Fore.CYAN + "Advanced Vulnerability Scanner")
            print("="*50)
            print("1. New Scan")
            print("2. Resume Previous Scan")
            print("3. Exit")
            print("="*50)
            
            choice = input("Enter your choice (1-3): ").strip()
            
            if choice == "1":
                url = input("Enter the URL to scan: ").strip()
                print(f"Starting enhanced scan for: {url}")
                enhanced_scan(url)
            
            elif choice == "2":
                url = input("Enter the URL to resume scanning: ").strip()
                print(f"Resuming scan for: {url}")
                enhanced_scan(url, resume=True)
            
            elif choice == "3":
                print(Fore.YELLOW + "[*] Exiting...")
                break
            
            else:
                print("Invalid choice! Please enter a number between 1 and 3.")
        
        except KeyboardInterrupt:
            print(Fore.YELLOW + "[*] Operation cancelled by user.")
            break
        
        except Exception as e:
            logger.error(f"Error during interaction: {str(e)}")
            print(Fore.RED + f"[-] Error: {str(e)}")

# ==============================================
# نقطة الدخول الرئيسية
# ==============================================

if __name__ == "__main__":
    enhanced_interact()