"""
Link Fetcher Module for Vulnerability Scanner

This module provides functions to fetch links from web pages for crawling.
"""

import os
import logging
from colorama import Fore, init

# Initialize colorama
init(autoreset=True)

# Configure logging
logger = logging.getLogger(__name__)

# Headers for Selenium (not used directly but kept for reference)
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Accept-Encoding": "gzip, deflate, br",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1",
    "Cache-Control": "max-age=0"
}

def fetch_links_sync(base_url, max_depth=3, max_urls=100, max_per_domain=50):
    """
    Synchronous link fetching using Selenium exclusively.
    تم تعديل هذه الدالة لتستخدم crawl_with_selenium_only حصريًا.

    Args:
        base_url (str): The base URL to start crawling from
        max_depth (int, optional): Maximum crawl depth
        max_urls (int, optional): Maximum number of URLs to crawl
        max_per_domain (int, optional): Maximum number of URLs to crawl per domain

    Returns:
        set: Set of discovered URLs
    """
    import os
    import sys
    from colorama import Fore

    # Add parent directory to path to import from 66.py
    current_dir = os.path.dirname(os.path.abspath(__file__))
    if current_dir not in sys.path:
        sys.path.append(current_dir)

    # Import the optimized Selenium crawler
    from importlib.util import spec_from_file_location, module_from_spec

    # Import crawl_with_selenium_only from 66.py
    spec = spec_from_file_location("main_66", os.path.join(current_dir, "66.py"))
    main_66 = module_from_spec(spec)
    spec.loader.exec_module(main_66)
    crawl_with_selenium_only = main_66.crawl_with_selenium_only

    print(Fore.CYAN + f"[*] بدء استخراج الروابط من: {base_url}")
    print(Fore.YELLOW + f"[!] تنبيه: تم توجيه الطلب إلى crawl_with_selenium_only للزحف")

    # Use crawl_with_selenium_only exclusively
    links = crawl_with_selenium_only(
        url=base_url,
        max_links=max_urls,
        max_depth=max_depth,
        max_per_domain=max_per_domain
    )

    # Convert list to set for consistency with original return type
    return set(links)
