"""
HTML Report Generator for Vulnerability Scanning

This module provides functions to generate detailed HTML reports from vulnerability scan results.
"""

import os
import json
from datetime import datetime

def generate_detailed_html_report(report_data, evidence_dir):
    """
    Generate a detailed HTML report from the JSON report data.

    Args:
        report_data (dict): The report data dictionary
        evidence_dir (str): Path to the evidence directory

    Returns:
        str: HTML report content
    """
    # Get report summary
    summary = report_data.get('summary', {})
    vulnerabilities = report_data.get('vulnerabilities', [])
    scan_url = report_data.get('url', 'Unknown')
    scan_time = report_data.get('scan_time', 'Unknown')
    scan_type = report_data.get('scan_type', 'Unknown').capitalize()

    # Start HTML content
    html = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vulnerability Scan Report - {scan_url}</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }}
        h1, h2, h3, h4 {{
            color: #2c3e50;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
        }}
        .header {{
            background-color: #2c3e50;
            color: white;
            padding: 20px;
            margin-bottom: 20px;
        }}
        .summary {{
            display: flex;
            justify-content: space-between;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }}
        .summary-box {{
            flex: 1;
            min-width: 150px;
            margin: 10px;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        .critical {{
            background-color: #e74c3c;
            color: white;
        }}
        .high {{
            background-color: #e67e22;
            color: white;
        }}
        .medium {{
            background-color: #f1c40f;
            color: #333;
        }}
        .low {{
            background-color: #3498db;
            color: white;
        }}
        .info {{
            background-color: #95a5a6;
            color: white;
        }}
        .vulnerability {{
            margin-bottom: 30px;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }}
        .vulnerability h3 {{
            margin-top: 0;
        }}
        .details {{
            margin-top: 10px;
        }}
        .details pre {{
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
        }}
        .evidence {{
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 5px;
            margin-top: 10px;
        }}
        .evidence-links {{
            margin-top: 15px;
        }}
        .evidence-links a {{
            display: inline-block;
            margin-right: 10px;
            padding: 8px 15px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 3px;
        }}
        .evidence-links a:hover {{
            background-color: #2980b9;
        }}
        .screenshot {{
            max-width: 100%;
            margin-top: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }}
        .payload {{
            font-family: monospace;
            background-color: #f9f9f9;
            padding: 10px;
            border-radius: 5px;
            overflow-x: auto;
            margin-top: 10px;
        }}
        .verified {{
            color: #27ae60;
            font-weight: bold;
        }}
        .not-verified {{
            color: #e74c3c;
            font-weight: bold;
        }}
        .exploit-success {{
            color: #27ae60;
            font-weight: bold;
        }}
        .exploit-failure {{
            color: #e74c3c;
        }}
        .tabs {{
            display: flex;
            margin-bottom: 10px;
        }}
        .tab {{
            padding: 10px 15px;
            background-color: #f1f1f1;
            cursor: pointer;
            margin-right: 5px;
            border-radius: 5px 5px 0 0;
        }}
        .tab.active {{
            background-color: #3498db;
            color: white;
        }}
        .tab-content {{
            display: none;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 0 5px 5px 5px;
        }}
        .tab-content.active {{
            display: block;
        }}
        .evidence-button {{
            display: inline-block;
            margin-right: 10px;
            margin-bottom: 10px;
            padding: 8px 15px;
            background-color: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 3px;
            font-weight: bold;
        }}
        .evidence-button:hover {{
            background-color: #2980b9;
        }}
        .evidence-button.disabled {{
            background-color: #95a5a6;
            cursor: not-allowed;
        }}
        .screenshot-button {{
            background-color: #27ae60;
        }}
        .screenshot-button:hover {{
            background-color: #219955;
        }}
        .request-button {{
            background-color: #e67e22;
        }}
        .request-button:hover {{
            background-color: #d35400;
        }}
        .response-button {{
            background-color: #9b59b6;
        }}
        .response-button:hover {{
            background-color: #8e44ad;
        }}
        .screenshot-container {{
            margin: 20px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #f9f9f9;
        }}
        .screenshot-caption {{
            margin-top: 10px;
            font-style: italic;
            color: #666;
            text-align: center;
        }}
        .evidence-details, .request-details, .response-details, .additional-evidence {{
            margin: 15px 0;
            padding: 15px;
            background-color: #f9f9f9;
            border-radius: 5px;
            border-left: 4px solid #3498db;
        }}
        .request-details {{
            border-left-color: #e67e22;
        }}
        .response-details {{
            border-left-color: #9b59b6;
        }}
        .additional-evidence {{
            border-left-color: #27ae60;
        }}
        h4 {{
            margin-top: 25px;
            margin-bottom: 10px;
            color: #2c3e50;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Vulnerability Scan Report</h1>
            <p><strong>URL:</strong> {scan_url}</p>
            <p><strong>Scan Time:</strong> {scan_time}</p>
            <p><strong>Scan Type:</strong> {scan_type}</p>
        </div>

        <h2>Summary</h2>
        <div class="summary">
            <div class="summary-box critical">
                <h3>Critical</h3>
                <p>{summary.get('critical', 0)}</p>
            </div>
            <div class="summary-box high">
                <h3>High</h3>
                <p>{summary.get('high', 0)}</p>
            </div>
            <div class="summary-box medium">
                <h3>Medium</h3>
                <p>{summary.get('medium', 0)}</p>
            </div>
            <div class="summary-box low">
                <h3>Low</h3>
                <p>{summary.get('low', 0)}</p>
            </div>
            <div class="summary-box info">
                <h3>Info</h3>
                <p>{summary.get('info', 0)}</p>
            </div>
        </div>

        <h2>Vulnerabilities</h2>
    """

    # Add each vulnerability to the report
    for vuln in vulnerabilities:
        # Get severity for styling
        severity = vuln.get('severity', 'Unknown').lower()

        # Set severity class for styling
        severity_class = ""
        if severity == 'critical':
            severity_class = "critical"
        elif severity == 'high':
            severity_class = "high"
        elif severity == 'medium':
            severity_class = "medium"
        elif severity == 'low':
            severity_class = "low"
        else:
            severity_class = "info"

        # Get verification status
        verified = vuln.get('verification_status', False)
        verification_confidence = vuln.get('verification_confidence', 0.0)

        # Format verification confidence as percentage
        verification_confidence_pct = f"{verification_confidence * 100:.1f}%"

        # Get exploit details
        exploit_success = vuln.get('exploit_success', False)
        exploit_result = vuln.get('exploit_result', 'No result available')

        # Get evidence files
        screenshot_path = vuln.get('screenshot_path', '')
        request_file = vuln.get('request_file', '')
        response_file = vuln.get('response_file', '')

        # Get additional evidence files
        evidence_dir_for_vuln = vuln.get('evidence_dir', '')
        vuln_type = vuln.get('type', 'unknown').replace(' ', '_').lower()

        # Try to find additional evidence if not explicitly provided
        if not screenshot_path and evidence_dir_for_vuln:
            # Look for screenshots in the vulnerability type directory
            vuln_type_dir = os.path.join(evidence_dir_for_vuln, vuln_type)
            if os.path.exists(vuln_type_dir):
                # Look for any PNG files that might be related to this vulnerability
                for file in os.listdir(vuln_type_dir):
                    if file.lower().endswith('.png') and ('vuln' in file.lower() or 'screenshot' in file.lower()):
                        screenshot_path = os.path.join(vuln_type_dir, file)
                        break

        # Try to find request/response files if not explicitly provided
        if not request_file and evidence_dir_for_vuln:
            vuln_type_dir = os.path.join(evidence_dir_for_vuln, vuln_type)
            if os.path.exists(vuln_type_dir):
                for file in os.listdir(vuln_type_dir):
                    if 'request' in file.lower() and file.lower().endswith('.txt'):
                        request_file = os.path.join(vuln_type_dir, file)
                        break

        if not response_file and evidence_dir_for_vuln:
            vuln_type_dir = os.path.join(evidence_dir_for_vuln, vuln_type)
            if os.path.exists(vuln_type_dir):
                for file in os.listdir(vuln_type_dir):
                    if 'response' in file.lower() and file.lower().endswith('.txt'):
                        response_file = os.path.join(vuln_type_dir, file)
                        break

        # Convert paths to relative paths for HTML
        if screenshot_path:
            try:
                rel_screenshot_path = os.path.relpath(screenshot_path, os.path.dirname(evidence_dir))
                # Make sure the path uses forward slashes for web compatibility
                rel_screenshot_path = rel_screenshot_path.replace('\\', '/')
            except ValueError:
                # If the paths are on different drives, use the absolute path
                rel_screenshot_path = screenshot_path.replace('\\', '/')
        else:
            rel_screenshot_path = ""

        if request_file:
            try:
                rel_request_file = os.path.relpath(request_file, os.path.dirname(evidence_dir))
                rel_request_file = rel_request_file.replace('\\', '/')
            except ValueError:
                rel_request_file = request_file.replace('\\', '/')
        else:
            rel_request_file = ""

        if response_file:
            try:
                rel_response_file = os.path.relpath(response_file, os.path.dirname(evidence_dir))
                rel_response_file = rel_response_file.replace('\\', '/')
            except ValueError:
                rel_response_file = response_file.replace('\\', '/')
        else:
            rel_response_file = ""

        # Add vulnerability to HTML
        html += f"""
        <div class="vulnerability {severity_class}">
            <h3>#{vuln.get('id', '?')} - {vuln.get('name', 'Unknown Vulnerability')}</h3>

            <div class="details">
                <p><strong>URL:</strong> {vuln.get('url', 'Unknown')}</p>
                <p><strong>Severity:</strong> {severity.capitalize()}</p>
                <p><strong>Verification Status:</strong>
                    <span class="{'verified' if verified else 'not-verified'}">
                        {'Verified' if verified else 'Not Verified'} (Confidence: {verification_confidence_pct})
                    </span>
                </p>
                <p><strong>Element:</strong> {vuln.get('element_name', 'Unknown')}</p>
                <p><strong>Description:</strong> {vuln.get('description', 'No description available')}</p>

                <div class="tabs">
                    <div class="tab active" onclick="showTab(this, 'details-{vuln.get('id', '0')}')">Details</div>
                    <div class="tab" onclick="showTab(this, 'evidence-{vuln.get('id', '0')}')">Evidence</div>
                    <div class="tab" onclick="showTab(this, 'technical-{vuln.get('id', '0')}')">Technical</div>
                </div>

                <div id="details-{vuln.get('id', '0')}" class="tab-content active">
                    <p><strong>Vulnerability Type:</strong> {vuln.get('vulnerability_type', 'Unknown')}</p>
                    <p><strong>Subtype:</strong> {vuln.get('subtype', 'N/A')}</p>
                    <p><strong>Element Type:</strong> {vuln.get('element_type', 'Unknown')}</p>
                    <p><strong>Scan Time:</strong> {vuln.get('scan_time', 'Unknown')}</p>
                    <p><strong>Payload:</strong></p>
                    <div class="payload">{vuln.get('payload', 'No payload available')}</div>
                </div>

                <div id="evidence-{vuln.get('id', '0')}" class="tab-content">
                    <p><strong>Exploit Success:</strong>
                        <span class="{'exploit-success' if exploit_success else 'exploit-failure'}">
                            {'Yes' if exploit_success else 'No'}
                        </span>
                    </p>
                    <p><strong>Exploit Result:</strong> {exploit_result}</p>

                    <h4>Evidence Files</h4>
                    <div class="evidence-links">
                        {f'<a href="{rel_screenshot_path}" target="_blank" class="evidence-button screenshot-button">View Screenshot</a>' if rel_screenshot_path else '<span class="evidence-button disabled">No Screenshot</span>'}
                        {f'<a href="{rel_request_file}" target="_blank" class="evidence-button request-button">View Request</a>' if rel_request_file else '<span class="evidence-button disabled">No Request</span>'}
                        {f'<a href="{rel_response_file}" target="_blank" class="evidence-button response-button">View Response</a>' if rel_response_file else '<span class="evidence-button disabled">No Response</span>'}
                    </div>

                    <h4>Evidence Details</h4>
                    <div class="evidence-details">
                        <p><strong>Evidence Type:</strong> {vuln.get('evidence_type', 'Screenshot and Request/Response')}</p>
                        <p><strong>Collection Time:</strong> {vuln.get('scan_time', 'Unknown')}</p>
                        <p><strong>Evidence Location:</strong> {vuln.get('evidence_dir', 'Unknown')}</p>
                    </div>

                    {f'<h4>Screenshot Evidence</h4><div class="screenshot-container"><img src="{rel_screenshot_path}" alt="Vulnerability Screenshot" class="screenshot"><p class="screenshot-caption">Screenshot showing the vulnerability: {vuln.get("type", "Unknown Vulnerability")}</p></div>' if rel_screenshot_path else ''}

                    {f'<h4>Request Details</h4><div class="request-details"><p>Request file available at: {request_file}</p></div>' if request_file else ''}

                    {f'<h4>Response Details</h4><div class="response-details"><p>Response file available at: {response_file}</p></div>' if response_file else ''}

                    <h4>Additional Evidence</h4>
                    <div class="additional-evidence">
                        {f'<p><strong>Payload Used:</strong> <code>{vuln.get("payload", "No payload information")}</code></p>' if vuln.get("payload", "") else ''}
                        {f'<p><strong>Evidence Text:</strong> <pre>{vuln.get("evidence", "No evidence text available")}</pre></p>' if vuln.get("evidence", "") else ''}
                        {f'<p><strong>Verification Method:</strong> {vuln.get("verification_method", "Not specified")}</p>' if vuln.get("verification_method", "") else ''}
                    </div>
                </div>

                <div id="technical-{vuln.get('id', '0')}" class="tab-content">
                    <p><strong>Technique Used:</strong> {vuln.get('technique_used', 'Unknown')}</p>
                    <p><strong>Verification Method:</strong> {vuln.get('verification_method', 'Unknown')}</p>
                    <p><strong>Confidence:</strong> {verification_confidence_pct}</p>

                    {f'<p><strong>Line Number:</strong> {vuln.get("line_number", "")}</p>' if vuln.get("line_number", "") else ''}
                    {f'<p><strong>Line Content:</strong></p><pre>{vuln.get("line_content", "")}</pre>' if vuln.get("line_content", "") else ''}
                </div>
            </div>
        </div>
        """

    # Add JavaScript for tab functionality
    html += """
    <script>
        function showTab(tabElement, tabContentId) {
            // Get all tabs in the same group
            const tabContainer = tabElement.parentElement;
            const tabs = tabContainer.getElementsByClassName('tab');
            const tabContents = tabContainer.nextElementSibling.parentElement.getElementsByClassName('tab-content');

            // Remove active class from all tabs and tab contents
            for (let i = 0; i < tabs.length; i++) {
                tabs[i].classList.remove('active');
            }

            for (let i = 0; i < tabContents.length; i++) {
                tabContents[i].classList.remove('active');
            }

            // Add active class to selected tab and tab content
            tabElement.classList.add('active');
            document.getElementById(tabContentId).classList.add('active');
        }
    </script>
    """

    # Close HTML
    html += """
    </div>
</body>
</html>
    """

    return html
