"""
OWASP Top 10 Categories for Vulnerability Classification

This module provides functions to map vulnerability types to OWASP Top 10 categories.
"""

def get_owasp_category(vuln_type):
    """
    Map a vulnerability type to its corresponding OWASP Top 10 category.
    
    Args:
        vuln_type (str): The type of vulnerability
        
    Returns:
        dict: OWASP categorization information
    """
    vuln_type = str(vuln_type).lower()
    
    # OWASP Top 10 2021 Categories
    if 'sql' in vuln_type or 'injection' in vuln_type:
        return {
            "id": "A03:2021",
            "name": "Injection",
            "description": "Injection flaws, such as SQL, NoSQL, OS, and LDAP injection, occur when untrusted data is sent to an interpreter as part of a command or query.",
            "reference": "https://owasp.org/Top10/A03_2021-Injection/"
        }
    
    elif 'xss' in vuln_type or 'cross-site' in vuln_type:
        return {
            "id": "A03:2021",
            "name": "Injection",
            "description": "Cross-Site Scripting (XSS) is a type of injection where malicious scripts are injected into trusted websites.",
            "reference": "https://owasp.org/Top10/A03_2021-Injection/"
        }
    
    elif 'broken auth' in vuln_type or 'authentication' in vuln_type or 'session' in vuln_type:
        return {
            "id": "A07:2021",
            "name": "Identification and Authentication Failures",
            "description": "Authentication failures can allow attackers to assume other users' identities.",
            "reference": "https://owasp.org/Top10/A07_2021-Identification_and_Authentication_Failures/"
        }
    
    elif 'csrf' in vuln_type or 'cross-site request' in vuln_type:
        return {
            "id": "A01:2021",
            "name": "Broken Access Control",
            "description": "Cross-Site Request Forgery (CSRF) allows an attacker to trick users into performing actions they did not intend to perform.",
            "reference": "https://owasp.org/Top10/A01_2021-Broken_Access_Control/"
        }
    
    elif 'idor' in vuln_type or 'insecure direct' in vuln_type or 'access control' in vuln_type:
        return {
            "id": "A01:2021",
            "name": "Broken Access Control",
            "description": "Restrictions on what authenticated users are allowed to do are often not properly enforced.",
            "reference": "https://owasp.org/Top10/A01_2021-Broken_Access_Control/"
        }
    
    elif 'security' in vuln_type and 'config' in vuln_type or 'misconfiguration' in vuln_type:
        return {
            "id": "A05:2021",
            "name": "Security Misconfiguration",
            "description": "Security misconfiguration is the most commonly seen issue, often a result of insecure default configurations or incomplete configurations.",
            "reference": "https://owasp.org/Top10/A05_2021-Security_Misconfiguration/"
        }
    
    elif 'sensitive' in vuln_type and 'data' in vuln_type or 'exposure' in vuln_type:
        return {
            "id": "A02:2021",
            "name": "Cryptographic Failures",
            "description": "Failures related to cryptography that often lead to sensitive data exposure.",
            "reference": "https://owasp.org/Top10/A02_2021-Cryptographic_Failures/"
        }
    
    elif 'xxe' in vuln_type or 'xml' in vuln_type:
        return {
            "id": "A05:2021",
            "name": "Security Misconfiguration",
            "description": "XML External Entities (XXE) attacks target applications that parse XML input.",
            "reference": "https://owasp.org/Top10/A05_2021-Security_Misconfiguration/"
        }
    
    elif 'deserialization' in vuln_type:
        return {
            "id": "A08:2021",
            "name": "Software and Data Integrity Failures",
            "description": "Insecure deserialization can lead to remote code execution attacks.",
            "reference": "https://owasp.org/Top10/A08_2021-Software_and_Data_Integrity_Failures/"
        }
    
    elif 'component' in vuln_type or 'library' in vuln_type or 'dependency' in vuln_type:
        return {
            "id": "A06:2021",
            "name": "Vulnerable and Outdated Components",
            "description": "Components, such as libraries, frameworks, and other software modules, run with the same privileges as the application.",
            "reference": "https://owasp.org/Top10/A06_2021-Vulnerable_and_Outdated_Components/"
        }
    
    elif 'logging' in vuln_type or 'monitor' in vuln_type:
        return {
            "id": "A09:2021",
            "name": "Security Logging and Monitoring Failures",
            "description": "Insufficient logging and monitoring, coupled with missing or ineffective integration with incident response.",
            "reference": "https://owasp.org/Top10/A09_2021-Security_Logging_and_Monitoring_Failures/"
        }
    
    elif 'ssrf' in vuln_type or 'request forgery' in vuln_type:
        return {
            "id": "A10:2021",
            "name": "Server-Side Request Forgery",
            "description": "SSRF flaws occur whenever a web application is fetching a remote resource without validating the user-supplied URL.",
            "reference": "https://owasp.org/Top10/A10_2021-Server-Side_Request_Forgery_%28SSRF%29/"
        }
    
    elif 'command' in vuln_type or 'rce' in vuln_type or 'code' in vuln_type and 'execution' in vuln_type:
        return {
            "id": "A03:2021",
            "name": "Injection",
            "description": "Command injection attacks are possible when an application passes unsafe user supplied data to a system shell.",
            "reference": "https://owasp.org/Top10/A03_2021-Injection/"
        }
    
    elif 'lfi' in vuln_type or 'rfi' in vuln_type or 'file inclusion' in vuln_type:
        return {
            "id": "A01:2021",
            "name": "Broken Access Control",
            "description": "File inclusion vulnerabilities allow an attacker to include a file from the server or from another site.",
            "reference": "https://owasp.org/Top10/A01_2021-Broken_Access_Control/"
        }
    
    elif 'open redirect' in vuln_type:
        return {
            "id": "A04:2021",
            "name": "Insecure Design",
            "description": "Open redirects occur when an application redirects users to an untrusted destination.",
            "reference": "https://owasp.org/Top10/A04_2021-Insecure_Design/"
        }
    
    # Default category for unknown vulnerability types
    return {
        "id": "Unknown",
        "name": "Uncategorized Vulnerability",
        "description": "This vulnerability type does not clearly map to a specific OWASP Top 10 category.",
        "reference": "https://owasp.org/Top10/"
    }
