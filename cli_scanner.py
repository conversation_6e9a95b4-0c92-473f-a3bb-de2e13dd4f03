#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
CLI Scanner - Interfaz de línea de comandos para el escáner de vulnerabilidades

Este script proporciona una interfaz de línea de comandos interactiva para el escáner de vulnerabilidades,
permitiendo al usuario seleccionar el tipo de escaneo, la URL objetivo y configurar opciones adicionales
antes de iniciar el escaneo.
"""

import os
import sys
import asyncio
import importlib.util
from colorama import Fore, Style, init
import argparse

# Inicializar colorama
init(autoreset=True)

# Importar el módulo 66.py
try:
    spec = importlib.util.spec_from_file_location("module_66", "66.py")
    module_66 = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module_66)
    print(Fore.GREEN + "[+] Módulo de escaneo importado correctamente")
except Exception as e:
    print(Fore.RED + f"[-] Error importando el módulo de escaneo: {str(e)}")
    sys.exit(1)

def print_banner():
    """Imprime un banner de bienvenida para el escáner"""
    banner = """
    ╔═══════════════════════════════════════════════════════════════╗
    ║                                                               ║
    ║   █▀▀ █▀▀ █▀▀ █▀▀ █▀█ █▀█ █▀▀ █▀█   █▀▄ █▀▀   █▀▀ █▀▀ █▀▀    ║
    ║   █▀▀ ▀▀█ █   █▀▀ █▀█ █▀▀ █▀▀ █▀▄   █▀▄ █▀▀   ▀▀█ █   █      ║
    ║   ▀▀▀ ▀▀▀ ▀▀▀ ▀▀▀ ▀ ▀ ▀   ▀▀▀ ▀ ▀   ▀▀  ▀▀▀   ▀▀▀ ▀▀▀ ▀▀▀    ║
    ║                                                               ║
    ║   Escáner de Vulnerabilidades Web - Versión Mejorada         ║
    ║                                                               ║
    ╚═══════════════════════════════════════════════════════════════╝
    """
    print(Fore.CYAN + banner)

def get_user_input(prompt, default=None, options=None, is_number=False, min_val=None, max_val=None):
    """
    Obtiene entrada del usuario con validación.

    Args:
        prompt (str): El mensaje para mostrar al usuario
        default: Valor por defecto si el usuario no ingresa nada
        options (list): Lista de opciones válidas
        is_number (bool): Si la entrada debe ser un número
        min_val: Valor mínimo para números
        max_val: Valor máximo para números

    Returns:
        La entrada validada del usuario
    """
    default_str = f" [{default}]" if default is not None else ""
    options_str = f" ({'/'.join(map(str, options))})" if options else ""

    while True:
        user_input = input(Fore.YELLOW + f"{prompt}{default_str}{options_str}: " + Style.RESET_ALL)

        # Usar valor por defecto si no hay entrada
        if not user_input and default is not None:
            return default

        # Validar opciones
        if options and user_input not in options:
            print(Fore.RED + f"[-] Entrada inválida. Por favor, elija una de las opciones: {', '.join(map(str, options))}")
            continue

        # Validar números
        if is_number:
            try:
                value = int(user_input)
                if min_val is not None and value < min_val:
                    print(Fore.RED + f"[-] El valor debe ser al menos {min_val}")
                    continue
                if max_val is not None and value > max_val:
                    print(Fore.RED + f"[-] El valor debe ser como máximo {max_val}")
                    continue
                return value
            except ValueError:
                print(Fore.RED + "[-] Por favor, ingrese un número válido")
                continue

        return user_input

def get_scan_options():
    """
    Obtiene las opciones de escaneo del usuario.

    Returns:
        dict: Diccionario con las opciones de escaneo
    """
    print(Fore.CYAN + "\n[*] Configuración del Escaneo")
    print(Fore.CYAN + "=" * 50)

    # Tipo de escaneo
    print(Fore.CYAN + "\n[*] Tipos de escaneo disponibles:")
    print(Fore.CYAN + "  1. Escaneo Tradicional - Análisis secuencial completo de vulnerabilidades")
    print(Fore.CYAN + "  2. Escaneo Rápido - Análisis básico de vulnerabilidades (más rápido)")
    print(Fore.CYAN + "  3. Escaneo Profundo - Análisis exhaustivo de vulnerabilidades (más lento)")

    scan_type = get_user_input("Seleccione el tipo de escaneo", default="1", options=["1", "2", "3"])

    # URL objetivo
    target_url = get_user_input("Ingrese la URL objetivo (incluyendo http:// o https://)",
                               default="https://example.com")

    # Opciones avanzadas
    print(Fore.CYAN + "\n[*] ¿Desea configurar opciones avanzadas?")
    advanced_options = get_user_input("Configurar opciones avanzadas", default="n", options=["y", "n"]) == "y"

    options = {
        "scan_type": scan_type,
        "target_url": target_url,
        "max_links": 100,
        "max_depth": 3,
        "max_per_domain": 50,
        "evidence_dir": "evidence"
    }

    if advanced_options:
        print(Fore.CYAN + "\n[*] Opciones Avanzadas")
        print(Fore.CYAN + "-" * 30)

        options["max_links"] = get_user_input("Número máximo de enlaces a escanear",
                                             default=100, is_number=True, min_val=1, max_val=1000)

        options["max_depth"] = get_user_input("Profundidad máxima de crawling",
                                             default=3, is_number=True, min_val=1, max_val=10)

        options["max_per_domain"] = get_user_input("Máximo de URLs por dominio",
                                                 default=50, is_number=True, min_val=1, max_val=500)

        options["evidence_dir"] = get_user_input("Directorio para guardar evidencias",
                                               default="evidence")

    return options

async def run_scan(options):
    """
    Ejecuta el escaneo con las opciones proporcionadas.

    Args:
        options (dict): Opciones de escaneo
    """
    print(Fore.CYAN + "\n[*] Iniciando escaneo con las siguientes opciones:")
    print(Fore.CYAN + f"  - Tipo de escaneo: {options['scan_type']}")
    print(Fore.CYAN + f"  - URL objetivo: {options['target_url']}")
    print(Fore.CYAN + f"  - Máximo de enlaces: {options['max_links']}")
    print(Fore.CYAN + f"  - Profundidad máxima: {options['max_depth']}")
    print(Fore.CYAN + f"  - Máximo por dominio: {options['max_per_domain']}")
    print(Fore.CYAN + f"  - Directorio de evidencias: {options['evidence_dir']}")

    # Crear directorio de evidencias
    os.makedirs(options["evidence_dir"], exist_ok=True)

    try:
        # Ejecutar el tipo de escaneo seleccionado
        if options["scan_type"] == "1":  # Escaneo Tradicional
            print(Fore.CYAN + f"\n[*] Ejecutando escaneo tradicional en {options['target_url']}")
            vulnerabilities = await module_66.run_traditional_scan(
                options["target_url"],
                max_links=options["max_links"],
                max_depth=options["max_depth"],
                max_per_domain=options["max_per_domain"],
                evidence_dir=options["evidence_dir"]
            )
        elif options["scan_type"] == "2":  # Escaneo Rápido
            print(Fore.CYAN + f"\n[*] Ejecutando escaneo rápido en {options['target_url']}")
            # Usar valores más bajos para un escaneo rápido
            vulnerabilities = await module_66.run_traditional_scan(
                options["target_url"],
                max_links=min(options["max_links"], 20),
                max_depth=min(options["max_depth"], 2),
                max_per_domain=min(options["max_per_domain"], 10),
                evidence_dir=options["evidence_dir"]
            )
        elif options["scan_type"] == "3":  # Escaneo Profundo
            print(Fore.CYAN + f"\n[*] Ejecutando escaneo profundo en {options['target_url']}")
            # Usar valores más altos para un escaneo profundo
            vulnerabilities = await module_66.run_traditional_scan(
                options["target_url"],
                max_links=options["max_links"] * 2,
                max_depth=options["max_depth"] + 1,
                max_per_domain=options["max_per_domain"] * 2,
                evidence_dir=options["evidence_dir"]
            )

        # Solo mostrar mensaje de éxito si realmente se encontraron vulnerabilidades
        if vulnerabilities is not None:
            print(Fore.GREEN + f"\n[+] Escaneo completado. Se encontraron {len(vulnerabilities)} vulnerabilidades")
        else:
            print(Fore.RED + f"\n[!] El escaneo no se completó correctamente.")

    except Exception as e:
        print(Fore.RED + f"\n[-] Error ejecutando el escaneo: {str(e)}")
        print(Fore.RED + f"[!] Crawling failed. Scan aborted. No scan phases were executed.")

def parse_command_line_args():
    """
    Parsea los argumentos de línea de comandos.

    Returns:
        dict: Opciones de escaneo basadas en argumentos de línea de comandos
    """
    parser = argparse.ArgumentParser(description="Escáner de Vulnerabilidades Web")
    parser.add_argument("-u", "--url", help="URL objetivo para escanear")
    parser.add_argument("-t", "--type", choices=["1", "2", "3"],
                        help="Tipo de escaneo: 1=Tradicional, 2=Rápido, 3=Profundo")
    parser.add_argument("-l", "--max-links", type=int, help="Número máximo de enlaces a escanear")
    parser.add_argument("-d", "--max-depth", type=int, help="Profundidad máxima de crawling")
    parser.add_argument("-p", "--max-per-domain", type=int, help="Máximo de URLs por dominio")
    parser.add_argument("-e", "--evidence-dir", help="Directorio para guardar evidencias")

    args = parser.parse_args()

    # Si se proporcionaron argumentos, usarlos en lugar de la interfaz interactiva
    if args.url or args.type:
        options = {
            "scan_type": args.type or "1",
            "target_url": args.url or "https://example.com",
            "max_links": args.max_links or 100,
            "max_depth": args.max_depth or 3,
            "max_per_domain": args.max_per_domain or 50,
            "evidence_dir": args.evidence_dir or "evidence"
        }
        return options, True

    return {}, False

async def main():
    """Función principal"""
    print_banner()

    # Verificar argumentos de línea de comandos
    cmd_options, use_cmd_args = parse_command_line_args()

    if use_cmd_args:
        options = cmd_options
    else:
        # Obtener opciones de escaneo del usuario
        options = get_scan_options()

    # Confirmar inicio del escaneo
    if not use_cmd_args:
        print(Fore.CYAN + "\n[*] Configuración completada. ¿Desea iniciar el escaneo?")
        start_scan = get_user_input("Iniciar escaneo", default="y", options=["y", "n"]) == "y"

        if not start_scan:
            print(Fore.YELLOW + "\n[!] Escaneo cancelado por el usuario")
            return

    # Ejecutar el escaneo
    await run_scan(options)

if __name__ == "__main__":
    asyncio.run(main())
