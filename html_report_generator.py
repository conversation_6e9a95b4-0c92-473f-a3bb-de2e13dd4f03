"""
HTML Report Generator for Traditional Vulnerability Scanning

This module generates detailed HTML reports from vulnerability scan results.
"""

import os
import json
from datetime import datetime

def generate_html_report(report_data, evidence_dir):
    """
    Generate a detailed HTML report from the JSON report data.
    
    Args:
        report_data (dict): The report data dictionary
        evidence_dir (str): Path to the evidence directory
        
    Returns:
        str: HTML report content
    """
    # Get report summary
    summary = report_data.get('summary', {})
    vulnerabilities = report_data.get('vulnerabilities', [])
    scan_url = report_data.get('url', 'Unknown')
    scan_time = report_data.get('scan_time', 'Unknown')
    
    # Create HTML content
    html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Vulnerability Scan Report - {scan_url}</title>
    <style>
        body {{
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            border-radius: 5px;
        }}
        h1, h2, h3 {{
            color: #2c3e50;
        }}
        h1 {{
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }}
        .summary-box {{
            background-color: #f8f9fa;
            border-left: 4px solid #3498db;
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 4px;
        }}
        .vulnerability {{
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 20px;
            overflow: hidden;
        }}
        .vulnerability-header {{
            padding: 10px 15px;
            background-color: #f8f9fa;
            border-bottom: 1px solid #ddd;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }}
        .vulnerability-body {{
            padding: 15px;
            display: none;
        }}
        .vulnerability.active .vulnerability-body {{
            display: block;
        }}
        .severity {{
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            color: white;
            font-weight: bold;
            font-size: 0.8em;
            text-transform: uppercase;
        }}
        .critical {{
            background-color: #e74c3c;
        }}
        .high {{
            background-color: #e67e22;
        }}
        .medium {{
            background-color: #f39c12;
        }}
        .low {{
            background-color: #3498db;
        }}
        .info {{
            background-color: #2ecc71;
        }}
        .verified {{
            color: #2ecc71;
        }}
        .not-verified {{
            color: #e74c3c;
        }}
        .details p {{
            margin: 5px 0;
        }}
        .tabs {{
            display: flex;
            border-bottom: 1px solid #ddd;
            margin-bottom: 15px;
        }}
        .tab {{
            padding: 8px 15px;
            cursor: pointer;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-bottom: none;
            margin-right: 5px;
            border-radius: 4px 4px 0 0;
        }}
        .tab.active {{
            background-color: #fff;
            border-bottom: 1px solid #fff;
            margin-bottom: -1px;
        }}
        .tab-content {{
            display: none;
            padding: 15px;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 4px 4px;
        }}
        .tab-content.active {{
            display: block;
        }}
        .payload {{
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            overflow-x: auto;
        }}
        .evidence-image {{
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
            margin-top: 10px;
        }}
        .summary-chart {{
            display: flex;
            justify-content: space-around;
            margin-bottom: 20px;
        }}
        .chart {{
            width: 45%;
        }}
        table {{
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }}
        th, td {{
            padding: 8px 12px;
            text-align: left;
            border-bottom: 1px solid #ddd;
        }}
        th {{
            background-color: #f8f9fa;
        }}
        .evidence-link {{
            color: #3498db;
            text-decoration: none;
        }}
        .evidence-link:hover {{
            text-decoration: underline;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>Vulnerability Scan Report</h1>
        
        <div class="summary-box">
            <h2>Scan Summary</h2>
            <p><strong>Target URL:</strong> {scan_url}</p>
            <p><strong>Scan Time:</strong> {scan_time}</p>
            <p><strong>Total Vulnerabilities:</strong> {len(vulnerabilities)}</p>
        </div>
        
        <div class="summary-chart">
            <div class="chart">
                <h3>Vulnerabilities by Severity</h3>
                <table>
                    <tr>
                        <th>Severity</th>
                        <th>Count</th>
                    </tr>
"""
    
    # Add severity counts to the table
    severity_counts = summary.get('by_severity', {})
    for severity, count in severity_counts.items():
        html_content += f"""
                    <tr>
                        <td><span class="severity {severity.lower()}">{severity.upper()}</span></td>
                        <td>{count}</td>
                    </tr>"""
    
    html_content += """
                </table>
            </div>
            
            <div class="chart">
                <h3>Vulnerabilities by Type</h3>
                <table>
                    <tr>
                        <th>Type</th>
                        <th>Count</th>
                    </tr>
"""
    
    # Add type counts to the table
    type_counts = summary.get('by_type', {})
    for vuln_type, count in type_counts.items():
        html_content += f"""
                    <tr>
                        <td>{vuln_type}</td>
                        <td>{count}</td>
                    </tr>"""
    
    html_content += """
                </table>
            </div>
        </div>
        
        <h2>Vulnerabilities</h2>
"""
    
    # Add each vulnerability to the report
    for i, vuln in enumerate(vulnerabilities):
        # Get severity for styling
        severity = vuln.get('severity', 'Unknown').lower()
        
        # Set severity class for styling
        severity_class = severity if severity in ['critical', 'high', 'medium', 'low'] else 'info'
        
        # Get verification status
        exploit_success = vuln.get('exploit_success', False)
        
        # Get evidence files
        screenshot_path = vuln.get('screenshot_path', '')
        request_file = vuln.get('request_file', '')
        response_file = vuln.get('response_file', '')
        
        # Convert paths to relative paths for HTML
        if screenshot_path:
            rel_screenshot_path = os.path.relpath(screenshot_path, os.path.dirname(evidence_dir))
        else:
            rel_screenshot_path = ""
        
        if request_file:
            rel_request_file = os.path.relpath(request_file, os.path.dirname(evidence_dir))
        else:
            rel_request_file = ""
        
        if response_file:
            rel_response_file = os.path.relpath(response_file, os.path.dirname(evidence_dir))
        else:
            rel_response_file = ""
        
        # Add vulnerability entry
        html_content += f"""
        <div class="vulnerability" id="vuln-{i}">
            <div class="vulnerability-header" onclick="toggleVulnerability('vuln-{i}')">
                <h3>{vuln.get('vulnerability_type', 'Unknown Vulnerability')} in {vuln.get('element_type', 'Unknown Element')}</h3>
                <span class="severity {severity_class}">{severity.upper()}</span>
            </div>
            
            <div class="vulnerability-body">
                <div class="tabs">
                    <div class="tab active" onclick="showTab('details-{i}', this)">Details</div>
                    <div class="tab" onclick="showTab('technical-{i}', this)">Technical</div>
                    <div class="tab" onclick="showTab('evidence-{i}', this)">Evidence</div>
                </div>
                
                <div id="details-{i}" class="tab-content active">
                    <div class="details">
                        <p><strong>URL:</strong> {vuln.get('url', 'Unknown')}</p>
                        <p><strong>Severity:</strong> {severity.capitalize()}</p>
                        <p><strong>Element Type:</strong> {vuln.get('element_type', 'Unknown')}</p>
                        <p><strong>Element Name:</strong> {vuln.get('parameter_name', vuln.get('input_name', 'Unknown'))}</p>
                        <p><strong>Vulnerability Type:</strong> {vuln.get('vulnerability_type', 'Unknown')}</p>
                        <p><strong>Exploit Success:</strong> <span class="{'verified' if exploit_success else 'not-verified'}">{str(exploit_success)}</span></p>
                        <p><strong>Payload:</strong></p>
                        <div class="payload">{vuln.get('payload', 'No payload available')}</div>
                    </div>
                </div>
                
                <div id="technical-{i}" class="tab-content">
                    <p><strong>Request Method:</strong> {vuln.get('request_method', 'Unknown')}</p>
                    <p><strong>Response Status:</strong> {vuln.get('response_status', 'Unknown')}</p>
                    <p><strong>Evidence:</strong> {vuln.get('response_evidence', 'No evidence available')}</p>
                    <p><strong>Exploit Result:</strong> {vuln.get('exploit_result', 'No result available')}</p>
                </div>
                
                <div id="evidence-{i}" class="tab-content">
"""
        
        # Add evidence files if available
        if rel_screenshot_path:
            html_content += f"""
                    <p><strong>Screenshot:</strong></p>
                    <img src="{rel_screenshot_path}" alt="Vulnerability Screenshot" class="evidence-image">
"""
        
        if rel_request_file:
            html_content += f"""
                    <p><strong>Request Details:</strong> <a href="{rel_request_file}" class="evidence-link" target="_blank">View Request File</a></p>
"""
        
        if rel_response_file:
            html_content += f"""
                    <p><strong>Response Details:</strong> <a href="{rel_response_file}" class="evidence-link" target="_blank">View Response File</a></p>
"""
        
        html_content += """
                </div>
            </div>
        </div>
"""
    
    # Add JavaScript for interactivity
    html_content += """
        <script>
            function toggleVulnerability(id) {
                const vuln = document.getElementById(id);
                vuln.classList.toggle('active');
            }
            
            function showTab(tabId, tabElement) {
                // Hide all tab contents
                const tabContents = tabElement.parentElement.parentElement.querySelectorAll('.tab-content');
                tabContents.forEach(tab => tab.classList.remove('active'));
                
                // Deactivate all tabs
                const tabs = tabElement.parentElement.querySelectorAll('.tab');
                tabs.forEach(tab => tab.classList.remove('active'));
                
                // Activate selected tab and content
                document.getElementById(tabId).classList.add('active');
                tabElement.classList.add('active');
            }
            
            // Open the first vulnerability by default
            document.addEventListener('DOMContentLoaded', function() {
                const firstVuln = document.querySelector('.vulnerability');
                if (firstVuln) {
                    firstVuln.classList.add('active');
                }
            });
        </script>
    </div>
</body>
</html>
"""
    
    return html_content

def save_html_report(report_data, output_dir, filename=None):
    """
    Generate and save an HTML report.
    
    Args:
        report_data (dict): The report data
        output_dir (str): Directory to save the report
        filename (str, optional): Filename for the report
        
    Returns:
        str: Path to the saved report
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Generate filename if not provided
    if not filename:
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"vulnerability_report_{timestamp}.html"
    
    # Generate HTML content
    html_content = generate_html_report(report_data, output_dir)
    
    # Save to file
    report_path = os.path.join(output_dir, filename)
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    return report_path
