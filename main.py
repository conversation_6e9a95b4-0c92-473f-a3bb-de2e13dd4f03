import logging
import asyncio
import signal
from typing import List, Dict, Any

from config import config
from scanner.scanner import WebScanner
from scanner.crawler import WebCrawler
from scanner.vulnerability import VulnerabilityScanner

logger = logging.getLogger(__name__)

def signal_handler(signum, frame):
    """Handle system signals for graceful shutdown"""
    logger.info("Received shutdown signal. Cleaning up...")
    cleanup_resources()
    sys.exit(0)

def cleanup_resources():
    """Clean up all resources"""
    try:
        # Add cleanup logic here
        logger.info("Resources cleaned up successfully")
    except Exception as e:
        logger.error(f"Error during cleanup: {str(e)}")

def main():
    """Main entry point for the vulnerability scanner"""
    try:
        # Register signal handlers
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # Initialize components
        scanner = WebScanner()
        crawler = WebCrawler()
        vuln_scanner = VulnerabilityScanner()
        
        # Initialize all components
        scanner.initialize()
        crawler.initialize()
        vuln_scanner.initialize()
        
        # Start scanning
        start_url = input("Enter the target URL: ")
        
        # Crawl the website
        logger.info("Starting website crawling...")
        vulnerabilities = asyncio.run(crawler.crawl(start_url))
        
        # Scan for vulnerabilities
        logger.info("Starting vulnerability scanning...")
        vulnerabilities.extend(vuln_scanner.scan_security_headers(start_url))
        
        # Generate report
        logger.info("Generating vulnerability report...")
        vuln_scanner.generate_report(vulnerabilities)
        
        logger.info("Scanning completed successfully")
        
    except KeyboardInterrupt:
        logger.info("Scan interrupted by user")
    except Exception as e:
        logger.error(f"An error occurred: {str(e)}")
    finally:
        cleanup_resources()

if __name__ == "__main__":
    main()
