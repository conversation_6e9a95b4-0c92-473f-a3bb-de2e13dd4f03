2025-05-20 15:32:42.777 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vscode.vscode-selfhost-test-provider' wants API proposal 'attributableCoverage' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:32:42.778 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.python' wants API proposal 'contribIssueReporter' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:32:42.778 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'github.vscode-pull-request-github' wants API proposal 'fileComments' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:32:42.779 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'github.copilot-chat' wants API proposal 'chatVariableResolver' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:32:42.780 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'github.copilot-chat' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:32:42.780 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.debugpy' wants API proposal 'contribIssueReporter' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:32:42.781 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'ms-azuretools.vscode-azure-github-copilot' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:32:42.781 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vscode.cpptools' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:32:42.782 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'redhat.java' wants API proposal 'documentPaste' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:32:42.782 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'vscjava.vscode-java-pack' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:32:43.297 [warning] [Window] IWorkbenchContributionsRegistry#getContribution('windsurf.cascadePanel'): contribution instantiated before LifecyclePhase.Restored!
2025-05-20 15:32:43.487 [info] [Views] Added views:windsurf.cascadePanel in windsurf.cascadeViewContainerId
2025-05-20 15:32:43.488 [info] [Views] Added views:outline in workbench.view.explorer
2025-05-20 15:32:43.488 [info] [Views] Added views:workbench.view.search in workbench.view.search
2025-05-20 15:32:43.488 [info] [Views] Added views:workbench.scm in workbench.view.scm
2025-05-20 15:32:43.488 [info] [Views] Added views:workbench.panel.markers.view in workbench.panel.markers
2025-05-20 15:32:43.488 [info] [Views] Added views:workbench.panel.output in workbench.panel.output
2025-05-20 15:32:43.488 [info] [Views] Added views:terminal in terminal
2025-05-20 15:32:43.488 [info] [Views] Added views:timeline in workbench.view.explorer
2025-05-20 15:32:43.488 [info] [Views] Added views:workbench.explorer.fileView in workbench.view.explorer
2025-05-20 15:32:43.855 [info] [Window] Started local extension host with pid 3560.
2025-05-20 15:32:44.348 [info] [Views] Added views:workbench.debug.variablesView,workbench.debug.watchExpressionsView,workbench.debug.callStackView,workbench.debug.breakPointsView in workbench.view.debug
2025-05-20 15:32:45.086 [error] [Window] Extension 'ms-python.python' appears in product.json but enables LESS API proposals than the extension wants.
package.json (LOSES): contribEditorContentMenu, quickPickSortByLabel, testObserver, quickPickItemTooltip, terminalDataWriteEvent, terminalExecuteCommandEvent, codeActionAI, notebookReplDocument, notebookVariableProvider
product.json (WINS): contribEditorContentMenu, quickPickSortByLabel, portsAttributes, testObserver, quickPickItemTooltip, terminalDataWriteEvent, terminalExecuteCommandEvent, notebookReplDocument
2025-05-20 15:32:45.172 [warning] [Window] [ms-python.python]: Cannot register 'python.venvPath'. This property is already registered.
2025-05-20 15:32:45.227 [info] [Views] Added views:windsurfDevContainers in workbench.view.remote
2025-05-20 15:32:45.227 [info] [Views] Added views:windsurfSSHHosts in workbench.view.remote
2025-05-20 15:32:45.228 [info] [Views] Added views:windsurfWslTargets in workbench.view.remote
2025-05-20 15:32:45.836 [info] [Extension Host] Extension host with pid 3560 started
2025-05-20 15:32:45.836 [info] [Extension Host] Skipping acquiring lock for c:\Users\<USER>\AppData\Roaming\Windsurf\User\workspaceStorage\8733121f5ef91815a7b30be6ec2a6e15.
2025-05-20 15:32:46.495 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-05-20 15:32:46.528 [info] [Views] Added views:workbench.panel.repl.view in workbench.panel.repl
2025-05-20 15:32:46.571 [info] [Extension Host] ExtensionService#_doActivateExtension ms-python.python, startup: false, activationEvent: 'onLanguage:python', root cause: Codeium.windsurfPyright
2025-05-20 15:32:47.183 [info] [Views] Added views:~remote.forwardedPorts in ~remote.forwardedPortsContainer
2025-05-20 15:32:47.402 [error] [Window] App icon customization is not supported on this OS: Error: App icon customization is not supported on this OS
    at z2.setIcon (file:///D:/Windsurf/resources/app/out/main.js:87:3056)
    at Object.call (file:///D:/Windsurf/resources/app/out/main.js:33:4564)
    at Cf.s (file:///D:/Windsurf/resources/app/out/main.js:31:20114)
    at Cf.q (file:///D:/Windsurf/resources/app/out/main.js:31:19637)
    at Qo.value (file:///D:/Windsurf/resources/app/out/main.js:31:19039)
    at D.B (file:///D:/Windsurf/resources/app/out/main.js:30:2373)
    at D.C (file:///D:/Windsurf/resources/app/out/main.js:30:2443)
    at D.fire (file:///D:/Windsurf/resources/app/out/main.js:30:2660)
    at Qo.value (file:///D:/Windsurf/resources/app/out/main.js:28:4898)
    at D.B (file:///D:/Windsurf/resources/app/out/main.js:30:2373)
    at D.fire (file:///D:/Windsurf/resources/app/out/main.js:30:2591)
    at Qo.value (file:///D:/Windsurf/resources/app/out/main.js:28:5086)
    at D.B (file:///D:/Windsurf/resources/app/out/main.js:30:2373)
    at D.fire (file:///D:/Windsurf/resources/app/out/main.js:30:2591)
    at F (file:///D:/Windsurf/resources/app/out/main.js:28:7373)
    at IpcMainImpl.i (file:///D:/Windsurf/resources/app/out/main.js:33:21024)
    at IpcMainImpl.emit (node:events:518:28)
    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:87043)
    at WebContents.emit (node:events:518:28)
2025-05-20 15:32:47.417 [error] [Remote Tunnel Service] Missing 'tunnelApplicationConfig' or 'tunnelApplicationName' in product.json. Remote tunneling is not available.
2025-05-20 15:32:47.463 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-05-20 15:32:47.523 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:jsonc'
2025-05-20 15:32:47.548 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:jsonc'
2025-05-20 15:32:47.713 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.typescript-language-features, startup: false, activationEvent: 'onLanguage:jsonc'
2025-05-20 15:32:48.103 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-05-20 15:32:48.232 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-05-20 15:32:48.307 [info] [Views] Added views:workbench.view.testing in workbench.view.extension.test
2025-05-20 15:32:48.518 [info] [Extension Host] ExtensionService#_doActivateExtension Codeium.windsurfPyright, startup: false, activationEvent: 'onLanguage:python'
2025-05-20 15:32:48.647 [info] [Extension Host] ExtensionService#_doActivateExtension ms-python.debugpy, startup: false, activationEvent: 'onLanguage:python'
2025-05-20 15:32:49.226 [info] [Git] [main] Log level: Info
2025-05-20 15:32:49.227 [info] [Git] [main] Validating found git in: "C:\Program Files\Git\cmd\git.exe"
2025-05-20 15:32:49.227 [info] [Git] [main] Validating found git in: "C:\Program Files (x86)\Git\cmd\git.exe"
2025-05-20 15:32:49.227 [info] [Git] [main] Validating found git in: "C:\Program Files\Git\cmd\git.exe"
2025-05-20 15:32:49.227 [info] [Git] [main] Validating found git in: "C:\Users\<USER>\AppData\Local\Programs\Git\cmd\git.exe"
2025-05-20 15:32:49.228 [info] [GitHub] Log level: Info
2025-05-20 15:32:49.350 [info] [Git] [main] Validating found git in: "D:\Git\cmd\git.exe"
2025-05-20 15:32:49.729 [info] [Git] [main] Using git "2.47.1.windows.2" from "D:\Git\cmd\git.exe"
2025-05-20 15:32:49.729 [info] [Git] [Model][doInitialScan] Initial repository scan started
2025-05-20 15:32:49.796 [info] [Extension Host] ExtensionService#_doActivateExtension codeium.windsurf, startup: true, activationEvent: '*'
2025-05-20 15:32:50.386 [info] [Window] [perf] Render performance baseline is 81ms
2025-05-20 15:32:51.261 [info] [Git] > git rev-parse --show-toplevel [1488ms]
2025-05-20 15:32:51.261 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:32:51.626 [info] [Git] > git rev-parse --show-toplevel [302ms]
2025-05-20 15:32:51.626 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:32:51.852 [info] [Git] > git rev-parse --show-toplevel [166ms]
2025-05-20 15:32:51.852 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:32:52.005 [info] [Git] > git rev-parse --show-toplevel [143ms]
2025-05-20 15:32:52.005 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:32:52.178 [info] [Git] > git rev-parse --show-toplevel [164ms]
2025-05-20 15:32:52.178 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:32:52.350 [info] [Git] > git rev-parse --show-toplevel [161ms]
2025-05-20 15:32:52.350 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:32:52.511 [info] [Git] > git rev-parse --show-toplevel [152ms]
2025-05-20 15:32:52.511 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:32:52.671 [info] [Git] > git rev-parse --show-toplevel [150ms]
2025-05-20 15:32:52.671 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:32:52.838 [info] [Git] > git rev-parse --show-toplevel [157ms]
2025-05-20 15:32:52.838 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:32:53.010 [info] [Git] > git rev-parse --show-toplevel [161ms]
2025-05-20 15:32:53.010 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:32:53.016 [info] [Git] [Model][doInitialScan] Initial repository scan completed - repositories (0), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-05-20 15:32:57.339 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-05-20 15:32:57.346 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-05-20 15:32:57.407 [info] [Extension Host] ExtensionService#_doActivateExtension Codeium.windsurf-dev-containers, startup: false, activationEvent: 'onStartupFinished'
2025-05-20 15:33:01.425 [info] [Extension Host] Eager extensions activated
2025-05-20 15:33:07.122 [error] [Window] [Extension Host] Error during login process: ConnectError: [unauthenticated] api server wire error: invalid api key
	at c (d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:3762359)
	at l (d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:3762755)
	at next (d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:3768977)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:3366440
	at async Object.unary (d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:3767945)
	at async Object.getUserStatus (d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:3755593)
	at async v (d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:2282985)
	at async t.initializeAuthSession (d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:2282207)
2025-05-20 15:33:27.699 [warning] [Window] Overlapping semantic tokens detected at lineNumber 11, column 17
2025-05-20 15:33:28.289 [info] [Extension Host] ExtensionService#_doActivateExtension codeium.windsurf-remote-openssh, startup: false, activationEvent: 'onCommand:windsurf-remote-openssh.newWindow'
2025-05-20 15:33:34.863 [info] [Extension Host] Extension host terminating: renderer closed the MessagePort
2025-05-20 15:33:34.932 [info] [Extension Host] Extension host with pid 3560 exiting with code 0
2025-05-20 15:33:35.032 [info] [Views] Removed views:workbench.view.testing from workbench.view.extension.test
2025-05-20 15:33:35.832 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vscode.vscode-selfhost-test-provider' wants API proposal 'attributableCoverage' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:33:35.833 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.python' wants API proposal 'contribIssueReporter' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:33:35.834 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'github.vscode-pull-request-github' wants API proposal 'fileComments' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:33:35.834 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'github.copilot-chat' wants API proposal 'chatVariableResolver' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:33:35.834 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'github.copilot-chat' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:33:35.835 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.debugpy' wants API proposal 'contribIssueReporter' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:33:35.835 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'ms-azuretools.vscode-azure-github-copilot' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:33:35.835 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vscode.cpptools' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:33:35.836 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'redhat.java' wants API proposal 'documentPaste' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:33:35.836 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'vscjava.vscode-java-pack' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:33:36.160 [warning] [Window] IWorkbenchContributionsRegistry#getContribution('windsurf.cascadePanel'): contribution instantiated before LifecyclePhase.Restored!
2025-05-20 15:33:36.257 [info] [Views] Added views:windsurf.cascadePanel in windsurf.cascadeViewContainerId
2025-05-20 15:33:36.257 [info] [Views] Added views:outline in workbench.view.explorer
2025-05-20 15:33:36.258 [info] [Views] Added views:workbench.view.search in workbench.view.search
2025-05-20 15:33:36.258 [info] [Views] Added views:workbench.scm in workbench.view.scm
2025-05-20 15:33:36.258 [info] [Views] Added views:workbench.panel.markers.view in workbench.panel.markers
2025-05-20 15:33:36.258 [info] [Views] Added views:workbench.panel.output in workbench.panel.output
2025-05-20 15:33:36.258 [info] [Views] Added views:terminal in terminal
2025-05-20 15:33:36.258 [info] [Views] Added views:timeline in workbench.view.explorer
2025-05-20 15:33:36.258 [info] [Views] Added views:workbench.explorer.fileView in workbench.view.explorer
2025-05-20 15:33:36.780 [info] [Window] Started local extension host with pid 9140.
2025-05-20 15:33:36.860 [info] [Views] Added views:workbench.debug.variablesView,workbench.debug.watchExpressionsView,workbench.debug.callStackView,workbench.debug.breakPointsView in workbench.view.debug
2025-05-20 15:33:37.494 [error] [Window] Extension 'ms-python.python' appears in product.json but enables LESS API proposals than the extension wants.
package.json (LOSES): contribEditorContentMenu, quickPickSortByLabel, testObserver, quickPickItemTooltip, terminalDataWriteEvent, terminalExecuteCommandEvent, codeActionAI, notebookReplDocument, notebookVariableProvider
product.json (WINS): contribEditorContentMenu, quickPickSortByLabel, portsAttributes, testObserver, quickPickItemTooltip, terminalDataWriteEvent, terminalExecuteCommandEvent, notebookReplDocument
2025-05-20 15:33:37.549 [warning] [Window] [ms-python.python]: Cannot register 'python.venvPath'. This property is already registered.
2025-05-20 15:33:37.584 [info] [Views] Added views:windsurfDevContainers in workbench.view.remote
2025-05-20 15:33:37.585 [info] [Views] Added views:windsurfSSHHosts in workbench.view.remote
2025-05-20 15:33:37.586 [info] [Views] Added views:windsurfWslTargets in workbench.view.remote
2025-05-20 15:33:37.896 [info] [Extension Host] Extension host with pid 9140 started
2025-05-20 15:33:37.896 [info] [Extension Host] Skipping acquiring lock for c:\Users\<USER>\AppData\Roaming\Windsurf\User\workspaceStorage\8733121f5ef91815a7b30be6ec2a6e15.
2025-05-20 15:33:38.188 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-05-20 15:33:38.192 [info] [Views] Added views:workbench.panel.repl.view in workbench.panel.repl
2025-05-20 15:33:38.245 [info] [Extension Host] ExtensionService#_doActivateExtension ms-python.python, startup: false, activationEvent: 'onLanguage:python', root cause: Codeium.windsurfPyright
2025-05-20 15:33:38.849 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:jsonc'
2025-05-20 15:33:38.889 [info] [Views] Added views:~remote.forwardedPorts in ~remote.forwardedPortsContainer
2025-05-20 15:33:38.892 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:jsonc'
2025-05-20 15:33:38.971 [error] [Window] App icon customization is not supported on this OS: Error: App icon customization is not supported on this OS
    at z2.setIcon (file:///D:/Windsurf/resources/app/out/main.js:87:3056)
    at Object.call (file:///D:/Windsurf/resources/app/out/main.js:33:4564)
    at Cf.s (file:///D:/Windsurf/resources/app/out/main.js:31:20114)
    at Cf.q (file:///D:/Windsurf/resources/app/out/main.js:31:19637)
    at Qo.value (file:///D:/Windsurf/resources/app/out/main.js:31:19039)
    at D.B (file:///D:/Windsurf/resources/app/out/main.js:30:2373)
    at D.C (file:///D:/Windsurf/resources/app/out/main.js:30:2443)
    at D.fire (file:///D:/Windsurf/resources/app/out/main.js:30:2660)
    at Qo.value (file:///D:/Windsurf/resources/app/out/main.js:28:4898)
    at D.B (file:///D:/Windsurf/resources/app/out/main.js:30:2373)
    at D.fire (file:///D:/Windsurf/resources/app/out/main.js:30:2591)
    at Qo.value (file:///D:/Windsurf/resources/app/out/main.js:28:5086)
    at D.B (file:///D:/Windsurf/resources/app/out/main.js:30:2373)
    at D.fire (file:///D:/Windsurf/resources/app/out/main.js:30:2591)
    at F (file:///D:/Windsurf/resources/app/out/main.js:28:7373)
    at IpcMainImpl.i (file:///D:/Windsurf/resources/app/out/main.js:33:21024)
    at IpcMainImpl.emit (node:events:518:28)
    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:87043)
    at WebContents.emit (node:events:518:28)
2025-05-20 15:33:38.984 [error] [Remote Tunnel Service] Missing 'tunnelApplicationConfig' or 'tunnelApplicationName' in product.json. Remote tunneling is not available.
2025-05-20 15:33:39.055 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.typescript-language-features, startup: false, activationEvent: 'onLanguage:jsonc'
2025-05-20 15:33:39.441 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-05-20 15:33:39.583 [info] [Views] Added views:workbench.view.testing in workbench.view.extension.test
2025-05-20 15:33:39.664 [info] [Extension Host] ExtensionService#_doActivateExtension Codeium.windsurfPyright, startup: false, activationEvent: 'onLanguage:python'
2025-05-20 15:33:39.765 [info] [Extension Host] ExtensionService#_doActivateExtension ms-python.debugpy, startup: false, activationEvent: 'onLanguage:python'
2025-05-20 15:33:39.926 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-05-20 15:33:39.970 [info] [Window] [perf] Render performance baseline is 78ms
2025-05-20 15:33:40.061 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-05-20 15:33:40.545 [info] [Git] [main] Log level: Info
2025-05-20 15:33:40.545 [info] [Git] [main] Validating found git in: "C:\Program Files\Git\cmd\git.exe"
2025-05-20 15:33:40.545 [info] [Git] [main] Validating found git in: "C:\Program Files (x86)\Git\cmd\git.exe"
2025-05-20 15:33:40.545 [info] [Git] [main] Validating found git in: "C:\Program Files\Git\cmd\git.exe"
2025-05-20 15:33:40.545 [info] [Git] [main] Validating found git in: "C:\Users\<USER>\AppData\Local\Programs\Git\cmd\git.exe"
2025-05-20 15:33:40.546 [info] [GitHub] Log level: Info
2025-05-20 15:33:40.653 [info] [Git] [main] Validating found git in: "D:\Git\cmd\git.exe"
2025-05-20 15:33:40.834 [info] [Git] [main] Using git "2.47.1.windows.2" from "D:\Git\cmd\git.exe"
2025-05-20 15:33:40.834 [info] [Git] [Model][doInitialScan] Initial repository scan started
2025-05-20 15:33:40.881 [info] [Extension Host] ExtensionService#_doActivateExtension codeium.windsurf, startup: true, activationEvent: '*'
2025-05-20 15:33:42.075 [info] [Git] > git rev-parse --show-toplevel [1202ms]
2025-05-20 15:33:42.075 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:33:42.244 [info] [Git] > git rev-parse --show-toplevel [147ms]
2025-05-20 15:33:42.244 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:33:42.484 [info] [Git] > git rev-parse --show-toplevel [166ms]
2025-05-20 15:33:42.484 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:33:42.655 [info] [Git] > git rev-parse --show-toplevel [160ms]
2025-05-20 15:33:42.655 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:33:42.796 [info] [Git] > git rev-parse --show-toplevel [131ms]
2025-05-20 15:33:42.796 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:33:42.925 [info] [Git] > git rev-parse --show-toplevel [119ms]
2025-05-20 15:33:42.925 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:33:43.063 [info] [Git] > git rev-parse --show-toplevel [128ms]
2025-05-20 15:33:43.063 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:33:43.285 [info] [Git] > git rev-parse --show-toplevel [212ms]
2025-05-20 15:33:43.286 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:33:43.611 [info] [Git] > git rev-parse --show-toplevel [300ms]
2025-05-20 15:33:43.611 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:33:43.784 [info] [Git] > git rev-parse --show-toplevel [162ms]
2025-05-20 15:33:43.784 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:33:43.790 [info] [Git] [Model][doInitialScan] Initial repository scan completed - repositories (0), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-05-20 15:33:49.218 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-05-20 15:33:49.225 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-05-20 15:33:49.295 [info] [Extension Host] ExtensionService#_doActivateExtension Codeium.windsurf-dev-containers, startup: false, activationEvent: 'onStartupFinished'
2025-05-20 15:33:49.636 [info] [Extension Host] Eager extensions activated
2025-05-20 15:33:57.476 [error] [Window] [Extension Host] Error during login process: ConnectError: [unauthenticated] api server wire error: invalid api key
	at c (d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:3762359)
	at l (d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:3762755)
	at next (d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:3768977)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:3366440
	at async Object.unary (d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:3767945)
	at async Object.getUserStatus (d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:3755593)
	at async v (d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:2282985)
	at async t.initializeAuthSession (d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:2282207)
2025-05-20 15:34:20.824 [warning] [Window] Overlapping semantic tokens detected at lineNumber 11, column 17
2025-05-20 15:35:14.232 [info] [Extension Host] Extension host terminating: renderer closed the MessagePort
2025-05-20 15:35:14.311 [info] [Extension Host] Extension host with pid 9140 exiting with code 0
2025-05-20 15:35:14.381 [info] [Views] Removed views:workbench.view.testing from workbench.view.extension.test
2025-05-20 15:35:15.218 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vscode.vscode-selfhost-test-provider' wants API proposal 'attributableCoverage' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:35:15.218 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.python' wants API proposal 'contribIssueReporter' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:35:15.219 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'github.vscode-pull-request-github' wants API proposal 'fileComments' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:35:15.219 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'github.copilot-chat' wants API proposal 'chatVariableResolver' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:35:15.220 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'github.copilot-chat' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:35:15.221 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'ms-python.debugpy' wants API proposal 'contribIssueReporter' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:35:15.221 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'ms-azuretools.vscode-azure-github-copilot' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:35:15.222 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'ms-vscode.cpptools' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:35:15.223 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'redhat.java' wants API proposal 'documentPaste' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:35:15.223 [warning] [Window] Via 'product.json#extensionEnabledApiProposals' extension 'vscjava.vscode-java-pack' wants API proposal 'lmTools' but that proposal DOES NOT EXIST. Likely, the proposal has been finalized (check 'vscode.d.ts') or was abandoned.
2025-05-20 15:35:15.623 [warning] [Window] IWorkbenchContributionsRegistry#getContribution('windsurf.cascadePanel'): contribution instantiated before LifecyclePhase.Restored!
2025-05-20 15:35:15.735 [info] [Views] Added views:windsurf.cascadePanel in windsurf.cascadeViewContainerId
2025-05-20 15:35:15.736 [info] [Views] Added views:outline in workbench.view.explorer
2025-05-20 15:35:15.736 [info] [Views] Added views:workbench.view.search in workbench.view.search
2025-05-20 15:35:15.736 [info] [Views] Added views:workbench.scm in workbench.view.scm
2025-05-20 15:35:15.736 [info] [Views] Added views:workbench.panel.markers.view in workbench.panel.markers
2025-05-20 15:35:15.736 [info] [Views] Added views:workbench.panel.output in workbench.panel.output
2025-05-20 15:35:15.736 [info] [Views] Added views:terminal in terminal
2025-05-20 15:35:15.736 [info] [Views] Added views:timeline in workbench.view.explorer
2025-05-20 15:35:15.736 [info] [Views] Added views:workbench.explorer.fileView in workbench.view.explorer
2025-05-20 15:35:16.230 [info] [Window] Started local extension host with pid 1072.
2025-05-20 15:35:16.317 [info] [Views] Added views:workbench.debug.variablesView,workbench.debug.watchExpressionsView,workbench.debug.callStackView,workbench.debug.breakPointsView in workbench.view.debug
2025-05-20 15:35:16.970 [error] [Window] Extension 'ms-python.python' appears in product.json but enables LESS API proposals than the extension wants.
package.json (LOSES): contribEditorContentMenu, quickPickSortByLabel, testObserver, quickPickItemTooltip, terminalDataWriteEvent, terminalExecuteCommandEvent, codeActionAI, notebookReplDocument, notebookVariableProvider
product.json (WINS): contribEditorContentMenu, quickPickSortByLabel, portsAttributes, testObserver, quickPickItemTooltip, terminalDataWriteEvent, terminalExecuteCommandEvent, notebookReplDocument
2025-05-20 15:35:17.023 [warning] [Window] [ms-python.python]: Cannot register 'python.venvPath'. This property is already registered.
2025-05-20 15:35:17.062 [info] [Views] Added views:windsurfDevContainers in workbench.view.remote
2025-05-20 15:35:17.062 [info] [Views] Added views:windsurfSSHHosts in workbench.view.remote
2025-05-20 15:35:17.063 [info] [Views] Added views:windsurfWslTargets in workbench.view.remote
2025-05-20 15:35:17.375 [info] [Extension Host] Extension host with pid 1072 started
2025-05-20 15:35:17.375 [info] [Extension Host] Skipping acquiring lock for c:\Users\<USER>\AppData\Roaming\Windsurf\User\workspaceStorage\8733121f5ef91815a7b30be6ec2a6e15.
2025-05-20 15:35:17.685 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.emmet, startup: false, activationEvent: 'onLanguage'
2025-05-20 15:35:17.688 [info] [Views] Added views:workbench.panel.repl.view in workbench.panel.repl
2025-05-20 15:35:17.742 [info] [Extension Host] ExtensionService#_doActivateExtension ms-python.python, startup: false, activationEvent: 'onLanguage:python', root cause: Codeium.windsurfPyright
2025-05-20 15:35:18.320 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.configuration-editing, startup: false, activationEvent: 'onLanguage:jsonc'
2025-05-20 15:35:18.339 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.json-language-features, startup: false, activationEvent: 'onLanguage:jsonc'
2025-05-20 15:35:18.395 [info] [Views] Added views:~remote.forwardedPorts in ~remote.forwardedPortsContainer
2025-05-20 15:35:18.482 [error] [Window] App icon customization is not supported on this OS: Error: App icon customization is not supported on this OS
    at z2.setIcon (file:///D:/Windsurf/resources/app/out/main.js:87:3056)
    at Object.call (file:///D:/Windsurf/resources/app/out/main.js:33:4564)
    at Cf.s (file:///D:/Windsurf/resources/app/out/main.js:31:20114)
    at Cf.q (file:///D:/Windsurf/resources/app/out/main.js:31:19637)
    at Qo.value (file:///D:/Windsurf/resources/app/out/main.js:31:19039)
    at D.B (file:///D:/Windsurf/resources/app/out/main.js:30:2373)
    at D.C (file:///D:/Windsurf/resources/app/out/main.js:30:2443)
    at D.fire (file:///D:/Windsurf/resources/app/out/main.js:30:2660)
    at Qo.value (file:///D:/Windsurf/resources/app/out/main.js:28:4898)
    at D.B (file:///D:/Windsurf/resources/app/out/main.js:30:2373)
    at D.fire (file:///D:/Windsurf/resources/app/out/main.js:30:2591)
    at Qo.value (file:///D:/Windsurf/resources/app/out/main.js:28:5086)
    at D.B (file:///D:/Windsurf/resources/app/out/main.js:30:2373)
    at D.fire (file:///D:/Windsurf/resources/app/out/main.js:30:2591)
    at F (file:///D:/Windsurf/resources/app/out/main.js:28:7373)
    at IpcMainImpl.i (file:///D:/Windsurf/resources/app/out/main.js:33:21024)
    at IpcMainImpl.emit (node:events:518:28)
    at WebContents.<anonymous> (node:electron/js2c/browser_init:2:87043)
    at WebContents.emit (node:events:518:28)
2025-05-20 15:35:18.492 [error] [Remote Tunnel Service] Missing 'tunnelApplicationConfig' or 'tunnelApplicationName' in product.json. Remote tunneling is not available.
2025-05-20 15:35:18.499 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.typescript-language-features, startup: false, activationEvent: 'onLanguage:jsonc'
2025-05-20 15:35:18.999 [info] [Views] Added views:workbench.view.testing in workbench.view.extension.test
2025-05-20 15:35:19.041 [info] [Extension Host] ExtensionService#_doActivateExtension Codeium.windsurfPyright, startup: false, activationEvent: 'onLanguage:python'
2025-05-20 15:35:19.120 [info] [Extension Host] ExtensionService#_doActivateExtension ms-python.debugpy, startup: false, activationEvent: 'onLanguage:python'
2025-05-20 15:35:19.234 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.git-base, startup: true, activationEvent: '*', root cause: vscode.git
2025-05-20 15:35:19.408 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.git, startup: true, activationEvent: '*'
2025-05-20 15:35:19.556 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.github, startup: true, activationEvent: '*'
2025-05-20 15:35:19.636 [info] [Window] [perf] Render performance baseline is 85ms
2025-05-20 15:35:20.006 [info] [Git] [main] Log level: Info
2025-05-20 15:35:20.006 [info] [Git] [main] Validating found git in: "C:\Program Files\Git\cmd\git.exe"
2025-05-20 15:35:20.006 [info] [Git] [main] Validating found git in: "C:\Program Files (x86)\Git\cmd\git.exe"
2025-05-20 15:35:20.006 [info] [Git] [main] Validating found git in: "C:\Program Files\Git\cmd\git.exe"
2025-05-20 15:35:20.006 [info] [Git] [main] Validating found git in: "C:\Users\<USER>\AppData\Local\Programs\Git\cmd\git.exe"
2025-05-20 15:35:20.008 [info] [GitHub] Log level: Info
2025-05-20 15:35:20.112 [info] [Git] [main] Validating found git in: "D:\Git\cmd\git.exe"
2025-05-20 15:35:20.420 [info] [Git] [main] Using git "2.47.1.windows.2" from "D:\Git\cmd\git.exe"
2025-05-20 15:35:20.420 [info] [Git] [Model][doInitialScan] Initial repository scan started
2025-05-20 15:35:20.482 [info] [Extension Host] ExtensionService#_doActivateExtension codeium.windsurf, startup: true, activationEvent: '*'
2025-05-20 15:35:21.784 [info] [Git] > git rev-parse --show-toplevel [1325ms]
2025-05-20 15:35:21.784 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:35:22.036 [info] [Git] > git rev-parse --show-toplevel [204ms]
2025-05-20 15:35:22.036 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:35:22.232 [info] [Git] > git rev-parse --show-toplevel [142ms]
2025-05-20 15:35:22.232 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:35:22.359 [info] [Git] > git rev-parse --show-toplevel [117ms]
2025-05-20 15:35:22.359 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:35:22.480 [info] [Git] > git rev-parse --show-toplevel [111ms]
2025-05-20 15:35:22.480 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:35:22.611 [info] [Git] > git rev-parse --show-toplevel [121ms]
2025-05-20 15:35:22.611 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:35:22.737 [info] [Git] > git rev-parse --show-toplevel [115ms]
2025-05-20 15:35:22.737 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:35:22.850 [info] [Git] > git rev-parse --show-toplevel [100ms]
2025-05-20 15:35:22.850 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:35:22.972 [info] [Git] > git rev-parse --show-toplevel [113ms]
2025-05-20 15:35:22.972 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:35:23.159 [info] [Git] > git rev-parse --show-toplevel [177ms]
2025-05-20 15:35:23.159 [info] [Git] fatal: not a git repository (or any of the parent directories): .git
2025-05-20 15:35:23.174 [info] [Git] [Model][doInitialScan] Initial repository scan completed - repositories (0), closed repositories (0), parent repositories (0), unsafe repositories (0)
2025-05-20 15:35:28.086 [info] [Extension Host] Eager extensions activated
2025-05-20 15:35:28.199 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.debug-auto-launch, startup: false, activationEvent: 'onStartupFinished'
2025-05-20 15:35:28.207 [info] [Extension Host] ExtensionService#_doActivateExtension vscode.merge-conflict, startup: false, activationEvent: 'onStartupFinished'
2025-05-20 15:35:28.274 [info] [Extension Host] ExtensionService#_doActivateExtension Codeium.windsurf-dev-containers, startup: false, activationEvent: 'onStartupFinished'
2025-05-20 15:35:49.270 [error] [Window] [Extension Host] Error during login process: ConnectError: [unauthenticated] api server wire error: invalid api key
	at c (d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:3762359)
	at l (d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:3762755)
	at next (d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:3768977)
	at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
	at async d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:3366440
	at async Object.unary (d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:3767945)
	at async Object.getUserStatus (d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:3755593)
	at async v (d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:2282985)
	at async t.initializeAuthSession (d:\Windsurf\resources\app\extensions\windsurf\dist\extension.js:2:2282207)
2025-05-20 15:36:01.400 [warning] [Window] Overlapping semantic tokens detected at lineNumber 11, column 17
