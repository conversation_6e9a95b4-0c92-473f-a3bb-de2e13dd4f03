"""
Request and Response Logging Module

This module provides functions to log HTTP requests and responses for vulnerability evidence.
"""

import os
import json
import time
import hashlib
from datetime import datetime
from urllib.parse import urlparse, parse_qs
from colorama import Fore, init

# Initialize colorama
init(autoreset=True)

def log_http_request_response(url, method, headers, params, data, payload, response, vuln_type, vuln_subtype, param_name, evidence_dir):
    """
    Log HTTP request and response details for vulnerability evidence.

    This function:
    1. Creates structured directories for evidence
    2. Saves request details to a file
    3. Saves response details to a file
    4. Returns paths to the saved files

    Args:
        url (str): The URL of the request
        method (str): HTTP method (GET, POST, etc.)
        headers (dict): Request headers
        params (dict): URL parameters
        data (dict): POST data
        payload (str): The payload used for testing
        response (requests.Response): The HTTP response object
        vuln_type (str): Type of vulnerability (e.g., 'SQLi', 'XSS')
        vuln_subtype (str): Subtype of vulnerability (e.g., 'Error-Based', 'Reflected')
        param_name (str): Name of the parameter being tested
        evidence_dir (str): Base directory for evidence

    Returns:
        tuple: (request_file_path, response_file_path)
    """
    try:
        # Create timestamp and unique ID
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        url_hash = hashlib.md5(url.encode()).hexdigest()[:8]
        vuln_id = f"{vuln_type.lower()}_{param_name}_{url_hash}_{timestamp}"

        # Create directories
        requests_dir = os.path.join(evidence_dir, "requests")
        responses_dir = os.path.join(evidence_dir, "responses")

        os.makedirs(requests_dir, exist_ok=True)
        os.makedirs(responses_dir, exist_ok=True)

        # Create filenames
        request_filename = f"{vuln_id}_request.txt"
        response_filename = f"{vuln_id}_response.txt"

        request_file_path = os.path.join(requests_dir, request_filename)
        response_file_path = os.path.join(responses_dir, response_filename)

        # Format request details
        request_details = f"=== REQUEST DETAILS ===\n"
        request_details += f"Timestamp: {timestamp}\n"
        request_details += f"Vulnerability Type: {vuln_type}\n"
        request_details += f"Vulnerability Subtype: {vuln_subtype}\n"
        request_details += f"Parameter Name: {param_name}\n"
        request_details += f"Payload: {payload}\n\n"

        request_details += f"URL: {url}\n"
        request_details += f"Method: {method}\n"
        request_details += f"Headers:\n"
        for header, value in headers.items():
            request_details += f"  {header}: {value}\n"

        request_details += f"\nParameters:\n"
        if params:
            for param, value in params.items():
                request_details += f"  {param}: {value}\n"
        else:
            request_details += f"  None\n"

        request_details += f"\nData:\n"
        if data:
            if isinstance(data, dict):
                for key, value in data.items():
                    request_details += f"  {key}: {value}\n"
            else:
                request_details += f"  {data}\n"
        else:
            request_details += f"  None\n"

        # Format response details
        response_details = f"=== RESPONSE DETAILS ===\n"
        response_details += f"Timestamp: {timestamp}\n"
        response_details += f"Status Code: {response.status_code}\n"
        response_details += f"Reason: {response.reason}\n"
        response_details += f"Response Time: {response.elapsed.total_seconds()} seconds\n\n"

        response_details += f"Headers:\n"
        for header, value in response.headers.items():
            response_details += f"  {header}: {value}\n"

        response_details += f"\nContent Type: {response.headers.get('Content-Type', 'Unknown')}\n"
        response_details += f"Content Length: {len(response.content)} bytes\n\n"

        # Add content sample (limit to avoid huge files)
        content_sample = response.text[:5000]
        if len(response.text) > 5000:
            content_sample += "\n... (content truncated) ..."

        response_details += f"Content Sample:\n{content_sample}\n"

        # Save files
        with open(request_file_path, 'w', encoding='utf-8') as f:
            f.write(request_details)

        with open(response_file_path, 'w', encoding='utf-8') as f:
            f.write(response_details)

        print(Fore.GREEN + f"[+] Request details saved: {request_file_path}")
        print(Fore.GREEN + f"[+] Response details saved: {response_file_path}")

        return request_file_path, response_file_path

    except Exception as e:
        print(Fore.RED + f"[-] Error logging HTTP request/response: {str(e)}")
        return None, None

def generate_vulnerability_summary(vulnerabilities, url):
    """
    Generate a comprehensive summary table of vulnerabilities for a URL.

    Args:
        vulnerabilities (list): List of vulnerability dictionaries
        url (str): The URL that was scanned

    Returns:
        str: Formatted summary table with detailed vulnerability information
    """
    if not vulnerabilities:
        return f"\n{Fore.YELLOW}[!] No vulnerabilities found for {url}\n"

    summary = f"\n{Fore.CYAN}[*] ======== VULNERABILITY SUMMARY FOR {url} ========\n\n"

    # Group vulnerabilities by type
    vuln_types = {}
    for vuln in vulnerabilities:
        vuln_type = vuln.get('type', vuln.get('prediction', 'Unknown'))
        if vuln_type not in vuln_types:
            vuln_types[vuln_type] = []
        vuln_types[vuln_type].append(vuln)

    # Summary statistics
    summary += f"{Fore.CYAN}[*] Total vulnerabilities: {len(vulnerabilities)}\n"
    summary += f"{Fore.CYAN}[*] Vulnerability types found: {len(vuln_types)}\n\n"

    # Severity distribution
    severity_counts = {"Critical": 0, "High": 0, "Medium": 0, "Low": 0, "Info": 0, "Unknown": 0}
    for vuln in vulnerabilities:
        severity = vuln.get('severity', 'Unknown')
        severity_counts[severity] = severity_counts.get(severity, 0) + 1

    summary += f"{Fore.CYAN}[*] Severity distribution:\n"
    for severity, count in severity_counts.items():
        if count > 0:
            severity_color = Fore.RED if severity in ["Critical", "High"] else (
                Fore.YELLOW if severity == "Medium" else (
                    Fore.GREEN if severity == "Low" else Fore.WHITE
                )
            )
            summary += f"   {severity_color}{severity}: {count}\n"

    summary += "\n"

    # Create detailed table for each vulnerability type
    for vuln_type, vulns in vuln_types.items():
        # Set color based on vulnerability type
        type_color = Fore.RED if vuln_type in ["SQLi", "Command_Injection", "RCE"] else (
            Fore.YELLOW if vuln_type in ["XSS", "CSRF", "SSRF"] else Fore.WHITE
        )

        summary += f"{type_color}[!] {vuln_type.upper()} VULNERABILITIES ({len(vulns)})\n"

        # Create header for this vulnerability type
        header = f"{Fore.CYAN}{'ID':<5} | {'Element/Parameter':<20} | {'Payload':<25} | {'Severity':<10} | {'Evidence':<40}\n"
        header += f"{'-'*5} | {'-'*20} | {'-'*25} | {'-'*10} | {'-'*40}\n"

        summary += header

        # Add each vulnerability of this type
        for i, vuln in enumerate(vulns):
            element_name = vuln.get('element_name', vuln.get('parameter_name', vuln.get('input_name', 'Unknown')))
            payload = vuln.get('payload', 'N/A')
            if len(payload) > 25:
                payload = payload[:22] + "..."

            severity = vuln.get('severity', 'Unknown')
            evidence = vuln.get('evidence', 'No specific evidence')
            if len(evidence) > 40:
                evidence = evidence[:37] + "..."

            # Evidence files
            evidence_files = []
            if vuln.get('screenshot_path'):
                evidence_files.append("Screenshot")
            if vuln.get('request_file'):
                evidence_files.append("Request")
            if vuln.get('response_file'):
                evidence_files.append("Response")
            if vuln.get('form_file'):
                evidence_files.append("Form")

            evidence_str = ", ".join(evidence_files) if evidence_files else "None"

            summary += f"{Fore.WHITE}{i+1:<5} | {element_name:<20} | {payload:<25} | {severity:<10} | {evidence_str:<40}\n"

        # Add details for the first few vulnerabilities of this type
        max_details = min(3, len(vulns))
        if max_details > 0:
            summary += f"\n{Fore.CYAN}[*] Detailed information for the first {max_details} {vuln_type} vulnerabilities:\n\n"

            for i in range(max_details):
                vuln = vulns[i]
                element_name = vuln.get('element_name', vuln.get('parameter_name', vuln.get('input_name', 'Unknown')))
                payload = vuln.get('payload', 'N/A')
                evidence = vuln.get('evidence', 'No specific evidence')

                summary += f"{Fore.WHITE}  {i+1}. {element_name} ({vuln.get('element_type', 'Unknown')})\n"
                summary += f"     Payload: {payload}\n"
                summary += f"     Evidence: {evidence}\n"

                if vuln.get('screenshot_path'):
                    summary += f"     Screenshot: {vuln.get('screenshot_path')}\n"

                if vuln.get('request_file') and vuln.get('response_file'):
                    summary += f"     Request/Response: {vuln.get('request_file')}\n"

                summary += "\n"

        summary += f"{Fore.CYAN}[*] ----------------------------------------\n\n"

    summary += f"{Fore.CYAN}[*] ======== END OF VULNERABILITY SUMMARY ========\n"

    return summary
