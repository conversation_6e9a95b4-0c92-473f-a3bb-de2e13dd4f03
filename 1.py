import os
import pandas as pd

# مسار المجلد الرئيسي الذي يحتوي على ملفات الثغرات
main_folder_path = r'D:\AI\web hacking\github_files1'  # قم بتغيير هذا إلى المسار الصحيح

# قائمة لتخزين البيانات
data = []

# استعراض جميع الملفات في المجلد الرئيسي
for root, _, files in os.walk(main_folder_path):
    for file_name in files:
        # الحصول على المسار الكامل للملف
        file_path = os.path.join(root, file_name)
        
        # تحديد نوع الملف (بناءً على الامتداد)
        file_extension = os.path.splitext(file_name)[1].lower()
        
        # محاولة قراءة الملف كملف نصي
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                # قراءة محتويات الملف بالكامل
                content = file.read()
                if content.strip():  # تجاهل الملفات الفارغة
                    # استخراج اسم الثغرة من اسم الملف فقط (بدون الامتداد)
                    vulnerability_name = os.path.splitext(file_name)[0]
                    
                    # إضافة البيانات إلى القائمة
                    data.append({
                        'vulnerability': vulnerability_name,  # اسم الملف كاسم الثغرة
                        'code': content.strip(),  # محتوى الملف الكامل
                        'file_type': file_extension,  # نوع الملف
                        'file_path': file_path  # مسار الملف
                    })
        except UnicodeDecodeError:
            # إذا كان الملف غير نصي (مثل ملفات ثنائية)، نتجاهله أو نتعامل معه بشكل مختلف
            print(f"تم تجاهل الملف: {file_path} (ملف غير نصي)")
        except Exception as e:
            print(f"خطأ في قراءة الملف: {file_path} - {str(e)}")

# تحويل البيانات إلى DataFrame
df = pd.DataFrame(data)

# حفظ البيانات في ملف CSV
output_csv = 'github_payloads3.csv'
df.to_csv(output_csv, index=False, encoding='utf-8')

print(f"تم حفظ البيانات في ملف {output_csv} بنجاح!")