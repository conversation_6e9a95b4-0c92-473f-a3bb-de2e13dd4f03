from github import Github
import os
import base64
import re
from urllib3.exceptions import ProtocolError
from http.client import IncompleteRead

# مفتاح API الخاص بك (GitHub Token)
GITHUB_TOKEN = '****************************************'

# اسم المستخدم واسم المستودع
REPO_OWNER = 'AkashAswal5'  # اسم المستخدم
REPO_NAME = 'web-attack-list-Payloads'   # اسم المستودع

# تهيئة GitHub API
g = Github(GITHUB_TOKEN)
repo = g.get_repo(f"{REPO_OWNER}/{REPO_NAME}")

# مجلد لحفظ الملفات
output_dir = 'github_files1'
os.makedirs(output_dir, exist_ok=True)

# دالة لتنظيف أسماء الملفات من الأحرف غير الصالحة
def clean_filename(filename):
    # استبدال الأحرف غير المسموح بها بشرطة سفلية (_)
    return re.sub(r'[<>:"/\\|?*]', '_', filename)

# دالة لتنزيل الملفات
def download_files(contents, path=""):
    for content in contents:
        try:
            if content.type == "dir":
                # إذا كان العنصر مجلدًا، ننشئ مجلدًا جديدًا ونستمر في التنزيل
                new_path = os.path.join(output_dir, path, clean_filename(content.name))
                os.makedirs(new_path, exist_ok=True)
                download_files(repo.get_contents(content.path), os.path.join(path, clean_filename(content.name)))
            elif content.type == "file":
                # إذا كان العنصر ملفًا، نقوم بتنزيله
                file_path = os.path.join(output_dir, path, clean_filename(content.name))
                os.makedirs(os.path.dirname(file_path), exist_ok=True)  # إنشاء المجلدات الفرعية إذا لم تكن موجودة
                file_content = repo.get_contents(content.path)
                
                if file_content.encoding == "base64":
                    # فك تشفير المحتوى إذا كان مشفرًا بـ base64
                    decoded_content = base64.b64decode(file_content.content)
                    with open(file_path, 'wb') as file:
                        file.write(decoded_content)
                elif file_content.encoding is None:
                    # إذا لم يكن هناك ترميز، نتعامل مع الملف كملف ثنائي
                    with open(file_path, 'wb') as file:
                        file.write(file_content.decoded_content)
                else:
                    # إذا كان الترميز غير مدعوم، نحاول حفظ الملف كنص
                    try:
                        with open(file_path, 'w', encoding='utf-8') as file:
                            file.write(file_content.decoded_content.decode('utf-8'))
                    except UnicodeDecodeError:
                        # إذا فشل فك التشفير، نحفظ الملف كملف ثنائي
                        with open(file_path, 'wb') as file:
                            file.write(file_content.decoded_content)
                
                print(f"تم تنزيل: {file_path}")
        except ProtocolError as e:
            print(f"خطأ في الاتصال: {e}")
        except IncompleteRead as e:
            print(f"قراءة غير مكتملة: {e}")
        except Exception as e:
            print(f"خطأ غير متوقع: {e}")

# بدء التنزيل
try:
    contents = repo.get_contents("")
    download_files(contents)
    print("تم تنزيل جميع الملفات بنجاح!")
except Exception as e:
    print(f"حدث خطأ أثناء محاولة تنزيل الملفات: {e}")
