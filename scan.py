import os
import joblib
import requests
from lxml import html
from urllib.parse import urljoin, urlparse, urlunparse, parse_qs, quote
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import Embedding, LSTM, Dense, Conv1D, MaxPooling1D, Flatten
from tensorflow.keras.preprocessing.text import Tokenizer
from tensorflow.keras.preprocessing.sequence import pad_sequences
from sklearn.feature_extraction.text import TfidfVectorizer
from nltk.tokenize import word_tokenize
from nltk.stem import WordNetLemmatizer
from PIL import Image, ImageDraw, ImageFont, ImageChops, ImageEnhance
from concurrent.futures import ThreadPoolExecutor, as_completed, ProcessPoolExecutor, wait
import logging
import tensorflow as tf
import time
import unittest
from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
from colorama import Fore, Style, init
from stable_baselines3 import PPO
import gym
from gym import spaces
from sklearn.ensemble import IsolationForest
import subprocess
import dns.resolver
import shap
import torch
import networkx as nx
import pytorch_lightning as pl
from alibi.explainers import Counterfactual
import seaborn as sns
import matplotlib.pyplot as plt
from art.attacks.evasion import FastGradientMethod
from art.estimators.classification import TensorFlowV2Classifier
import h5py
import pennylane as qml
from transformers import BertTokenizer, BertForSequenceClassification, pipeline
from gtts import gTTS
import speech_recognition as sr
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import gradio as gr
import tempfile
import brian2
from cachetools import cached, TTLCache
from functools import lru_cache
import asyncio
import aiohttp
import backoff
import psutil
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
import signal
import faker
import json

# Initialize colorama
init()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Global headers for requests
HEADERS = {
    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language': 'en-US,en;q=0.5',
    'Connection': 'keep-alive',
}

# Global configuration
CONFIG = {
    'MAX_RETRIES': 3,
    'TIMEOUT': 30,
    'MAX_CONCURRENT_REQUESTS': 10,
    'MAX_CONCURRENT_SCREENSHOTS': 5,
    'MAX_URL_LENGTH': 2000,
    'PROXY_POOL': [],
    'CACHE_SIZE': 1024,
    'MEMORY_LIMIT': 0.8,  # 80% of available memory
    'CHUNK_SIZE': 1024 * 1024,  # 1MB chunks for large files
}

# Utility functions for performance and stability
def get_memory_usage():
    """Get current memory usage percentage"""
    return psutil.Process().memory_percent()

def check_memory_limit():
    """Check if memory usage exceeds limit"""
    return get_memory_usage() > CONFIG['MEMORY_LIMIT']

def cleanup_memory():
    """Clean up memory by clearing caches and unused objects"""
    import gc
    gc.collect()
    if torch.cuda.is_available():
        torch.cuda.empty_cache()

def safe_url(url: str) -> str:
    """Safely encode and validate URL"""
    try:
        parsed = urlparse(url)
        if len(url) > CONFIG['MAX_URL_LENGTH']:
            logging.warning(f"URL too long, truncating: {url}")
            url = url[:CONFIG['MAX_URL_LENGTH']]
        return quote(url, safe=':/?=&')
    except Exception as e:
        logging.error(f"Error processing URL {url}: {str(e)}")
        return url

def get_random_proxy() -> Optional[str]:
    """Get a random proxy from the pool"""
    if CONFIG['PROXY_POOL']:
        return random.choice(CONFIG['PROXY_POOL'])
    return None

def create_session() -> requests.Session:
    """Create a requests session with retry logic"""
    session = requests.Session()
    retry_strategy = Retry(
        total=CONFIG['MAX_RETRIES'],
        backoff_factor=0.5,
        status_forcelist=[500, 502, 503, 504]
    )
    adapter = HTTPAdapter(max_retries=retry_strategy)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    return session

def cleanup_resources():
    """Clean up all resources"""
    cleanup_memory()
    # Close any open sessions or connections
    if 'session' in globals():
        globals()['session'].close()
    # Close any open webdriver instances
    if 'driver' in globals():
        try:
            globals()['driver'].quit()
        except:
            pass

def signal_handler(signum, frame):
    """Handle system signals for graceful shutdown"""
    logging.info("Received shutdown signal, cleaning up...")
    cleanup_resources()
    sys.exit(0)

# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)

# تهيئة Faker لإنشاء بيانات وهمية
fake = Faker()

# تحسين إعدادات البيئة
os.environ['TF_ENABLE_ONEDNN_OPTS'] = '0'
os.environ['OMP_NUM_THREADS'] = str(cpu_count())
os.environ['TF_NUM_INTEROP_THREADS'] = str(cpu_count())
os.environ['TF_NUM_INTRAOP_THREADS'] = str(cpu_count())
os.environ['TF_CPP_MIN_LOG_LEVEL'] = '3'

# تهيئة colorama
init(autoreset=True)

# تعطيل تحذيرات TensorFlow
tf.get_logger().setLevel('ERROR')
tf.autograph.set_verbosity(0)

# إعدادات التسجيل
logging.basicConfig(
    filename='vulnerability_scanner.log',
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    filemode='w'
)
logger = logging.getLogger(__name__)

# عناوين HTTP المحسنة
HEADERS = {
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
    "Accept-Encoding": "gzip, deflate, br",
    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8",
    "Accept-Language": "en-US,en;q=0.5",
    "Connection": "keep-alive",
    "Upgrade-Insecure-Requests": "1"
}

# مسارات شائعة للفحص مع إضافات جديدة
COMMON_PATHS = [
    "/admin", "/login", "/wp-admin", "/config", "/backup", "/api", "/test", "/secret",
    "/robots.txt", "/.git", "/.env", "/.htaccess", "/.htpasswd", "/phpinfo.php",
    "/register", "/signup", "/password-reset", "/profile", "/dashboard", "/console",
    "/debug", "/management", "/phpMyAdmin", "/dbadmin", "/administrator", "/.well-known"
]

# تكوين Redis للتخزين المؤقت
try:
    redis_client = redis.Redis(
        host='localhost',
        port=6379,
        db=0,
        socket_timeout=5,
        socket_connect_timeout=5,
        decode_responses=True,
        health_check_interval=30
    )
    redis_client.ping()
except redis.ConnectionError:
    redis_client = None
    logger.warning("Redis connection failed, falling back to in-memory cache")

# قائمة User Agents متنوعة
USER_AGENTS = [
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 Edg/91.0.864.59',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36 Edg/92.0.902.55',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36 OPR/77.0.4054.203',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/92.0.4515.107 Safari/537.36 OPR/77.0.4054.203',
    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
]

def init_environment():
    """
    تهيئة البيئة والموارد اللازمة للفحص
    """
    try:
        print(Fore.YELLOW + "[*] Initializing environment...")

        # تهيئة الجلسة
        global session
        session = requests.Session()
        session.headers.update(HEADERS)

        # تهيئة Selenium
        global driver
        options = webdriver.ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        driver = webdriver.Chrome(options=options)

        # تهيئة نماذج التعلم الآلي
        print(Fore.YELLOW + "[*] Loading ML models...")
        load_bert_model()
        load_lstm_model()
        load_cnn_model()

        print(Fore.GREEN + "[+] Environment initialized successfully")
        return True
    except Exception as e:
        logger.error(f"Error initializing environment: {str(e)}")
        print(Fore.RED + f"[-] Error initializing environment: {str(e)}")
        return False

def load_bert_model():
    """تحميل نموذج BERT"""
    try:
        tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')
        model = BertForSequenceClassification.from_pretrained('bert-base-uncased')
        return {'tokenizer': tokenizer, 'model': model}
    except Exception as e:
        logger.error(f"Failed to load BERT model: {str(e)}")
        return None

def crawl_internal_links(url, max_depth=8, max_pages=5000):
    """Enhanced crawling of internal links with improved depth and discovery"""
    print(Fore.YELLOW + f"[*] بدء عملية الزحف للروابط الداخلية...")

    visited_links = set()
    links_to_visit = [(url, 0)]
    discovered_links = set()
    session = requests.Session()

    # إعدادات جلسة HTTP مع إعادة المحاولة والضغط
    retry = Retry(
        total=5,
        backoff_factor=0.1,
        status_forcelist=[500, 502, 503, 504]
    )
    adapter = HTTPAdapter(max_retries=retry)
    session.mount('http://', adapter)
    session.mount('https://', adapter)

    # إعدادات Selenium
    options = webdriver.ChromeOptions()
    options.add_argument('--headless')
    options.add_argument('--no-sandbox')
    options.add_argument('--disable-dev-shm-usage')
    options.add_argument('--disable-gpu')
    options.add_argument('--window-size=1920,1080')
    options.add_argument('--disable-notifications')
    options.add_argument('--disable-popup-blocking')
    driver = webdriver.Chrome(options=options)

    def extract_internal_links(page_url, current_depth):
        """Extract internal links with enhanced discovery"""
        if current_depth > max_depth or len(discovered_links) >= max_pages:
            return

        try:
            # استخدام Selenium للصفحات الديناميكية
            driver.get(page_url)
            time.sleep(2)  # انتظار تحميل JavaScript

            # استخراج الروابط من DOM
            links = driver.find_elements(By.TAG_NAME, 'a')
            for link in links:
                try:
                    href = link.get_attribute('href')
                    if href and url in href and href not in visited_links:
                        discovered_links.add(href)
                        links_to_visit.append((href, current_depth + 1))
                except:
                    continue

            # استخراج الروابط من JavaScript
            js_links = driver.execute_script("""
                var links = [];
                var elements = document.getElementsByTagName('*');
                for (var i = 0; i < elements.length; i++) {
                    var element = elements[i];
                    var onclick = element.getAttribute('onclick');
                    if (onclick && onclick.includes('window.location')) {
                        var match = onclick.match(/window\.location\s*=\s*['"]([^'"]+)['"]/);
                        if (match) links.push(match[1]);
                    }
                }
                return links;
            """)

            for js_link in js_links:
                if js_link and url in js_link and js_link not in visited_links:
                    discovered_links.add(js_link)
                    links_to_visit.append((js_link, current_depth + 1))

            # استخراج الروابط من XMLHttpRequest
            xhr_links = driver.execute_script("""
                var links = [];
                var xhr = new XMLHttpRequest();
                xhr.open('GET', arguments[0], true);
                xhr.onreadystatechange = function() {
                    if (xhr.readyState == 4 && xhr.status == 200) {
                        var response = xhr.responseText;
                        var matches = response.match(/https?:\/\/[^"']+/g);
                        if (matches) links = links.concat(matches);
                    }
                };
                xhr.send();
                return links;
            """, page_url)

            for xhr_link in xhr_links:
                if xhr_link and url in xhr_link and xhr_link not in visited_links:
                    discovered_links.add(xhr_link)
                    links_to_visit.append((xhr_link, current_depth + 1))

            # استخراج الروابط من iframes
            iframes = driver.find_elements(By.TAG_NAME, 'iframe')
            for iframe in iframes:
                try:
                    driver.switch_to.frame(iframe)
                    iframe_links = driver.find_elements(By.TAG_NAME, 'a')
                    for link in iframe_links:
                        try:
                            href = link.get_attribute('href')
                            if href and url in href and href not in visited_links:
                                discovered_links.add(href)
                                links_to_visit.append((href, current_depth + 1))
                        except:
                            continue
                    driver.switch_to.default_content()
                except:
                    driver.switch_to.default_content()

        except Exception as e:
            print(Fore.RED + f"[-] Error crawling {page_url}: {str(e)}")

    # بدء عملية الزحف
    while links_to_visit and len(discovered_links) < max_pages:
        current_url, depth = links_to_visit.pop(0)
        if current_url not in visited_links and depth <= max_depth:
            print(Fore.YELLOW + f"[*] جلب الروابط الداخلية من: {current_url} (عمق: {depth})")
            visited_links.add(current_url)
            extract_internal_links(current_url, depth)

    driver.quit()
    print(Fore.GREEN + f"[+] تم اكتشاف {len(discovered_links)} رابط داخلي فريد")
    return list(discovered_links)

def fetch_links(url):
    """Enhanced link discovery using multiple methods"""
    print(Fore.YELLOW + "[*] Fetching links using multiple methods...")
    discovered_links = set()

    # 1. فحص robots.txt
    try:
        robots_url = urljoin(url, '/robots.txt')
        response = requests.get(robots_url, verify=False, timeout=10)
        if response.status_code == 200:
            for line in response.text.split('\n'):
                if line.startswith('Allow:') or line.startswith('Disallow:'):
                    path = line.split(': ')[1].strip()
                    if path and not path.startswith('*'):
                        discovered_links.add(urljoin(url, path))
    except:
        pass

    # 2. فحص sitemap.xml
    try:
        sitemap_url = urljoin(url, '/sitemap.xml')
        response = requests.get(sitemap_url, verify=False, timeout=10)
        if response.status_code == 200:
            soup = BeautifulSoup(response.text, 'xml')
            for loc in soup.find_all('loc'):
                discovered_links.add(loc.text)
    except:
        pass

    # 3. الزحف المباشر
    internal_links = crawl_internal_links(url)
    discovered_links.update(internal_links)

    # 4. فحص الروابط المخفية في JavaScript
    try:
        response = requests.get(url, verify=False, timeout=10)
        if response.status_code == 200:
            # البحث عن الروابط في JavaScript
            js_links = re.findall(r'window\.location\s*=\s*[\'"]([^\'"]+)[\'"]', response.text)
            js_links.extend(re.findall(r'location\.href\s*=\s*[\'"]([^\'"]+)[\'"]', response.text))
            js_links.extend(re.findall(r'<a[^>]+href=[\'"]([^\'"]+)[\'"]', response.text))

            for link in js_links:
                if url in link:
                    discovered_links.add(link)
    except:
        pass

    print(Fore.GREEN + f"[+] تم العثور على {len(discovered_links)} رابط داخلي فريد")
    return list(discovered_links)

def find_vulnerabilities(content, url):
    """Find vulnerabilities in content using enhanced detection methods"""
    scanner = ClamAVScanner()
    vulnerabilities = scanner.scan_content(content, url)

    # Add other vulnerability checks here...
    # ... existing vulnerability checks ...

    return vulnerabilities

def load_model(file_name):
    """تحميل نموذج مع التخزين المؤقت"""
    logger.info(f"Loading model: {file_name}")
    print(Fore.YELLOW + f"[*] Loading model: {file_name}")
    try:
        if os.path.getsize(file_name) > 100 * 1024 * 1024:  # للنماذج الأكبر من 100MB
            with open(file_name, 'rb') as f:
                if file_name.endswith('.h5'):
                    model = tf.keras.models.load_model(f)
                else:
                    model = joblib.load(f)
        else:
            if file_name.endswith('.h5'):
                model = tf.keras.models.load_model(file_name)
            else:
                model = joblib.load(file_name)
        return model
    except Exception as e:
        logger.error(f"Error loading model {file_name}: {str(e)}")
        print(Fore.RED + f"[-] Error loading model: {str(e)}")
        return None

# تحميل جميع النماذج والمتجهات
models_and_vectorizers = {
    "vectorizer": load_model('exploitdb_vectorizer_full.pkl'),
    "model": load_model('exploitdb_model_full.pkl'),
    "model0": load_model('vuln_model.pk1'),
    "vectorizer0": load_model('Vectorizer.pk1'),
    "model01": load_model('vuln_model0.pk1'),
    "vectorizer01": load_model('Vectorizer0.pk1'),
    "model5": load_model('vuln_model5.pk1'),
    "vectorizer5": load_model('Vectorizer5.pk1'),
    "model00": load_model('vuln_model00.pk1'),
    "vectorizer00": load_model('Vectorizer00.pk1'),
    "classified_model": load_model('classified_vulnerabilities_model.pkl'),
    "classified_vectorizer": load_model('classified_vulnerabilities_vectorizer.pkl'),
    "classified_model99": load_model('classified_vulnerabilities_model99.pkl'),
    "classified_vectorizer99": load_model('classified_vulnerabilities_vectorizer99.pkl'),
    "final_dataset00_model": load_model('final_dataset00.pkl'),
    "final_dataset00_vectorizer": load_model('final_dataset00_Vectorizer1.pkl'),
    "github_payloads2_model": load_model('github_payloads2.pkl'),
    "github_payloads2_vectorizer": load_model('github_payloads2_Vectorizer1.pkl'),
    "github_payloads1_model": load_model('github_payloads1.pkl'),
    "github_payloads1_vectorizer": load_model('github_payloads1_Vectorizer1.pkl')
}

@cached(cache)
@lru_cache(maxsize=1024)
def process_text(content):
    """معالجة النص للتحليل"""
    try:
        if not content:
            return ""

        # تنظيف النص
        content = content.strip()

        # إزالة العلامات HTML
        soup = BeautifulSoup(content, 'html.parser')
        content = soup.get_text(separator=' ', strip=True)

        # إزالة الأحرف الخاصة والمسافات الزائدة
        content = re.sub(r'[^\w\s]', ' ', content)
        content = re.sub(r'\s+', ' ', content)

        # تجزئة النص
        tokens = word_tokenize(content)

        # تصريف الكلمات
        lemmatizer = WordNetLemmatizer()
        processed_tokens = [lemmatizer.lemmatize(token.lower()) for token in tokens]

        # إزالة الكلمات غير المهمة
        stop_words = set(['the', 'a', 'an', 'and', 'or', 'but', 'if', 'because', 'as', 'what',
                         'when', 'where', 'how', 'which', 'who', 'whom', 'this', 'that', 'these',
                         'those', 'then', 'just', 'so', 'than', 'such', 'both', 'through', 'about',
                         'for', 'is', 'of', 'while', 'during', 'to', 'What', 'How', 'When', 'Where',
                         'Who', 'Whom', 'Which', 'Why', 'Whether'])

        processed_tokens = [token for token in processed_tokens if token not in stop_words]

        # إزالة الكلمات القصيرة جداً
        processed_tokens = [token for token in processed_tokens if len(token) > 2]

        # إزالة الأرقام
        processed_tokens = [token for token in processed_tokens if not token.isdigit()]

        # إعادة تجميع النص
        processed_text = ' '.join(processed_tokens)

        # التحقق من جودة النص المعالج
        if len(processed_text) < 10:
            logger.warning("Processed text is too short")
            return ""

        return processed_text

    except Exception as e:
        logger.error(f"Error processing text: {str(e)}")
        return ""

@cached(cache)
@lru_cache(maxsize=1)
def load_lstm_model():
    """تحميل نموذج LSTM"""
    try:
        model = Sequential([
            Embedding(10000, 128, input_length=100),
            LSTM(128, return_sequences=True),
            LSTM(64),
            Dense(64, activation='relu'),
            Dense(1, activation='sigmoid')
        ])

        model.compile(
            optimizer='adam',
            loss='binary_crossentropy',
            metrics=['accuracy']
        )

        return model

    except Exception as e:
        logger.error(f"Failed to load LSTM model: {str(e)}")
        return None

@cached(cache)
@lru_cache(maxsize=1)
def load_cnn_model():
    """تحميل نموذج CNN"""
    try:
        model = Sequential([
            Embedding(10000, 128, input_length=100),
            Conv1D(128, 5, activation='relu'),
            MaxPooling1D(5),
            Conv1D(128, 5, activation='relu'),
            MaxPooling1D(5),
            Flatten(),
            Dense(128, activation='relu'),
            Dense(1, activation='sigmoid')
        ])

        model.compile(
            optimizer='adam',
            loss='binary_crossentropy',
            metrics=['accuracy']
        )

        return model

    except Exception as e:
        logger.error(f"Failed to load CNN model: {str(e)}")
        return None

def fine_tune_bert_model(train_texts, train_labels):
    """ضبط نموذج BERT على البيانات المخصصة"""
    logger.info("Fine-tuning BERT model")
    print(Fore.YELLOW + "[*] Fine-tuning BERT model on custom data with Data Augmentation and Hyperparameter Tuning...")

    try:
        tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')
        model = BertForSequenceClassification.from_pretrained('bert-base-uncased')

        def back_translation(texts):
            """زيادة البيانات بالترجمة العكسية"""
            translator = Translator()
            augmented_texts = []
            with ThreadPoolExecutor(max_workers=4) as executor:
                futures = []
                for text in texts:
                    futures.append(executor.submit(
                        lambda t: translator.translate(
                            translator.translate(t, src='en', dest='fr').text,
                            src='fr', dest='en'
                        ).text,
                        text
                    ))
                for future in as_completed(futures):
                    try:
                        augmented_texts.append(future.result())
                    except:
                        augmented_texts.append(text)
            return augmented_texts

        augmented_texts = back_translation(train_texts)

        train_encodings = tokenizer(
            augmented_texts,
            truncation=True,
            padding=True,
            max_length=128,
            return_tensors="pt"
        )

        train_dataset = Dataset.from_dict({
            'input_ids': train_encodings['input_ids'].tolist(),
            'attention_mask': train_encodings['attention_mask'].tolist(),
            'labels': train_labels
        })

        def objective(trial):
            """وظيفة التحسين لـ Optuna"""
            lr = trial.suggest_float("lr", 1e-5, 1e-3, log=True)
            batch_size = trial.suggest_categorical("batch_size", [8, 16, 32])
            num_epochs = trial.suggest_int("num_epochs", 1, 5)

            args = TrainingArguments(
                output_dir='./results',
                num_train_epochs=num_epochs,
                per_device_train_batch_size=batch_size,
                learning_rate=lr,
                save_steps=10_000,
                save_total_limit=2,
                logging_dir='./logs',
                logging_steps=500,
                evaluation_strategy="steps",
                eval_steps=500,
                warmup_steps=500,
                weight_decay=0.01,
                fp16=True,
                gradient_accumulation_steps=2
            )

            trainer = Trainer(
                model=model,
                args=args,
                train_dataset=train_dataset,
            )

            trainer.train()
            return trainer.evaluate()

        study = optuna.create_study(direction="maximize")
        study.optimize(objective, n_trials=10, timeout=3600)
        best_params = study.best_params
        logger.info(f"Best Hyperparameters: {best_params}")
        print(Fore.GREEN + f"[+] Best Hyperparameters: {best_params}")

        final_args = TrainingArguments(
            output_dir='./results',
            num_train_epochs=best_params.get('num_epochs', 3),
            per_device_train_batch_size=best_params.get('batch_size', 16),
            learning_rate=best_params.get('lr', 2e-5),
            save_steps=10_000,
            save_total_limit=2,
            logging_dir='./logs',
            logging_steps=500,
            evaluation_strategy="steps",
            eval_steps=500,
            warmup_steps=500,
            weight_decay=0.01,
            fp16=True,
            gradient_accumulation_steps=2
        )

        final_trainer = Trainer(
            model=model,
            args=final_args,
            train_dataset=train_dataset,
        )

        final_trainer.train()
        return model
    except Exception as e:
        logger.error(f"Error fine-tuning BERT model: {str(e)}")
        return None

def quantum_neural_network_example():
    """مثال على شبكة عصبية كمية"""
    logger.info("Running Quantum Neural Network example")
    print(Fore.YELLOW + "[*] Running Quantum Neural Network example with PennyLane...")

    try:
        dev = qml.device("default.qubit", wires=2)

        @qml.qnode(dev)
        def circuit(inputs, weights):
            qml.RX(inputs[0], wires=0)
            qml.RY(inputs[1], wires=1)
            qml.CNOT(wires=[0, 1])
            qml.Rot(*weights[0], wires=0)
            qml.Rot(*weights[1], wires=1)
            qml.CNOT(wires=[0, 1])
            return [qml.expval(qml.PauliZ(i)) for i in range(2)]

        inputs = np.array([0.54, 0.12])
        weights = np.random.rand(2, 3)
        result = circuit(inputs, weights)
        logger.info(f"Quantum Neural Network Result: {result}")
        print(Fore.GREEN + f"[+] Quantum Neural Network Result: {result}")
        return result
    except Exception as e:
        logger.error(f"Error in quantum neural network: {str(e)}")
        return None

def generate_counterfactual_explanation(model, input_data):
    """إنشاء تفسيرات افتراضية مضادة"""
    logger.info("Generating Counterfactual Explanation")
    print(Fore.YELLOW + "[*] Generating Counterfactual Explanation using Alibi...")

    try:
        cf = Counterfactual(model, shape=(1, len(input_data)))
        explanation = cf.explain(input_data)
        if explanation.cf is not None:
            logger.info(f"Counterfactual Explanation: {explanation.cf}")
            print(Fore.GREEN + f"[+] Counterfactual Explanation: {explanation.cf}")
            return explanation.cf
        else:
            logger.warning("No Counterfactual Explanation found")
            print(Fore.RED + "[-] No Counterfactual Explanation found.")
            return None
    except Exception as e:
        logger.error(f"Error generating counterfactual: {str(e)}")
        return None

def graph_neural_network_analysis(vulnerabilities):
    """تحليل الثغرات باستخدام الشبكات العصبية البيانية"""
    logger.info("Analyzing vulnerabilities using GNNs")
    print(Fore.YELLOW + "[*] Analyzing vulnerabilities using Graph Neural Networks (GNNs)...")

    try:
        G = nx.Graph()
        for vuln in vulnerabilities:
            G.add_node(vuln["line_number"], content=vuln["line_content"], prediction=vuln["prediction"])

        for i in range(len(vulnerabilities) - 1):
            G.add_edge(vulnerabilities[i]["line_number"], vulnerabilities[i + 1]["line_number"])

        adjacency_matrix = nx.to_numpy_array(G)
        logger.info(f"GNN Analysis Completed. Adjacency Matrix: {adjacency_matrix}")
        print(Fore.GREEN + f"[+] Graph Neural Network Analysis Completed. Adjacency Matrix: {adjacency_matrix}")
        return adjacency_matrix
    except Exception as e:
        logger.error(f"Error in GNN analysis: {str(e)}")
        return None

def reinforcement_learning_exploitation():
    """تعلم تعزيزي لاستغلال الثغرات"""
    logger.info("Running Reinforcement Learning for Exploitation")
    print(Fore.YELLOW + "[*] Running Reinforcement Learning for Exploitation...")

    try:
        env = make_vec_env(lambda: gym.make('CartPole-v1'), n_envs=4)
        model = PPO('MlpPolicy', env, verbose=1, device='auto')
        model.learn(total_timesteps=10000, progress_bar=True)
        logger.info("Reinforcement Learning for Exploitation Completed")
        print(Fore.GREEN + "[+] Reinforcement Learning for Exploitation Completed.")
        return model
    except Exception as e:
        logger.error(f"Error in reinforcement learning: {str(e)}")
        return None

def parallel_processing_example(tasks):
    """مثال على المعالجة المتوازية"""
    logger.info("Running Parallel Processing example")
    print(Fore.YELLOW + "[*] Running Parallel Processing example...")

    try:
        results = []
        with ProcessPoolExecutor(max_workers=cpu_count()) as executor:
            futures = [executor.submit(task) for task in tasks]
            for future in as_completed(futures):
                try:
                    result = future.result()
                    results.append(result)
                    logger.info(f"Parallel Processing Result: {result}")
                    print(Fore.GREEN + f"[+] Parallel Processing Result: {result}")
                except Exception as e:
                    logger.error(f"Error in parallel task: {str(e)}")
        return results
    except Exception as e:
        logger.error(f"Error in parallel processing: {str(e)}")
        return None

def caching_mechanism_example():
    """مثال على آلية التخزين المؤقت"""
    logger.info("Running Caching Mechanism example")
    print(Fore.YELLOW + "[*] Running Caching Mechanism example...")

    try:
        @lru_cache(maxsize=1024)
        def expensive_function(x):
            return x * x

        result = expensive_function(10)
        logger.info(f"Caching Mechanism Result: {result}")
        print(Fore.GREEN + f"[+] Caching Mechanism Result: {result}")
        return result
    except Exception as e:
        logger.error(f"Error in caching mechanism: {str(e)}")
        return None

class AutomatedPenetrationTester:
    """أداة اختبار الاختراق الآلي"""
    def __init__(self, target_url):
        self.target_url = target_url
        self.session = requests.Session()
        self.vulnerabilities = []
        self.test_cases = self._load_test_cases()
        self.payloads = self._load_payloads()

    def _load_test_cases(self):
        """تحميل سيناريوهات اختبار الاختراق"""
        try:
            with open('penetration_test_cases.json', 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading test cases: {str(e)}")
            return []

    def _load_payloads(self):
        """تحميل حمولات الهجوم"""
        try:
            with open('attack_payloads.json', 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading payloads: {str(e)}")
            return []

    def run_full_penetration_test(self):
        """تنفيذ اختبار اختراق شامل"""
        print(Fore.CYAN + "[*] Starting Automated Penetration Testing...")

        # تشغيل جميع وحدات الاختبار
        self.test_authentication()
        self.test_injection_attacks()
        self.test_file_inclusion()
        self.test_business_logic()
        self.test_api_security()
        self.test_server_security()
        self.test_cors_misconfigurations()
        self.test_csrf_vulnerabilities()
        self.test_xxe_vulnerabilities()
        self.test_deserialization()
        self.test_ssrf_vulnerabilities()
        self.test_idor_vulnerabilities()

        print(Fore.GREEN + "[+] Automated Penetration Testing completed")
        return self.vulnerabilities

    def test_authentication(self):
        """اختبار آليات المصادقة"""
        print(Fore.YELLOW + "[*] Testing Authentication Mechanisms...")

        # تحميل نماذج المصادقة
        auth_model = models_and_vectorizers.get("classified_model")
        auth_vectorizer = models_and_vectorizers.get("classified_vectorizer")

        # اختبار بيانات الاعتماد الافتراضية
        for cred_pair in self.payloads.get('default_credentials', []):
            result = self._test_default_credentials(cred_pair['username'], cred_pair['password'])
            if result:
                if auth_model and auth_vectorizer:
                    features = auth_vectorizer.transform([result['description']])
                    prediction = auth_model.predict(features)[0]
                    result['ml_prediction'] = prediction
                    result['ml_confidence'] = max(auth_model.predict_proba(features)[0])

        # اختبار ثغرات القوة الغاشمة
        self._test_brute_force()

        # اختبار ضعف سياسة كلمة المرور
        self._test_password_policy()

        # اختبار مشكلات إدارة الجلسة
        self._test_session_management()

    def _test_default_credentials(self, username, password):
        """اختبار بيانات الاعتماد الافتراضية"""
        login_urls = [
            urljoin(self.target_url, 'login'),
            urljoin(self.target_url, 'admin'),
            urljoin(self.target_url, 'wp-login.php')
        ]

        for url in login_urls:
            try:
                response = self.session.post(url, data={
                    'username': username,
                    'password': password
                }, timeout=10)

                if response.status_code == 200 and any(word in response.text.lower() for word in ['dashboard', 'welcome', 'admin']):
                    vuln = {
                        'type': 'Authentication',
                        'severity': 'Critical',
                        'description': f'Default credentials work: {username}/{password}',
                        'url': url,
                        'request': {'username': username, 'password': password},
                        'response': response.text[:500]
                    }
                    self.vulnerabilities.append(vuln)
                    return vuln
            except Exception as e:
                logger.error(f"Error testing default credentials: {str(e)}")
        return None

    def _test_brute_force(self):
        """اختبار ثغرات القوة الغاشمة"""
        login_url = urljoin(self.target_url, 'login')
        test_users = ['admin', 'test', 'user', 'root']
        test_passwords = ['password', '123456', 'admin', 'welcome']

        try:
            for user in test_users:
                for pwd in test_passwords:
                    response = self.session.post(login_url, data={
                        'username': user,
                        'password': pwd
                    }, timeout=5)

                    if 'invalid' not in response.text.lower() and 'incorrect' not in response.text.lower():
                        self.vulnerabilities.append({
                            'type': 'Authentication',
                            'severity': 'High',
                            'description': f'Possible brute force vulnerability. Credentials {user}/{pwd} may work',
                            'url': login_url,
                            'request': {'username': user, 'password': pwd},
                            'response': response.text[:500]
                        })
                        break
        except Exception as e:
            logger.error(f"Error testing brute force: {str(e)}")

    def test_injection_attacks(self):
        """اختبار هجمات الحقن"""
        print(Fore.YELLOW + "[*] Testing Injection Attacks...")

        # تحميل نماذج الحقن
        injection_model = models_and_vectorizers.get("github_payloads1_model")
        injection_vectorizer = models_and_vectorizers.get("github_payloads1_vectorizer")

        # حقن SQL
        sql_results = self._test_sql_injection()
        if sql_results and injection_model and injection_vectorizer:
            for result in sql_results:
                features = injection_vectorizer.transform([result['payload']])
                result['ml_prediction'] = injection_model.predict(features)[0]
                result['ml_confidence'] = max(injection_model.predict_proba(features)[0])

        # XSS
        xss_results = self._test_xss()
        if xss_results and injection_model and injection_vectorizer:
            for result in xss_results:
                features = injection_vectorizer.transform([result['payload']])
                result['ml_prediction'] = injection_model.predict(features)[0]
                result['ml_confidence'] = max(injection_model.predict_proba(features)[0])

        # حقن الأوامر
        cmd_results = self._test_command_injection()
        if cmd_results and injection_model and injection_vectorizer:
            for result in cmd_results:
                features = injection_vectorizer.transform([result['payload']])
                result['ml_prediction'] = injection_model.predict(features)[0]
                result['ml_confidence'] = max(injection_model.predict_proba(features)[0])

        # حقن LDAP
        ldap_results = self._test_ldap_injection()
        if ldap_results and injection_model and injection_vectorizer:
            for result in ldap_results:
                features = injection_vectorizer.transform([result['payload']])
                result['ml_prediction'] = injection_model.predict(features)[0]
                result['ml_confidence'] = max(injection_model.predict_proba(features)[0])

        # حقن XPath
        xpath_results = self._test_xpath_injection()
        if xpath_results and injection_model and injection_vectorizer:
            for result in xpath_results:
                features = injection_vectorizer.transform([result['payload']])
                result['ml_prediction'] = injection_model.predict(features)[0]
                result['ml_confidence'] = max(injection_model.predict_proba(features)[0])

    def _test_sql_injection(self):
        """اختبار ثغرات حقن SQL"""
        test_urls = self._find_input_points()
        sql_vulns = []

        for url, params in test_urls.items():
            for payload in self.payloads.get('sql_injection', []):
                try:
                    if params:  # طلب POST
                        test_params = params.copy()
                        for key in test_params:
                            if isinstance(test_params[key], str):
                                test_params[key] = payload

                        response = self.session.post(url, data=test_params, timeout=10)
                    else:  # طلب GET
                        parsed = urlparse(url)
                        query = parse_qs(parsed.query)
                        for key in query:
                            query[key] = payload

                        new_url = parsed._replace(query=None).geturl()
                        new_url += "?" + "&".join(f"{k}={v}" for k, v in query.items())
                        response = self.session.get(new_url, timeout=10)

                    if any(indicator in response.text.lower() for indicator in ['sql', 'syntax', 'mysql', 'oracle', 'error']):
                        vuln = {
                            'type': 'SQL Injection',
                            'severity': 'Critical',
                            'description': f'Possible SQL injection vulnerability with payload: {payload}',
                            'url': url,
                            'payload': payload,
                            'response': response.text[:500]
                        }
                        self.vulnerabilities.append(vuln)
                        sql_vulns.append(vuln)
                except Exception as e:
                    logger.error(f"Error testing SQL injection: {str(e)}")
        return sql_vulns

    def _test_xss(self):
        """اختبار ثغرات XSS"""
        test_urls = self._find_input_points()
        xss_payloads = self.payloads.get('xss', [])
        xss_vulns = []

        for url, params in test_urls.items():
            for payload in xss_payloads:
                try:
                    if params:  # طلب POST
                        test_params = params.copy()
                        for key in test_params:
                            if isinstance(test_params[key], str):
                                test_params[key] = payload

                        response = self.session.post(url, data=test_params, timeout=10)
                    else:  # طلب GET
                        parsed = urlparse(url)
                        query = parse_qs(parsed.query)
                        for key in query:
                            query[key] = payload

                        new_url = parsed._replace(query=None).geturl()
                        new_url += "?" + "&".join(f"{k}={v}" for k, v in query.items())
                        response = self.session.get(new_url, timeout=10)

                    if payload in response.text:
                        vuln = {
                            'type': 'XSS',
                            'severity': 'High',
                            'description': f'Possible XSS vulnerability with payload: {payload}',
                            'url': url,
                            'payload': payload,
                            'response': response.text[:500]
                        }
                        self.vulnerabilities.append(vuln)
                        xss_vulns.append(vuln)
                except Exception as e:
                    logger.error(f"Error testing XSS: {str(e)}")
        return xss_vulns

    def test_file_inclusion(self):
        """اختبار ثغرات تضمين الملفات"""
        print(Fore.YELLOW + "[*] Testing File Inclusion Vulnerabilities...")

        # تحميل نماذج تضمين الملفات
        file_model = models_and_vectorizers.get("github_payloads2_model")
        file_vectorizer = models_and_vectorizers.get("github_payloads2_vectorizer")

        # تضمين ملف محلي
        lfi_results = self._test_lfi()
        if lfi_results and file_model and file_vectorizer:
            for result in lfi_results:
                features = file_vectorizer.transform([result['payload']])
                result['ml_prediction'] = file_model.predict(features)[0]
                result['ml_confidence'] = max(file_model.predict_proba(features)[0])

        # تضمين ملف عن بعد
        rfi_results = self._test_rfi()
        if rfi_results and file_model and file_vectorizer:
            for result in rfi_results:
                features = file_vectorizer.transform([result['payload']])
                result['ml_prediction'] = file_model.predict(features)[0]
                result['ml_confidence'] = max(file_model.predict_proba(features)[0])

        # اجتياز الدلائل
        traversal_results = self._test_directory_traversal()
        if traversal_results and file_model and file_vectorizer:
            for result in traversal_results:
                features = file_vectorizer.transform([result['payload']])
                result['ml_prediction'] = file_model.predict(features)[0]
                result['ml_confidence'] = max(file_model.predict_proba(features)[0])

    def _test_lfi(self):
        """اختبار ثغرات تضمين الملفات المحلية"""
        test_urls = self._find_input_points()
        lfi_payloads = self.payloads.get('lfi', [])
        lfi_vulns = []

        for url, params in test_urls.items():
            for payload in lfi_payloads:
                try:
                    if params:  # طلب POST
                        test_params = params.copy()
                        for key in test_params:
                            if isinstance(test_params[key], str):
                                test_params[key] = payload

                        response = self.session.post(url, data=test_params, timeout=10)
                    else:  # طلب GET
                        parsed = urlparse(url)
                        query = parse_qs(parsed.query)
                        for key in query:
                            query[key] = payload

                        new_url = parsed._replace(query=None).geturl()
                        new_url += "?" + "&".join(f"{k}={v}" for k, v in query.items())
                        response = self.session.get(new_url, timeout=10)

                    if any(indicator in response.text.lower() for indicator in ['root:', 'password:', 'etc/passwd']):
                        vuln = {
                            'type': 'LFI',
                            'severity': 'High',
                            'description': f'Possible Local File Inclusion with payload: {payload}',
                            'url': url,
                            'payload': payload,
                            'response': response.text[:500]
                        }
                        self.vulnerabilities.append(vuln)
                        lfi_vulns.append(vuln)
                except Exception as e:
                    logger.error(f"Error testing LFI: {str(e)}")
        return lfi_vulns

    def test_business_logic(self):
        """اختبار ثغرات منطق الأعمال"""
        print(Fore.YELLOW + "[*] Testing Business Logic Vulnerabilities...")

        # تحميل نماذج منطق الأعمال
        logic_model = models_and_vectorizers.get("final_dataset00_model")
        logic_vectorizer = models_and_vectorizers.get("final_dataset00_vectorizer")

        # التلاعب بالسعر
        price_results = self._test_price_manipulation()
        if price_results and logic_model and logic_vectorizer:
            for result in price_results:
                features = logic_vectorizer.transform([result['description']])
                result['ml_prediction'] = logic_model.predict(features)[0]
                result['ml_confidence'] = max(logic_model.predict_proba(features)[0])

        # التلاعب بالكمية
        quantity_results = self._test_quantity_manipulation()
        if quantity_results and logic_model and logic_vectorizer:
            for result in quantity_results:
                features = logic_vectorizer.transform([result['description']])
                result['ml_prediction'] = logic_model.predict(features)[0]
                result['ml_confidence'] = max(logic_model.predict_proba(features)[0])

        # تجاوز سير العمل
        workflow_results = self._test_workflow_bypass()
        if workflow_results and logic_model and logic_vectorizer:
            for result in workflow_results:
                features = logic_vectorizer.transform([result['description']])
                result['ml_prediction'] = logic_model.predict(features)[0]
                result['ml_confidence'] = max(logic_model.predict_proba(features)[0])

        # تصعيد الصلاحيات
        priv_results = self._test_privilege_escalation()
        if priv_results and logic_model and logic_vectorizer:
            for result in priv_results:
                features = logic_vectorizer.transform([result['description']])
                result['ml_prediction'] = logic_model.predict(features)[0]
                result['ml_confidence'] = max(logic_model.predict_proba(features)[0])

    def _test_price_manipulation(self):
        """اختبار إمكانية التلاعب بالأسعار"""
        cart_urls = [
            urljoin(self.target_url, 'cart'),
            urljoin(self.target_url, 'checkout'),
            urljoin(self.target_url, 'basket')
        ]
        price_vulns = []

        for url in cart_urls:
            try:
                response = self.session.get(url, timeout=10)
                soup = BeautifulSoup(response.text, 'html.parser')

                # البحث عن حقول إدخال الأسعار
                price_fields = soup.find_all('input', {
                    'name': lambda x: x and ('price' in x.lower() or 'amount' in x.lower())
                })

                for field in price_fields:
                    original_value = field.get('value', '1.00')
                    tampered_value = str(float(original_value) * 0.1)  # 10% من السعر الأصلي

                    form = field.find_parent('form')
                    if form:
                        form_data = {}
                        for input_tag in form.find_all('input'):
                            if input_tag.get('name'):
                                if input_tag == field:
                                    form_data[input_tag['name']] = tampered_value
                                else:
                                    form_data[input_tag['name']] = input_tag.get('value', '')

                        action = form.get('action', url)
                        if not action.startswith('http'):
                            action = urljoin(url, action)

                        response = self.session.post(action, data=form_data, timeout=10)

                        if 'success' in response.text.lower():
                            vuln = {
                                'type': 'Business Logic',
                                'severity': 'Critical',
                                'description': f'Price manipulation possible. Changed from {original_value} to {tampered_value}',
                                'url': url,
                                'field': field.get('name', ''),
                                'original_value': original_value,
                                'tampered_value': tampered_value
                            }
                            self.vulnerabilities.append(vuln)
                            price_vulns.append(vuln)
            except Exception as e:
                logger.error(f"Error testing price manipulation: {str(e)}")
        return price_vulns

    def test_api_security(self):
        """اختبار أمان واجهات برمجة التطبيقات"""
        print(Fore.YELLOW + "[*] Testing API Security...")

        # تحميل نماذج أمان API
        api_model = models_and_vectorizers.get("model5")
        api_vectorizer = models_and_vectorizers.get("vectorizer5")

        # اختبار صلاحيات كسر مستوى الكائن
        bola_results = self._test_broken_object_auth()
        if bola_results and api_model and api_vectorizer:
            for result in bola_results:
                features = api_vectorizer.transform([result['description']])
                result['ml_prediction'] = api_model.predict(features)[0]
                result['ml_confidence'] = max(api_model.predict_proba(features)[0])

        # اختبار تسريب البيانات الزائد
        data_results = self._test_excessive_data_exposure()
        if data_results and api_model and api_vectorizer:
            for result in data_results:
                features = api_vectorizer.transform([result['description']])
                result['ml_prediction'] = api_model.predict(features)[0]
                result['ml_confidence'] = max(api_model.predict_proba(features)[0])

        # اختبار التعيين الجماعي
        mass_results = self._test_mass_assignment()
        if mass_results and api_model and api_vectorizer:
            for result in mass_results:
                features = api_vectorizer.transform([result['description']])
                result['ml_prediction'] = api_model.predict(features)[0]
                result['ml_confidence'] = max(api_model.predict_proba(features)[0])

    def _test_broken_object_auth(self):
        """اختبار صلاحيات كسر مستوى الكائن"""
        api_urls = [
            urljoin(self.target_url, 'api/users/1'),
            urljoin(self.target_url, 'api/orders/1'),
            urljoin(self.target_url, 'api/account')
        ]
        bola_vulns = []

        for url in api_urls:
            try:
                # أول طلب بمستخدم عادي
                response1 = self.session.get(url, timeout=10)

                # طلب ثان بمستخدم مختلف (محاكاة بتغيير التوكن)
                headers = HEADERS.copy()
                headers['Authorization'] = 'Bearer different_token'
                response2 = self.session.get(url, headers=headers, timeout=10)

                if response1.status_code == 200 and response2.status_code == 200:
                    if response1.text == response2.text:
                        vuln = {
                            'type': 'API Security',
                            'severity': 'High',
                            'description': 'Broken Object Level Authorization - Same data accessible with different tokens',
                            'url': url,
                            'response1': response1.text[:200],
                            'response2': response2.text[:200]
                        }
                        self.vulnerabilities.append(vuln)
                        bola_vulns.append(vuln)
            except Exception as e:
                logger.error(f"Error testing broken object auth: {str(e)}")
        return bola_vulns

    def test_server_security(self):
        """اختبار إعدادات أمان الخادم"""
        print(Fore.YELLOW + "[*] Testing Server Security...")

        # تحميل نماذج أمان الخادم
        server_model = models_and_vectorizers.get("model00")
        server_vectorizer = models_and_vectorizers.get("vectorizer00")

        # اختبار العناوين غير الآمنة
        header_results = self._test_insecure_headers()
        if header_results and server_model and server_vectorizer:
            for result in header_results:
                features = server_vectorizer.transform([result['description']])
                result['ml_prediction'] = server_model.predict(features)[0]
                result['ml_confidence'] = max(server_model.predict_proba(features)[0])

        # اختبار طرق HTTP
        method_results = self._test_http_methods()
        if method_results and server_model and server_vectorizer:
            for result in method_results:
                features = server_vectorizer.transform([result['description']])
                result['ml_prediction'] = server_model.predict(features)[0]
                result['ml_confidence'] = max(server_model.predict_proba(features)[0])

        # اختبار سرد الدلائل
        dir_results = self._test_directory_listing()
        if dir_results and server_model and server_vectorizer:
            for result in dir_results:
                features = server_vectorizer.transform([result['description']])
                result['ml_prediction'] = server_model.predict(features)[0]
                result['ml_confidence'] = max(server_model.predict_proba(features)[0])

    def _test_insecure_headers(self):
        """اختبار العناوين غير الآمنة"""
        insecure_headers = []

        try:
            response = self.session.get(self.target_url, timeout=10)
            headers = response.headers

            # التحقق من العناوين الأمنية المفقودة
            security_headers = [
                'Content-Security-Policy',
                'X-Content-Type-Options',
                'X-Frame-Options',
                'Strict-Transport-Security'
            ]

            for header in security_headers:
                if header not in headers:
                    vuln = {
                        'type': 'Server Security',
                        'severity': 'Medium',
                        'description': f'Missing security header: {header}',
                        'url': self.target_url,
                        'headers': dict(headers)
                    }
                    self.vulnerabilities.append(vuln)
                    insecure_headers.append(vuln)

            # التحقق من إعدادات الكوكيز غير الآمنة
            if 'Set-Cookie' in headers:
                cookie = headers['Set-Cookie']
                issues = []

                if 'Secure' not in cookie:
                    issues.append('Missing Secure flag')
                if 'HttpOnly' not in cookie:
                    issues.append('Missing HttpOnly flag')
                if 'SameSite' not in cookie:
                    issues.append('Missing SameSite attribute')

                if issues:
                    vuln = {
                        'type': 'Server Security',
                        'severity': 'Medium',
                        'description': 'Insecure cookie settings: ' + ', '.join(issues),
                        'url': self.target_url,
                        'cookie': cookie
                    }
                    self.vulnerabilities.append(vuln)
                    insecure_headers.append(vuln)
        except Exception as e:
            logger.error(f"Error testing insecure headers: {str(e)}")
        return insecure_headers

    def test_cors_misconfigurations(self):
        """اختبار إعدادات CORS الخاطئة"""
        print(Fore.YELLOW + "[*] Testing CORS Misconfigurations...")

        # تحميل نماذج CORS
        cors_model = models_and_vectorizers.get("model01")
        cors_vectorizer = models_and_vectorizers.get("vectorizer01")

        # اختبار CORS المتساهل جداً
        permissive_results = self._test_permissive_cors()
        if permissive_results and cors_model and cors_vectorizer:
            for result in permissive_results:
                features = cors_vectorizer.transform([result['description']])
                result['ml_prediction'] = cors_model.predict(features)[0]
                result['ml_confidence'] = max(cors_model.predict_proba(features)[0])

        # اختبار أصل null
        null_results = self._test_null_origin()
        if null_results and cors_model and cors_vectorizer:
            for result in null_results:
                features = cors_vectorizer.transform([result['description']])
                result['ml_prediction'] = cors_model.predict(features)[0]
                result['ml_confidence'] = max(cors_model.predict_proba(features)[0])

    def _test_permissive_cors(self):
        """اختبار إعدادات CORS المتساهلة"""
        cors_vulns = []

        try:
            headers = {
                'Origin': 'https://malicious.com',
                'Access-Control-Request-Method': 'GET'
            }

            # طلب Preflight
            response = self.session.options(self.target_url, headers=headers, timeout=10)

            if 'Access-Control-Allow-Origin' in response.headers:
                if response.headers['Access-Control-Allow-Origin'] == '*' or \
                   response.headers['Access-Control-Allow-Origin'] == 'https://malicious.com':

                    vuln = {
                        'type': 'CORS Misconfiguration',
                        'severity': 'Medium',
                        'description': 'Overly permissive CORS policy',
                        'url': self.target_url,
                        'headers': dict(response.headers)
                    }
                    self.vulnerabilities.append(vuln)
                    cors_vulns.append(vuln)
        except Exception as e:
            logger.error(f"Error testing permissive CORS: {str(e)}")
        return cors_vulns

    def test_csrf_vulnerabilities(self):
        """اختبار ثغرات CSRF"""
        print(Fore.YELLOW + "[*] Testing CSRF Vulnerabilities...")

        # تحميل نماذج CSRF
        csrf_model = models_and_vectorizers.get("model0")
        csrf_vectorizer = models_and_vectorizers.get("vectorizer0")

        # اختبار توكنات CSRF المفقودة
        token_results = self._test_missing_csrf_tokens()
        if token_results and csrf_model and csrf_vectorizer:
            for result in token_results:
                features = csrf_vectorizer.transform([result['description']])
                result['ml_prediction'] = csrf_model.predict(features)[0]
                result['ml_confidence'] = max(csrf_model.predict_proba(features)[0])

        # اختبار حماية CSRF الضعيفة
        weak_results = self._test_weak_csrf_protection()
        if weak_results and csrf_model and csrf_vectorizer:
            for result in weak_results:
                features = csrf_vectorizer.transform([result['description']])
                result['ml_prediction'] = csrf_model.predict(features)[0]
                result['ml_confidence'] = max(csrf_model.predict_proba(features)[0])

    def _test_missing_csrf_tokens(self):
        """اختبار توكنات CSRF المفقودة"""
        csrf_vulns = []
        test_urls = self._find_input_points(methods=['POST'])

        for url, params in test_urls.items():
            try:
                response = self.session.get(url, timeout=10)
                soup = BeautifulSoup(response.text, 'html.parser')

                # التحقق من توكن CSRF في النموذج
                form = soup.find('form')
                if form:
                    csrf_input = form.find('input', {'name': lambda x: x and ('csrf' in x.lower() or 'token' in x.lower())})

                    if not csrf_input:
                        vuln = {
                            'type': 'CSRF',
                            'severity': 'High',
                            'description': 'Missing CSRF token in form',
                            'url': url,
                            'form_action': form.get('action', '')
                        }
                        self.vulnerabilities.append(vuln)
                        csrf_vulns.append(vuln)
            except Exception as e:
                logger.error(f"Error testing missing CSRF tokens: {str(e)}")
        return csrf_vulns

    def test_xxe_vulnerabilities(self):
        """اختبار ثغرات XXE"""
        print(Fore.YELLOW + "[*] Testing XXE Vulnerabilities...")

        # تحميل نماذج XXE
        xxe_model = models_and_vectorizers.get("model5")
        xxe_vectorizer = models_and_vectorizers.get("vectorizer5")

        # اختبار حقن XML
        xxe_results = self._test_xml_injection()
        if xxe_results and xxe_model and xxe_vectorizer:
            for result in xxe_results:
                features = xxe_vectorizer.transform([result['payload']])
                result['ml_prediction'] = xxe_model.predict(features)[0]
                result['ml_confidence'] = max(xxe_model.predict_proba(features)[0])

    def _test_xml_injection(self):
        """اختبار ثغرات حقن XML (XXE)"""
        xxe_vulns = []
        xxe_payloads = self.payloads.get('xxe', [])

        # البحث عن نقاط النهاية التي تقبل XML
        xml_urls = []
        try:
            response = self.session.get(self.target_url, timeout=10)
            soup = BeautifulSoup(response.text, 'html.parser')

            # البحث عن نقاط نهاية API التي قد تقبل XML
            for link in soup.find_all('a', href=True):
                if 'api' in link['href'].lower() or 'xml' in link['href'].lower():
                    xml_urls.append(urljoin(self.target_url, link['href']))
        except Exception as e:
            logger.error(f"Error finding XML endpoints: {str(e)}")

        # اختبار كل نقطة نهاية
        for url in xml_urls:
            for payload in xxe_payloads:
                try:
                    headers = {'Content-Type': 'application/xml'}
                    response = self.session.post(url, data=payload, headers=headers, timeout=10)

                    if any(indicator in response.text.lower() for indicator in ['root:', 'file:', 'etc/passwd']):
                        vuln = {
                            'type': 'XXE',
                            'severity': 'High',
                            'description': f'Possible XXE vulnerability with payload: {payload[:50]}...',
                            'url': url,
                            'payload': payload,
                            'response': response.text[:500]
                        }
                        self.vulnerabilities.append(vuln)
                        xxe_vulns.append(vuln)
                except Exception as e:
                    logger.error(f"Error testing XXE: {str(e)}")
        return xxe_vulns

    def test_deserialization(self):
        """اختبار إزالة التسلسل غير الآمن"""
        print(Fore.YELLOW + "[*] Testing Insecure Deserialization...")

        # تحميل نماذج إزالة التسلسل
        deserial_model = models_and_vectorizers.get("model01")
        deserial_vectorizer = models_and_vectorizers.get("vectorizer01")

        # اختبار إزالة تسلسل جافا
        java_results = self._test_java_deserialization()
        if java_results and deserial_model and deserial_vectorizer:
            for result in java_results:
                features = deserial_vectorizer.transform([result['payload']])
                result['ml_prediction'] = deserial_model.predict(features)[0]
                result['ml_confidence'] = max(deserial_model.predict_proba(features)[0])

        # اختبار حقن كائن PHP
        php_results = self._test_php_object_injection()
        if php_results and deserial_model and deserial_vectorizer:
            for result in php_results:
                features = deserial_vectorizer.transform([result['payload']])
                result['ml_prediction'] = deserial_model.predict(features)[0]
                result['ml_confidence'] = max(deserial_model.predict_proba(features)[0])

    def _test_java_deserialization(self):
        """اختبار ثغرات إزالة تسلسل جافا"""
        java_vulns = []
        java_payloads = self.payloads.get('java_deserialization', [])

        # البحث عن نقاط النهاية التي قد تقبل كائنات جافا مسلسلة
        java_urls = []
        try:
            response = self.session.get(self.target_url, timeout=10)
            soup = BeautifulSoup(response.text, 'html.parser')

            # البحث عن نقاط نهاية خاصة بجافا
            for link in soup.find_all('a', href=True):
                if any(ext in link['href'].lower() for ext in ['.jsp', '.do', '.action']):
                    java_urls.append(urljoin(self.target_url, link['href']))
        except Exception as e:
            logger.error(f"Error finding Java endpoints: {str(e)}")

        # اختبار كل نقطة نهاية
        for url in java_urls:
            for payload in java_payloads:
                try:
                    headers = {'Content-Type': 'application/java-serialized-object'}
                    response = self.session.post(url, data=payload, headers=headers, timeout=10)

                    if 'java.lang.' in response.text or 'rO0AB' in response.text:
                        vuln = {
                            'type': 'Insecure Deserialization',
                            'severity': 'Critical',
                            'description': f'Possible Java deserialization vulnerability with payload: {payload[:50]}...',
                            'url': url,
                            'payload': payload,
                            'response': response.text[:500]
                        }
                        self.vulnerabilities.append(vuln)
                        java_vulns.append(vuln)
                except Exception as e:
                    logger.error(f"Error testing Java deserialization: {str(e)}")
        return java_vulns

    def test_ssrf_vulnerabilities(self):
        """اختبار ثغرات SSRF"""
        print(Fore.YELLOW + "[*] Testing SSRF Vulnerabilities...")

        # تحميل نماذج SSRF
        ssrf_model = models_and_vectorizers.get("model00")
        ssrf_vectorizer = models_and_vectorizers.get("vectorizer00")

        # اختبار الطلبات الداخلية
        internal_results = self._test_internal_requests()
        if internal_results and ssrf_model and ssrf_vectorizer:
            for result in internal_results:
                features = ssrf_vectorizer.transform([result['payload']])
                result['ml_prediction'] = ssrf_model.predict(features)[0]
                result['ml_confidence'] = max(ssrf_model.predict_proba(features)[0])

        # اختبار بيانات تعريف السحابة
        cloud_results = self._test_cloud_metadata()
        if cloud_results and ssrf_model and ssrf_vectorizer:
            for result in cloud_results:
                features = ssrf_vectorizer.transform([result['payload']])
                result['ml_prediction'] = ssrf_model.predict(features)[0]
                result['ml_confidence'] = max(ssrf_model.predict_proba(features)[0])

    def _test_internal_requests(self):
        """اختبار تزوير الطلب من جانب الخادم (SSRF)"""
        ssrf_vulns = []
        ssrf_payloads = self.payloads.get('ssrf', [])

        # البحث عن نقاط النهاية التي تقوم بطلبات خارجية
        ssrf_urls = []
        try:
            response = self.session.get(self.target_url, timeout=10)
            soup = BeautifulSoup(response.text, 'html.parser')

            # البحث عن ميزات قد تقوم بطلبات (webhooks، رفع الصور، إلخ)
            for form in soup.find_all('form'):
                if any(word in form.get('action', '').lower() for word in ['webhook', 'callback', 'fetch']):
                    ssrf_urls.append(urljoin(self.target_url, form.get('action', '')))

            for input_tag in soup.find_all('input'):
                if input_tag.get('type', '').lower() == 'file':
                    ssrf_urls.append(urljoin(self.target_url, input_tag.find_parent('form').get('action', '')))
        except Exception as e:
            logger.error(f"Error finding SSRF endpoints: {str(e)}")

        # اختبار كل نقطة نهاية
        for url in ssrf_urls:
            for payload in ssrf_payloads:
                try:
                    # لرفع الملفات
                    if 'file' in payload:
                        files = {'file': ('test.jpg', payload, 'image/jpeg')}
                        response = self.session.post(url, files=files, timeout=10)
                    else:
                        # لمعاملات URL
                        response = self.session.post(url, data={'url': payload}, timeout=10)

                    if any(indicator in response.text.lower() for indicator in ['localhost', '127.0.0.1', 'internal']):
                        vuln = {
                            'type': 'SSRF',
                            'severity': 'High',
                            'description': f'Possible SSRF vulnerability with payload: {payload[:50]}...',
                            'url': url,
                            'payload': payload,
                            'response': response.text[:500]
                        }
                        self.vulnerabilities.append(vuln)
                        ssrf_vulns.append(vuln)
                except Exception as e:
                    logger.error(f"Error testing SSRF: {str(e)}")
        return ssrf_vulns

    def test_idor_vulnerabilities(self):
        """اختبار مراجع الكائن المباشر غير الآمن"""
        print(Fore.YELLOW + "[*] Testing IDOR Vulnerabilities...")

        # تحميل نماذج IDOR
        idor_model = models_and_vectorizers.get("model5")
        idor_vectorizer = models_and_vectorizers.get("vectorizer5")

        # اختبار مراجع الكائن المباشر
        idor_results = self._test_direct_object_references()
        if idor_results and idor_model and idor_vectorizer:
            for result in idor_results:
                features = idor_vectorizer.transform([result['description']])
                result['ml_prediction'] = idor_model.predict(features)[0]
                result['ml_confidence'] = max(idor_model.predict_proba(features)[0])

    def _test_direct_object_references(self):
        """اختبار مراجع الكائن المباشر غير الآمن (IDOR)"""
        idor_vulns = []

        # اختبار أنماط الكائنات الشائعة
        test_objects = [
            {'type': 'user', 'ids': [1, 2, 1000]},
            {'type': 'order', 'ids': [100, 101, 1000]},
            {'type': 'document', 'ids': [1, 2, 1000]}
        ]

        for obj in test_objects:
            for obj_id in obj['ids']:
                url = urljoin(self.target_url, f"api/{obj['type']}s/{obj_id}")
                try:
                    response = self.session.get(url, timeout=10)

                    if response.status_code == 200:
                        vuln = {
                            'type': 'IDOR',
                            'severity': 'Medium',
                            'description': f'Possible IDOR vulnerability accessing {obj["type"]} ID {obj_id}',
                            'url': url,
                            'object_type': obj['type'],
                            'object_id': obj_id,
                            'response': response.text[:500]
                        }
                        self.vulnerabilities.append(vuln)
                        idor_vulns.append(vuln)
                except Exception as e:
                    logger.error(f"Error testing IDOR: {str(e)}")
        return idor_vulns

    def _find_input_points(self, methods=None):
        """البحث عن جميع نقاط الإدخال (النماذج، معاملات الاستعلام) في التطبيق"""
        input_points = {}

        # الزحف عبر التطبيق للعثور على النماذج ومعاملات الاستعلام
        try:
            response = self.session.get(self.target_url, timeout=10)
            soup = BeautifulSoup(response.text, 'html.parser')

            # البحث عن جميع النماذج
            for form in soup.find_all('form'):
                if methods and form.get('method', 'get').lower() not in methods:
                    continue

                action = form.get('action', self.target_url)
                if not action.startswith('http'):
                    action = urljoin(self.target_url, action)

                method = form.get('method', 'get').lower()
                params = {}

                for input_tag in form.find_all('input'):
                    if input_tag.get('name'):
                        params[input_tag['name']] = input_tag.get('value', '')

                input_points[action] = params if method == 'post' else None

            # البحث عن معاملات الاستعلام في الروابط
            for a in soup.find_all('a', href=True):
                url = a['href']
                if url.startswith('http') and self.target_url in url:
                    parsed = urlparse(url)
                    if parsed.query:
                        input_points[url] = None

        except Exception as e:
            logger.error(f"Error finding input points: {str(e)}")

        return input_points

class VulnerabilityCorrelator:
    """فئة لتحليل وربط الثغرات الأمنية"""

    def __init__(self, vulnerabilities):
        """تهيئة المحلل"""
        self.vulnerabilities = vulnerabilities
        self.correlation_graph = None
        self.attack_paths = []
        self.risk_scores = {}
        self.vulnerability_chains = []
        self.mitigation_recommendations = {}

    def correlate_vulnerabilities(self):
        """ربط وتحليل الثغرات الأمنية"""
        try:
            # بناء الرسم البياني للعلاقات
            self._build_correlation_graph()

            # تحليل مسارات الهجوم
            self._analyze_attack_paths()

            # حساب درجات المخاطر
            self._calculate_risk_scores()

            # تحديد سلاسل الثغرات
            self._identify_vulnerability_chains()

            # توليد توصيات التخفيف
            self._generate_mitigation_recommendations()

            # إضافة المعلومات المحسنة للثغرات
            enhanced_vulns = []
            for vuln in self.vulnerabilities:
                enhanced_vuln = vuln.copy()
                enhanced_vuln.update({
                    'risk_score': self.risk_scores.get(vuln['type'], 0),
                    'attack_paths': [path for path in self.attack_paths if vuln['type'] in path],
                    'related_vulnerabilities': self._get_related_vulnerabilities(vuln),
                    'mitigation': self.mitigation_recommendations.get(vuln['type'], 'No specific mitigation available')
                })
                enhanced_vulns.append(enhanced_vuln)

            return enhanced_vulns

        except Exception as e:
            logger.error(f"Error correlating vulnerabilities: {str(e)}")
            return self.vulnerabilities

    def _build_correlation_graph(self):
        """بناء الرسم البياني للعلاقات بين الثغرات"""
        try:
            self.correlation_graph = nx.Graph()

            # إضافة العقد (الثغرات)
            for vuln in self.vulnerabilities:
                self.correlation_graph.add_node(vuln['type'], **vuln)

            # إضافة الحواف (العلاقات)
            for i, vuln1 in enumerate(self.vulnerabilities):
                for j, vuln2 in enumerate(self.vulnerabilities[i+1:], i+1):
                    if self._are_vulnerabilities_related(vuln1, vuln2):
                        self.correlation_graph.add_edge(
                            vuln1['type'],
                            vuln2['type'],
                            weight=self._calculate_relation_weight(vuln1, vuln2)
                        )

        except Exception as e:
            logger.error(f"Error building correlation graph: {str(e)}")
            self.correlation_graph = nx.Graph()

    def _analyze_attack_paths(self):
        """تحليل مسارات الهجوم المحتملة"""
        try:
            if not self.correlation_graph:
                return

            # العثور على جميع المسارات بين الثغرات
            for source in self.correlation_graph.nodes():
                for target in self.correlation_graph.nodes():
                    if source != target:
                        try:
                            paths = list(nx.all_simple_paths(self.correlation_graph, source, target, cutoff=3))
                            if paths:
                                self.attack_paths.extend(paths)
                        except nx.NetworkXNoPath:
                            continue

        except Exception as e:
            logger.error(f"Error analyzing attack paths: {str(e)}")
            self.attack_paths = []

    def _calculate_risk_scores(self):
        """حساب درجات المخاطر للثغرات"""
        try:
            for vuln in self.vulnerabilities:
                base_score = self._severity_to_score(vuln.get('severity', 'medium'))
                confidence_factor = vuln.get('confidence', 0.5)
                impact_factor = self._calculate_impact_factor(vuln)

                risk_score = base_score * confidence_factor * impact_factor
                self.risk_scores[vuln['type']] = min(risk_score, 10.0)  # تقييد الدرجة القصوى

        except Exception as e:
            logger.error(f"Error calculating risk scores: {str(e)}")
            self.risk_scores = {}

    def _identify_vulnerability_chains(self):
        """تحديد سلاسل الثغرات المتصلة"""
        try:
            if not self.attack_paths:
                return

            # تجميع المسارات المتشابهة
            unique_chains = set()
            for path in self.attack_paths:
                chain = tuple(sorted(path))
                unique_chains.add(chain)

            self.vulnerability_chains = list(unique_chains)

        except Exception as e:
            logger.error(f"Error identifying vulnerability chains: {str(e)}")
            self.vulnerability_chains = []

    def _generate_mitigation_recommendations(self):
        """توليد توصيات التخفيف للثغرات"""
        try:
            for vuln in self.vulnerabilities:
                vuln_type = vuln['type']
                self.mitigation_recommendations[vuln_type] = self._get_mitigation_strategy(vuln_type)

        except Exception as e:
            logger.error(f"Error generating mitigation recommendations: {str(e)}")
            self.mitigation_recommendations = {}

    def _get_mitigation_strategy(self, vuln_type):
        """الحصول على استراتيجية التخفيف لنوع معين من الثغرات"""
        strategies = {
            'sql_injection': 'Implement parameterized queries and input validation',
            'xss': 'Implement proper output encoding and Content Security Policy',
            'csrf': 'Implement CSRF tokens and SameSite cookie attributes',
            'idor': 'Implement proper access controls and object-level authorization',
            'lfi': 'Implement proper file path validation and whitelisting',
            'xxe': 'Disable XML external entity processing',
            'ssrf': 'Implement proper URL validation and whitelisting',
            'default': 'Implement proper security controls and follow security best practices'
        }
        return strategies.get(vuln_type, strategies['default'])

    def _severity_to_score(self, severity):
        """تحويل مستوى الخطورة إلى درجة رقمية"""
        severity_scores = {
            'critical': 10.0,
            'high': 8.0,
            'medium': 5.0,
            'low': 2.0
        }
        return severity_scores.get(severity.lower(), 5.0)

    def _calculate_impact_factor(self, vuln):
        """حساب عامل التأثير للثغرة"""
        try:
            impact_factors = {
                'data_exposure': 1.5,
                'system_access': 1.3,
                'privilege_escalation': 1.4,
                'denial_of_service': 1.2,
                'default': 1.0
            }

            impact_type = vuln.get('impact_type', 'default')
            return impact_factors.get(impact_type, impact_factors['default'])

        except Exception as e:
            logger.error(f"Error calculating impact factor: {str(e)}")
            return 1.0

    def _are_vulnerabilities_related(self, vuln1, vuln2):
        """تحديد ما إذا كانت الثغرات مرتبطة"""
        try:
            # التحقق من العلاقات المباشرة
            if vuln1['url'] == vuln2['url']:
                return True

            # التحقق من العلاقات غير المباشرة
            if (vuln1.get('impact_type') == vuln2.get('impact_type') or
                vuln1.get('severity') == vuln2.get('severity')):
                return True

            return False

        except Exception as e:
            logger.error(f"Error checking vulnerability relation: {str(e)}")
            return False

    def _calculate_relation_weight(self, vuln1, vuln2):
        """حساب وزن العلاقة بين الثغرات"""
        try:
            weight = 0.0

            # إضافة وزن للعلاقات المباشرة
            if vuln1['url'] == vuln2['url']:
                weight += 0.5

            # إضافة وزن للعلاقات غير المباشرة
            if vuln1.get('impact_type') == vuln2.get('impact_type'):
                weight += 0.3

            if vuln1.get('severity') == vuln2.get('severity'):
                weight += 0.2

            return min(weight, 1.0)

        except Exception as e:
            logger.error(f"Error calculating relation weight: {str(e)}")
            return 0.0

    def _get_related_vulnerabilities(self, vuln):
        """الحصول على الثغرات المرتبطة بثغرة معينة"""
        try:
            related = []
            for other_vuln in self.vulnerabilities:
                if (other_vuln['type'] != vuln['type'] and
                    self._are_vulnerabilities_related(vuln, other_vuln)):
                    related.append(other_vuln['type'])
            return related

        except Exception as e:
            logger.error(f"Error getting related vulnerabilities: {str(e)}")
            return []

class EnhancedVulnerabilityScanner:
    """ماسح الثغرات المحسن مع مراحل فحص منفصلة وتقارير مفصلة"""
    def __init__(self, target_url):
        self.target_url = target_url
        self.vulnerabilities = {
            'trained_model_vulns': [],
            'zero_day_vulns': [],
            'business_logic_vulns': [],
            'human_error_vulns': [],
            'registration_panel_vulns': [],
            'advanced_vulns': []
        }
        self.penetration_tester = AutomatedPenetrationTester(target_url)
        self.correlator = None
        self.report_paths = {
            'trained_model': 'reports/trained_model_vulnerabilities.json',
            'zero_day': 'reports/zero_day_vulnerabilities.json',
            'business_logic': 'reports/business_logic_vulnerabilities.json',
            'human_error': 'reports/human_error_vulnerabilities.json',
            'registration_panel': 'reports/registration_panel_vulnerabilities.json',
            'advanced': 'reports/advanced_vulnerabilities.json',
            'summary': 'reports/executive_summary.txt',
            'technical': 'reports/technical_report.txt'
        }

    def run_full_scan(self):
        """تشغيل فحص شامل للثغرات مع مراحل منفصلة"""
        print(Fore.CYAN + "[*] Starting Enhanced Vulnerability Scan...")

        # المرحلة 1: فحص الثغرات في النماذج المدربة والبيانات
        print(Fore.YELLOW + "[*] Phase 1: Scanning trained models and data...")
        self._scan_trained_models()

        # المرحلة 2: فحص الثغرات التي لا تعتمد على البيانات
        print(Fore.YELLOW + "[*] Phase 2: Scanning zero-day and business logic vulnerabilities...")
        self._scan_zero_day_vulns()
        self._scan_business_logic_vulns()

        # المرحلة 3: فحص أخطاء المستخدمين ولوحات التسجيل
        print(Fore.YELLOW + "[*] Phase 3: Scanning human errors and registration panels...")
        self._scan_human_errors()
        self._scan_registration_panels()

        # المرحلة 4: الفحص المتقدم
        print(Fore.YELLOW + "[*] Phase 4: Running advanced scanning...")
        self._run_advanced_scanning()

        # إنشاء التقارير
        self._generate_reports()

        print(Fore.GREEN + "[+] Enhanced Vulnerability Scan completed")
        return self.vulnerabilities

    def _scan_trained_models(self):
        """فحص الثغرات في النماذج المدربة والبيانات"""
        try:
            # تحميل النماذج المدربة
            bert_model = load_bert_model()
            lstm_model = load_lstm_model()
            cnn_model = load_cnn_model()

            # فحص كل نموذج
            for model in [bert_model, lstm_model, cnn_model]:
                vulnerabilities = self._analyze_model_vulnerabilities(model)
                self.vulnerabilities['trained_model_vulns'].extend(vulnerabilities)

        except Exception as e:
            logger.error(f"Error scanning trained models: {str(e)}")

    def _scan_zero_day_vulns(self):
        """فحص ثغرات الزيرو دي"""
        try:
            zero_day_vulns = detect_zero_day_vulnerabilities(self.target_url, None)
            self.vulnerabilities['zero_day_vulns'].extend(zero_day_vulns)
        except Exception as e:
            logger.error(f"Error scanning zero-day vulnerabilities: {str(e)}")

    def _scan_business_logic_vulns(self):
        """فحص ثغرات منطق الأعمال"""
        try:
            business_logic_vulns = detect_business_logic_vulnerabilities_enhanced(
                self.vulnerabilities['trained_model_vulns'],
                self.target_url
            )
            self.vulnerabilities['business_logic_vulns'].extend(business_logic_vulns)
        except Exception as e:
            logger.error(f"Error scanning business logic vulnerabilities: {str(e)}")

    def _scan_human_errors(self):
        """فحص الأخطاء البشرية"""
        try:
            human_error_vulns = detect_human_errors(self.target_url, None)
            self.vulnerabilities['human_error_vulns'].extend(human_error_vulns)
        except Exception as e:
            logger.error(f"Error scanning human errors: {str(e)}")

    def _scan_registration_panels(self):
        """فحص لوحات التسجيل"""
        try:
            registration_vulns = test_registration_panels(self.target_url)
            self.vulnerabilities['registration_panel_vulns'].extend(registration_vulns)
        except Exception as e:
            logger.error(f"Error scanning registration panels: {str(e)}")

    def _run_advanced_scanning(self):
        """تشغيل الفحص المتقدم"""
        try:
            # تشغيل اختبار الاختراق الآلي
            pentest_results = self.penetration_tester.run_full_penetration_test()
            self.vulnerabilities['advanced_vulns'].extend(pentest_results)

            # تشغيل الفحص التقليدي للثغرات
            scan_results = self._run_traditional_scan()
            self.vulnerabilities['advanced_vulns'].extend(scan_results)

        except Exception as e:
            logger.error(f"Error in advanced scanning: {str(e)}")

    def _generate_reports(self):
        """إنشاء تقارير مفصلة لكل نوع من الثغرات"""
        print(Fore.YELLOW + "[*] Generating Detailed Reports...")

        try:
            # إنشاء دليل التقارير
            os.makedirs('reports', exist_ok=True)

            # حفظ كل نوع من الثغرات في ملف JSON منفصل
            for vuln_type, vulns in self.vulnerabilities.items():
                if vulns:  # فقط إذا وجدت ثغرات
                    report_path = self.report_paths.get(vuln_type)
                    if report_path:
                        with open(report_path, 'w', encoding='utf-8') as f:
                            json.dump(vulns, f, indent=2, ensure_ascii=False)

            # إنشاء ملخص تنفيذي
            self._generate_executive_summary()

            # إنشاء تقرير فني
            self._generate_technical_report()

            print(Fore.GREEN + "[+] Reports generated in 'reports' directory")
        except Exception as e:
            logger.error(f"Error generating reports: {str(e)}")

    def _generate_executive_summary(self):
        """إنشاء تقرير ملخص تنفيذي"""
        try:
            summary = f"""
            Executive Summary
            =================

            Target: {self.target_url}
            Scan Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

            Vulnerability Counts by Type:
            """

            # إضافة عدد الثغرات لكل نوع
            for vuln_type, vulns in self.vulnerabilities.items():
                if vulns:
                    summary += f"- {vuln_type.replace('_', ' ').title()}: {len(vulns)}\n"

            # إضافة أهم الثغرات الحرجة
            critical_vulns = []
            for vulns in self.vulnerabilities.values():
                critical_vulns.extend([v for v in vulns if v.get('severity') == 'Critical'])

            if critical_vulns:
                summary += "\nTop Critical Vulnerabilities:\n"
                for vuln in sorted(critical_vulns, key=lambda x: x.get('confidence', 0), reverse=True)[:5]:
                    summary += f"- {vuln['type']} at {vuln.get('url', 'N/A')} (Confidence: {vuln.get('confidence', 'N/A')})\n"

            # إضافة التوصيات
            summary += """
            Recommendations:
            1. Address critical vulnerabilities immediately
            2. Review and fix business logic vulnerabilities
            3. Implement proper security controls
            4. Regular security scanning and penetration testing
            """

            # حفظ الملخص
            with open(self.report_paths['summary'], 'w', encoding='utf-8') as f:
                f.write(summary)

        except Exception as e:
            logger.error(f"Error generating executive summary: {str(e)}")

    def _generate_technical_report(self):
        """إنشاء تقرير فني مفصل"""
        try:
            report = f"""
            Technical Vulnerability Report
            =============================

            Target: {self.target_url}
            Scan Date: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
            """

            # إضافة تفاصيل لكل نوع من الثغرات
            for vuln_type, vulns in self.vulnerabilities.items():
                if vulns:
                    report += f"\n{vuln_type.replace('_', ' ').title()} Vulnerabilities ({len(vulns)} found)\n"
                    report += "-" * (len(vuln_type) + 20) + "\n"

                    for vuln in vulns[:5]:  # عرض أول 5 من كل نوع
                        report += f"""
                        Type: {vuln.get('type', 'Unknown')}
                        URL: {vuln.get('url', 'N/A')}
                        Severity: {vuln.get('severity', 'Medium')}
                        Description: {vuln.get('description', 'No description')}
                        Confidence: {vuln.get('confidence', 'N/A')}
                        Details: {vuln.get('details', {}).__str__()[:200]}...
                        """

                    if len(vulns) > 5:
                        report += f"\n... and {len(vulns) - 5} more {vuln_type} vulnerabilities\n"

            # حفظ التقرير الفني
            with open(self.report_paths['technical'], 'w', encoding='utf-8') as f:
                f.write(report)

        except Exception as e:
            logger.error(f"Error generating technical report: {str(e)}")

    def _analyze_model_vulnerabilities(self, model):
        """تحليل الثغرات في نموذج مدرب"""
        vulnerabilities = []
        try:
            # تحليل نقاط الضعف في النموذج
            if hasattr(model, 'layers'):
                for layer in model.layers:
                    if isinstance(layer, (Dense, Conv1D)):
                        # تحليل الأوزان للقيم المتطرفة
                        weights = layer.get_weights()[0]
                        if len(weights) > 0:
                            mean = np.mean(weights)
                            std = np.std(weights)
                            outliers = np.abs(weights - mean) > 3 * std
                            if np.any(outliers):
                                vulnerabilities.append({
                                    'type': 'Model Weight Anomaly',
                                    'severity': 'Medium',
                                    'description': f'Potential weight anomalies detected in {layer.name}',
                                    'confidence': 0.7,
                                    'details': {
                                        'layer': layer.name,
                                        'outlier_count': np.sum(outliers),
                                        'mean': float(mean),
                                        'std': float(std)
                                    }
                                })

            # تحليل حساسية النموذج للهجمات
            if isinstance(model, (Sequential, tf.keras.Model)):
                try:
                    classifier = TensorFlowV2Classifier(
                        model=model,
                        nb_classes=2,
                        input_shape=(None,),
                        loss_object=tf.keras.losses.BinaryCrossentropy()
                    )
                    attack = FastGradientMethod(estimator=classifier, eps=0.1)
                    # يمكن إضافة اختبارات الهجوم هنا

                except Exception as e:
                    logger.warning(f"Could not perform adversarial testing: {str(e)}")

            # تحليل انحياز النموذج
            if hasattr(model, 'predict'):
                try:
                    # يمكن إضافة اختبارات الانحياز هنا
                    pass
                except Exception as e:
                    logger.warning(f"Could not perform bias analysis: {str(e)}")

        except Exception as e:
            logger.error(f"Error analyzing model vulnerabilities: {str(e)}")

        return vulnerabilities

    def _run_traditional_scan(self):
        """تشغيل الفحص التقليدي للثغرات مع تحسين الأداء"""
        print(Fore.YELLOW + "[*] Running Traditional Vulnerability Scan...")

        try:
            # جلب جميع الروابط من URL الهدف
            links = fetch_links(self.target_url)
            print(Fore.GREEN + f"[+] Found {len(links)} links to scan")

            # استخدام ThreadPoolExecutor للفحص المتوازي
            vulnerabilities = []
            with ThreadPoolExecutor(max_workers=10) as executor:
                future_to_link = {
                    executor.submit(self._scan_link, link): link
                    for link in links[:1000]  # تحديد حد للروابط
                }

                for future in as_completed(future_to_link):
                    link = future_to_link[future]
                    try:
                        vulns = future.result()
                        vulnerabilities.extend(vulns)
                    except Exception as e:
                        logger.error(f"Error scanning {link}: {str(e)}")

            return vulnerabilities
        except Exception as e:
            logger.error(f"Error in traditional scan: {str(e)}")
            return []

    def _scan_link(self, link):
        """فحص رابط واحد للثغرات"""
        try:
            response = session.get(link, headers=HEADERS, timeout=30)
            content = response.text

            # البحث عن الثغرات في المحتوى
            vulns = find_vulnerabilities(content, link)

            # فحوصات إضافية
            self._check_for_misconfigurations(link, response)
            self._check_for_sensitive_data(link, response)

            return vulns
        except Exception as e:
            logger.error(f"Error scanning link {link}: {str(e)}")
            return []

    def _check_for_misconfigurations(self, url, response):
        """التحقق من إعدادات خادم الويب الخاطئة"""
        try:
            # التحقق من العناوين غير الآمنة
            insecure_headers = self._analyze_headers(response.headers)
            if insecure_headers:
                self.vulnerabilities['advanced_vulns'].append({
                    'type': 'Misconfiguration',
                    'severity': 'Medium',
                    'description': 'Insecure HTTP headers detected',
                    'url': url,
                    'details': insecure_headers
                })

            # التحقق من طرق HTTP غير الضرورية
            allowed_methods = self._check_http_methods(url)
            if 'PUT' in allowed_methods or 'DELETE' in allowed_methods:
                self.vulnerabilities['advanced_vulns'].append({
                    'type': 'Misconfiguration',
                    'severity': 'Medium',
                    'description': 'Potentially dangerous HTTP methods allowed',
                    'url': url,
                    'details': {'allowed_methods': allowed_methods}
                })
        except Exception as e:
            logger.error(f"Error checking misconfigurations: {str(e)}")

    def _analyze_headers(self, headers):
        """تحليل عناوين HTTP للقضايا الأمنية"""
        issues = []

        # التحقق من العناوين الأمنية المفقودة
        security_headers = [
            'Content-Security-Policy',
            'X-Content-Type-Options',
            'X-Frame-Options',
            'Strict-Transport-Security'
        ]

        for header in security_headers:
            if header not in headers:
                issues.append(f'Missing security header: {header}')

        # التحقق من إعدادات الكوكيز غير الآمنة
        if 'Set-Cookie' in headers:
            cookie = headers['Set-Cookie']
            if 'Secure' not in cookie:
                issues.append('Cookie missing Secure flag')
            if 'HttpOnly' not in cookie:
                issues.append('Cookie missing HttpOnly flag')
            if 'SameSite' not in cookie:
                issues.append('Cookie missing SameSite attribute')

        return issues

    def _check_http_methods(self, url):
        """التحقق من طرق HTTP المسموح بها"""
        try:
            response = session.request('OPTIONS', url, timeout=5)
            return response.headers.get('Allow', '').split(', ')
        except:
            return []

    def _check_for_sensitive_data(self, url, response):
        """التحقق من تسريب البيانات الحساسة"""
        try:
            sensitive_patterns = {
                'API Keys': r'[a-zA-Z0-9]{32}',
                'Email Addresses': r'[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}',
                'Credit Card Numbers': r'\b(?:\d[ -]*?){13,16}\b',
                'Private IPs': r'\b(10\.\d{1,3}\.\d{1,3}\.\d{1,3}|172\.(1[6-9]|2[0-9]|3[0-1])\.\d{1,3}\.\d{1,3}|192\.168\.\d{1,3}\.\d{1,3})\b'
            }

            found_data = {}
            for name, pattern in sensitive_patterns.items():
                matches = re.findall(pattern, response.text)
                if matches:
                    found_data[name] = list(set(matches))[:3]  # عرض أول 3 نتائج فريدة

            if found_data:
                self.vulnerabilities['advanced_vulns'].append({
                    'type': 'Sensitive Data Exposure',
                    'severity': 'High',
                    'description': 'Potential sensitive data found in response',
                    'url': url,
                    'details': found_data
                })
        except Exception as e:
            logger.error(f"Error checking for sensitive data: {str(e)}")

def human_in_the_loop_system(vulnerabilities):
    """نظام Human-in-the-loop المحسن للتحقق من الثغرات"""
    print(Fore.YELLOW + "[*] Running Enhanced Human-in-the-Loop System...")

    try:
        confirmed_vulnerabilities = []
        uncertain_vulnerabilities = []
        false_positives = []

        # تحليل أولي للثغرات باستخدام نماذج متعددة
        for vuln in vulnerabilities:
            # حساب درجة الثقة بناء على نماذج متعددة
            confidence_score = calculate_vulnerability_confidence(vuln)
            vuln['confidence_score'] = confidence_score

            # تأكيد جميع الثغرات تلقائياً
            confirmed_vulnerabilities.append(vuln)
            logger.info(f"Auto-confirmed vulnerability: {vuln['prediction']}")
            print(Fore.GREEN + f"[+] Auto-confirmed: {vuln['prediction']} (Confidence: {confidence_score:.2f})")

        # تحليل إحصائي للنتائج
        analyze_review_results(confirmed_vulnerabilities, false_positives)

        return {
            'confirmed': confirmed_vulnerabilities,
            'false_positives': false_positives,
            'stats': {
                'total_reviewed': len(vulnerabilities),
                'auto_confirmed': len(vulnerabilities),
                'human_reviewed': 0,
                'false_positives': 0
            }
        }

    except Exception as e:
        logger.error(f"Error in enhanced human-in-the-loop system: {str(e)}")
        print(Fore.RED + f"[-] Critical error in review system: {str(e)}")
        return {
            'confirmed': vulnerabilities,
            'false_positives': [],
            'error': str(e)
        }

def calculate_vulnerability_confidence(vuln):
    """حساب درجة ثقة مركبة للثغرة بناء على نماذج متعددة"""
    try:
        # عوامل الترجيح للنماذج المختلفة
        model_weights = {
            'lstm_model': 0.3,
            'cnn_model': 0.3,
            'model0': 0.2,
            'model5': 0.2
        }

        total_weight = 0
        weighted_sum = 0

        for model_name, weight in model_weights.items():
            if model_name in vuln.get('model_used', '').lower():
                # تحويل التنبؤ إلى قيمة رقمية
                if isinstance(vuln['prediction'], str):
                    pred_value = 1.0 if 'vulnerable' in vuln['prediction'].lower() else 0.0
                else:
                    pred_value = float(vuln['prediction'])

                weighted_sum += pred_value * weight
                total_weight += weight

        # تجنب القسمة على الصفر
        if total_weight == 0:
            return 0.5  # قيمة افتراضية

        return weighted_sum / total_weight

    except Exception as e:
        logger.warning(f"Error calculating confidence score: {str(e)}")
        return 0.5

def display_vulnerability_details(vuln):
    """عرض تفاصيل الثغرة بطريقة منظمة"""
    print(f"\n{Fore.CYAN}Type:{Style.RESET_ALL} {vuln.get('prediction', 'Unknown')}")
    print(f"{Fore.CYAN}Severity:{Style.RESET_ALL} {vuln.get('severity', 'Medium')}")
    print(f"{Fore.CYAN}Confidence:{Style.RESET_ALL} {vuln.get('confidence_score', 0.5):.2f}")
    print(f"{Fore.CYAN}Location:{Style.RESET_ALL} {vuln.get('direct_link', 'N/A')}")

    print(f"\n{Fore.YELLOW}Code Snippet:{Style.RESET_ALL}")
    print(f"Line {vuln.get('line_number', '?')}: {vuln.get('line_content', 'No content')[:200]}")

    print(f"\n{Fore.YELLOW}Description:{Style.RESET_ALL}")
    print(vuln.get('description', 'No description available')[:500])

    if 'payload' in vuln:
        print(f"\n{Fore.YELLOW}Payload:{Style.RESET_ALL}")
        print(vuln['payload'][:200])

def show_advanced_details(vuln):
    """عرض تفاصيل متقدمة للثغرة"""
    print(f"\n{Fore.BLUE}=== Advanced Details ===")
    print(f"Model Used: {vuln.get('model_used', 'Unknown')}")
    print(f"Request Method: {vuln.get('request_details', {}).get('method', 'GET')}")
    print(f"Response Status: {vuln.get('response_details', {}).get('status_code', 'Unknown')}")

    if 'response_content' in vuln:
        print(f"\nResponse Sample:")
        print(vuln['response_content'][:200] + "...")

    print("="*30 + Style.RESET_ALL)

def analyze_review_results(confirmed, false_positives):
    """تحليل إحصائي لنتائج المراجعة"""
    try:
        total = len(confirmed) + len(false_positives)
        if total == 0:
            return

        confirmation_rate = len(confirmed) / total
        fp_rate = len(false_positives) / total

        logger.info(f"Review Analysis - Confirmed: {len(confirmed)} ({confirmation_rate:.1%}), "
                   f"False Positives: {len(false_positives)} ({fp_rate:.1%})")

        print(f"\n{Fore.GREEN}=== Review Summary ===")
        print(f"Confirmed Vulnerabilities: {len(confirmed)} ({confirmation_rate:.1%})")
        print(f"False Positives: {len(false_positives)} ({fp_rate:.1%})")

        # تحليل حسب الشدة
        severity_counts = {'Critical': 0, 'High': 0, 'Medium': 0, 'Low': 0}
        for vuln in confirmed:
            severity = vuln.get('severity', 'Medium')
            severity_counts[severity] += 1

        print(f"\nSeverity Breakdown:")
        for severity, count in severity_counts.items():
            if count > 0:
                print(f"- {severity}: {count}")

        print("="*30 + Style.RESET_ALL)

    except Exception as e:
        logger.error(f"Error analyzing review results: {str(e)}")

def create_output_folders():
    """إنشاء مجلدات الإخراج مع معالجة الأخطاء المحسنة"""
    logger.info("Creating output folders")

    folders = [
        "reports",
        "screenshots",
        "business_logic",
        "zero_day",
        "human_errors",
        "registration_panels",
        "logs",
        "scan_states"
    ]

    success = True
    created_folders = []

    for folder in folders:
        try:
            # إنشاء المجلد مع التحقق من وجوده
            if not os.path.exists(folder):
                os.makedirs(folder, exist_ok=True)
                created_folders.append(folder)
                logger.info(f"Created folder: {folder}")
                print(Fore.GREEN + f"[+] Created folder: {folder}")
            else:
                logger.info(f"Folder already exists: {folder}")
                print(Fore.YELLOW + f"[*] Folder already exists: {folder}")
        except PermissionError:
            logger.error(f"Permission denied while creating folder: {folder}")
            print(Fore.RED + f"[-] Permission denied while creating folder: {folder}")
            success = False
        except Exception as e:
            logger.error(f"Error creating folder {folder}: {str(e)}")
            print(Fore.RED + f"[-] Error creating folder {folder}: {str(e)}")
            success = False

    if success:
        logger.info("All output folders created successfully")
        print(Fore.GREEN + "[+] All output folders created successfully")
        return True
    else:
        logger.error("Failed to create some output folders")
        print(Fore.RED + "[-] Failed to create some output folders")
        return False

def save_scan_state(vulnerabilities, output_path, links, current_link_index):
    """حفظ حالة الفحص الحالية لاستئنافها لاحقاً"""
    try:
        state = {
            "vulnerabilities": vulnerabilities,
            "output_path": output_path,
            "links": list(links),
            "current_link_index": current_link_index
        }
        os.makedirs("scan_states", exist_ok=True)
        state_path = os.path.join("scan_states", "scan_state.pkl")
        with open(state_path, 'wb') as f:
            joblib.dump(state, f, compress=('zlib', 6))
        print(Fore.GREEN + f"[+] Scan state saved. Last scanned link index: {current_link_index}")
    except Exception as e:
        logger.error(f"Error saving scan state: {str(e)}")
        print(Fore.RED + f"[-] Error saving scan state: {str(e)}")

def load_scan_state():
    """تحميل حالة الفحص السابقة إذا وجدت"""
    try:
        state_path = os.path.join("scan_states", "scan_state.pkl")
        if os.path.exists(state_path):
            with open(state_path, 'rb') as f:
                state = joblib.load(f)
            if state["current_link_index"] >= len(state["links"]):
                state["current_link_index"] = len(state["links"]) - 1
            print(Fore.GREEN + f"[+] Scan state loaded. Resuming from link {state['current_link_index'] + 1}/{len(state['links'])}")
            return state
        else:
            print(Fore.RED + "[-] No saved scan state found.")
            return None
    except Exception as e:
        logger.error(f"Error loading scan state: {str(e)}")
        print(Fore.RED + f"[-] Error loading scan state: {str(e)}")
        return None

async def process_screenshots_parallel(link, vuln, output_path, screenshot_filename):
    """معالجة التقاط الصور بشكل متوازي مع دعم التزامن"""
    try:
        # إنشاء مجلد لقطات الشاشة إذا لم يكن موجوداً
        screenshots_dir = os.path.join(output_path, "screenshots")
        os.makedirs(screenshots_dir, exist_ok=True)

        # التقاط لقطة الشاشة الأساسية
        base_screenshot = await capture_screenshot_async(
            link,
            screenshots_dir,
            f"base_{screenshot_filename}"
        )

        # التقاط لقطة شاشة محسنة مع تفاعلات
        actions = [
            {'type': 'input', 'xpath': '//input[contains(@name, "user")]', 'text': 'testuser'},
            {'type': 'input', 'xpath': '//input[contains(@name, "email")]', 'text': '<EMAIL>'},
            {'type': 'click', 'xpath': '//button[contains(@type, "submit")]'}
        ]
        enhanced_screenshot = await capture_screenshot_enhanced_async(
            link,
            screenshots_dir,
            f"enhanced_{screenshot_filename}",
            actions
        )

        # إنشاء صورة لثغرة المشروحة
        screenshot_path = os.path.join(screenshots_dir, screenshot_filename)
        await capture_vulnerability_image_async(
            vuln.get('line', ''),
            vuln.get('line_number', 0),
            vuln.get('prediction', ''),
            vuln.get('model_used', ''),
            link,
            screenshot_filename,
            screenshots_dir,
            exploit_result=vuln.get('exploit_result'),
            request_details=vuln.get('request_details'),
            response_details=vuln.get('response_details'),
            severity=vuln.get('severity'),
            description=vuln.get('description'),
            payload=vuln.get('payload'),
            exploit_method=vuln.get('exploit_method')
        )

        return {
            'base_screenshot': base_screenshot,
            'enhanced_screenshot': enhanced_screenshot,
            'annotated_screenshot': screenshot_path
        }
    except Exception as e:
        logger.error(f"Error in parallel screenshot processing: {str(e)}")
        return None

async def capture_screenshot_async(url, output_path, filename):
    """التقاط لقطة شاشة عادية للصفحة باستخدام Selenium مع دعم التزامن"""
    try:
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        options.add_argument('--ignore-certificate-errors')
        options.add_argument('--disable-notifications')
        options.add_argument('--disable-popup-blocking')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-infobars')
        options.add_argument('--disable-web-security')
        options.add_argument('--allow-running-insecure-content')

        driver = webdriver.Chrome(
            service=Service(ChromeDriverManager().install()),
            options=options
        )

        try:
            driver.get(url)
            await asyncio.sleep(2)  # انتظار تحميل الصفحة

            # التقاط الصورة الكاملة للصفحة
            total_height = driver.execute_script("return document.body.scrollHeight")
            viewport_height = driver.execute_script("return window.innerHeight")
            driver.set_window_size(1920, total_height)

            # التمرير التدريجي للصفحة
            for i in range(0, total_height, viewport_height):
                driver.execute_script(f"window.scrollTo(0, {i});")
                await asyncio.sleep(0.1)

            screenshot_path = os.path.join(output_path, filename)
            driver.save_screenshot(screenshot_path)

            # تحسين الصورة
            try:
                img = Image.open(screenshot_path)
                # تحسين التباين
                enhancer = ImageEnhance.Contrast(img)
                img = enhancer.enhance(1.5)
                # تحسين الحدة
                enhancer = ImageEnhance.Sharpness(img)
                img = enhancer.enhance(2.0)
                # تحسين السطوع
                enhancer = ImageEnhance.Brightness(img)
                img = enhancer.enhance(1.1)
                # حفظ الصورة المحسنة
                img.save(screenshot_path, quality=95, optimize=True)
            except Exception as e:
                logger.warning(f"Image enhancement failed: {str(e)}")

            print(Fore.GREEN + f"[+] Screenshot saved at: {screenshot_path}")
            return screenshot_path
        finally:
            driver.quit()
    except Exception as e:
        logger.error(f"Error capturing screenshot: {str(e)}")
        print(Fore.RED + f"[-] Error capturing screenshot: {str(e)}")
        return None

async def capture_screenshot_enhanced_async(url, output_path, filename, actions=None):
    """التقاط لقطة شاشة مع تنفيذ تفاعلات (inputs/clicks) قبل الالتقاط مع دعم التزامن"""
    try:
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        options.add_argument('--ignore-certificate-errors')
        options.add_argument('--disable-notifications')
        options.add_argument('--disable-popup-blocking')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-infobars')
        options.add_argument('--disable-web-security')
        options.add_argument('--allow-running-insecure-content')

        driver = webdriver.Chrome(
            service=Service(ChromeDriverManager().install()),
            options=options
        )

        try:
            driver.get(url)
            await asyncio.sleep(3)  # انتظار تحميل الصفحة

            if actions:
                for action in actions:
                    try:
                        if action['type'] == 'click':
                            element = WebDriverWait(driver, 10).until(
                                EC.presence_of_element_located((By.XPATH, action['xpath']))
                            )
                            ActionChains(driver).move_to_element(element).click().perform()
                            await asyncio.sleep(2)
                        elif action['type'] == 'input':
                            element = WebDriverWait(driver, 10).until(
                                EC.presence_of_element_located((By.XPATH, action['xpath']))
                            )
                            element.clear()
                            element.send_keys(action['text'])
                            await asyncio.sleep(1)
                    except Exception as e:
                        logger.warning(f"Failed to perform action {action}: {str(e)}")

            # التقاط الصورة الكاملة للصفحة
            total_height = driver.execute_script("return document.body.scrollHeight")
            viewport_height = driver.execute_script("return window.innerHeight")
            driver.set_window_size(1920, total_height)

            # التمرير التدريجي للصفحة
            for i in range(0, total_height, viewport_height):
                driver.execute_script(f"window.scrollTo(0, {i});")
                await asyncio.sleep(0.1)

            screenshot_path = os.path.join(output_path, filename)
            driver.save_screenshot(screenshot_path)

            # تحسين الصورة
            try:
                img = Image.open(screenshot_path)
                # تحسين التباين
                enhancer = ImageEnhance.Contrast(img)
                img = enhancer.enhance(1.5)
                # تحسين الحدة
                enhancer = ImageEnhance.Sharpness(img)
                img = enhancer.enhance(2.0)
                # تحسين السطوع
                enhancer = ImageEnhance.Brightness(img)
                img = enhancer.enhance(1.1)
                # حفظ الصورة المحسنة
                img.save(screenshot_path, quality=95, optimize=True)
            except Exception as e:
                logger.warning(f"Image enhancement failed: {str(e)}")

            print(Fore.GREEN + f"[+] Enhanced screenshot saved at: {screenshot_path}")
            return screenshot_path
        finally:
            driver.quit()
    except Exception as e:
        logger.error(f"Error capturing enhanced screenshot: {str(e)}")
        print(Fore.RED + f"[-] Error capturing enhanced screenshot: {str(e)}")
        return None

async def capture_vulnerability_image_async(line, line_number, prediction, model_used, url, filename, output_path, **kwargs):
    """إنشاء صورة الثغرة المشروحة مع دعم التزامن"""
    try:
        # التقاط لقطة الشاشة الأساسية
        screenshot_path = await capture_screenshot_async(url, output_path, filename)
        if not screenshot_path:
            return None

        # تحسين الصورة وإضافة التعليقات
        try:
            img = Image.open(screenshot_path)
            # إضافة التعليقات والعلامات
            draw = ImageDraw.Draw(img)
            font = ImageFont.truetype("arial.ttf", 24)

            # إضافة معلومات الثغرة
            draw.text((10, 10), f"Line {line_number}: {line}", fill=(255, 0, 0), font=font)
            draw.text((10, 40), f"Prediction: {prediction}", fill=(255, 0, 0), font=font)
            draw.text((10, 70), f"Model: {model_used}", fill=(255, 0, 0), font=font)

            # إضافة معلومات إضافية
            y = 100
            for key, value in kwargs.items():
                if value:
                    draw.text((10, y), f"{key}: {value}", fill=(255, 0, 0), font=font)
                    y += 30

            # حفظ الصورة المحسنة
            img.save(screenshot_path, quality=95, optimize=True)
            print(Fore.GREEN + f"[+] Annotated vulnerability image saved at: {screenshot_path}")
            return screenshot_path
        except Exception as e:
            logger.warning(f"Image annotation failed: {str(e)}")
            return screenshot_path
    except Exception as e:
        logger.error(f"Error creating vulnerability image: {str(e)}")
        print(Fore.RED + f"[-] Error creating vulnerability image: {str(e)}")
        return None

def enhanced_scan(url, resume=False):
    """
    مسح محسن مع دعم الاستئناف والتقاط الصور الفوري
    """
    try:
        # تهيئة المجلدات
        output_path = create_output_folders()

        # تحميل حالة المسح السابقة إذا طلب
        if resume:
            vulnerabilities, links, current_link_index = load_scan_state()
            if vulnerabilities is None:
                vulnerabilities = []
            if links is None:
                links = []
            if current_link_index is None:
                current_link_index = 0
        else:
            vulnerabilities = []
            links = []
            current_link_index = 0

        # جمع الروابط
        print(Fore.YELLOW + "\n[*] Phase 1: Link Collection")
        print(Fore.CYAN + "----------------------------------")

        if not links:
            print(Fore.YELLOW + "[*] Crawling internal links...")
            links = crawl_internal_links(url)
            print(Fore.GREEN + f"[+] Found {len(links)} internal links")

        # مسح الروابط
        print(Fore.YELLOW + "\n[*] Phase 2: Vulnerability Scanning")
        print(Fore.CYAN + "----------------------------------")

        for i in range(current_link_index, len(links)):
            link = links[i]
            print(Fore.CYAN + f"[*] Scanning link {i + 1}/{len(links)}: {link}")

            try:
                # مسح الرابط
                response = requests.get(link, headers=HEADERS, timeout=30)
                link_vulnerabilities = find_vulnerabilities(response.text, link)

                # إضافة الثغرات المكتشفة
                if link_vulnerabilities:
                    vulnerabilities.extend(link_vulnerabilities)
                    print(Fore.GREEN + f"[+] Found {len(link_vulnerabilities)} vulnerabilities in {link}")

                    # التقاط الصور للثغرات بشكل متوازي
                    print(Fore.YELLOW + "[*] Capturing vulnerability screenshots in parallel...")
                    with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
                        futures = []
                        for vuln in link_vulnerabilities:
                            screenshot_filename = f"vuln_{len(vulnerabilities)}_{vuln['type'].lower().replace(' ', '_')}.png"
                            future = executor.submit(
                                process_screenshots_parallel,
                                link,
                                vuln,
                                output_path,
                                screenshot_filename
                            )
                            futures.append((vuln, future))

                        for vuln, future in futures:
                            try:
                                screenshot_results = future.result()
                                if screenshot_results:
                                    vuln.update(screenshot_results)
                                    print(Fore.GREEN + f"[+] Screenshots captured for {vuln['type']} vulnerability")
                            except Exception as e:
                                logger.error(f"Error processing screenshots for {vuln['type']}: {str(e)}")
                                print(Fore.RED + f"[-] Error processing screenshots for {vuln['type']}: {str(e)}")

                # حفظ حالة المسح بعد كل رابط
                print(Fore.YELLOW + "[*] Saving scan state...")
                save_scan_state(vulnerabilities, output_path, links, i)
                print(Fore.GREEN + "[+] Scan state saved successfully")

            except Exception as e:
                logger.error(f"Error scanning link {link}: {str(e)}")
                print(Fore.RED + f"[-] Error scanning link {link}: {str(e)}")
                continue

        # تحليل النتائج
        if vulnerabilities:
            print(Fore.YELLOW + "\n[*] Phase 3: Result Analysis")
            print(Fore.CYAN + "----------------------------------")

            # تحليل الثغرات المترابطة
            print(Fore.YELLOW + "[*] Analyzing vulnerability correlations...")
            correlator = VulnerabilityCorrelator(vulnerabilities)
            correlator.correlate_vulnerabilities()

            # تحليل الثغرات المنطقية
            print(Fore.YELLOW + "[*] Analyzing business logic vulnerabilities...")
            vulnerabilities.extend(detect_business_logic_vulnerabilities_enhanced(vulnerabilities, url))

            # نظام التأكد البشري
            print(Fore.YELLOW + "[*] Starting human verification...")
            vulnerabilities = human_in_the_loop_system(vulnerabilities)

            # حفظ النتائج النهائية
            print(Fore.YELLOW + "[*] Saving final results...")
            save_reports(vulnerabilities, os.path.join(output_path, "reports"))
            print(Fore.GREEN + f"\n[+] Scan completed successfully! Found {len(vulnerabilities)} vulnerabilities")

        return vulnerabilities
    except Exception as e:
        logger.error(f"Error in enhanced scan: {str(e)}")
        print(Fore.RED + f"[-] Error in enhanced scan: {str(e)}")
        return None

def init_scan_environment():
    """تهيئة بيئة الفحص"""
    os.makedirs('scan_states', exist_ok=True)
    os.makedirs('reports', exist_ok=True)
    os.makedirs('screenshots', exist_ok=True)

    # تحسين إعدادات الجلسة
    session.headers.update(HEADERS)
    session.max_redirects = 5
    session.verify = False  # فقط لأغراض الاختبار

def capture_enhanced_screenshots(url, output_path):
    """التقاط لقطات شاشة محسنة مع سياق الثغرة"""
    try:
        # لقطة الشاشة الأساسية
        base_screenshot = capture_screenshot(url, output_path, f"base_{hash(url)}.png")

        # لقطة مع تفاعلات محتملة
        actions = [
            {'type': 'input', 'xpath': '//input[contains(@name, "user")]', 'text': 'testuser'},
            {'type': 'input', 'xpath': '//input[contains(@name, "email")]', 'text': '<EMAIL>'},
            {'type': 'click', 'xpath': '//button[contains(@type, "submit")]'}
        ]
        interactive_screenshot = capture_screenshot_enhanced(url, output_path, f"interactive_{hash(url)}.png", actions)

        return base_screenshot, interactive_screenshot
    except Exception as e:
        logger.warning(f"Screenshot capture failed for {url}: {str(e)}")
        return None, None

def save_reports(vulnerabilities, screenshot_dir):
    """حفظ التقارير النهائية بطرق متعددة"""
    try:
        # تقرير JSON مفصل
        save_to_json(vulnerabilities, "final_report.json")

        # تقرير PDF ملخص
        generate_pdf_report(vulnerabilities, "summary_report.pdf")

        # تقرير كامل مع لقطات الشاشة
        save_full_report(vulnerabilities, "complete_report.json")

        # حفظ لقطات الشاشة لجميع الثغرات
        for vuln in vulnerabilities:  # حفظ لقطة لكل ثغرة
            try:
                capture_vulnerability_image(
                    line=vuln['line_content'],
                    line_number=vuln['line_number'],
                    prediction=vuln['prediction'],
                    model_used=vuln['model_used'],
                    url=vuln.get('direct_link', ''),
                    filename=f"vuln_{vuln['line_number']}.png",
                    output_path=screenshot_dir,
                    severity=vuln.get('severity'),
                    description=vuln.get('description')
                )
            except Exception as e:
                logger.warning(f"Failed to save screenshot for vuln {vuln['line_number']}: {str(e)}")

        print(Fore.GREEN + "[+] Reports and screenshots saved successfully")
    except Exception as e:
        logger.error(f"Failed to save reports: {str(e)}")
        raise

def cleanup_resources():
    """تنظيف وإغلاق جميع الموارد"""
    try:
        # إغلاق جلسة الطلبات
        if 'session' in globals():
            session.close()

        # تنظيف الذاكرة المؤقتة
        if 'cache' in globals():
            cache.clear()

        # إغلاق النماذج
        if 'models_and_vectorizers' in globals():
            for model_name, model_data in models_and_vectorizers.items():
                if model_data and hasattr(model_data, 'close'):
                    model_data.close()

        # تنظيف الملفات المؤقتة
        temp_dirs = ['scan_states', 'reports', 'screenshots']
        for dir_name in temp_dirs:
            if os.path.exists(dir_name):
                try:
                    for file in os.listdir(dir_name):
                        file_path = os.path.join(dir_name, file)
                        if os.path.isfile(file_path):
                            os.remove(file_path)
                except Exception as e:
                    logger.warning(f"Failed to clean {dir_name}: {str(e)}")

        # إغلاق ملفات التسجيل
        for handler in logging.getLogger().handlers:
            if isinstance(handler, logging.FileHandler):
                handler.close()

        # تنظيف الذاكرة
        import gc
        gc.collect()

        print(Fore.GREEN + "[+] Resources cleaned up successfully")

    except Exception as e:
        logger.error(f"Cleanup failed: {str(e)}")
        print(Fore.RED + f"[-] Cleanup error: {str(e)}")

def enhanced_interact():
    """واجهة مستخدم محسنة مع خيارات متقدمة"""
    try:
        while True:
            print("\n" + "="*50)
            print(Fore.CYAN + "Advanced Vulnerability Scanner")
            print("="*50)
            print("1. New Comprehensive Scan")
            print("2. Resume Previous Scan")
            print("3. Quick Scan (Basic Checks Only)")
            print("4. Scan Settings")
            print("5. Exit")
            print("="*50)

            choice = input("\nEnter your choice (1-5): ").strip()

            if choice == "1":
                url = input("Enter target URL: ").strip()
                if not url:
                    print(Fore.RED + "[-] URL cannot be empty")
                    continue

                # إنشاء مجلدات الإخراج والتحقق من نجاح العملية
                if not create_output_folders():
                    print(Fore.RED + "[-] Cannot proceed with scan due to folder creation issues")
                    continue

                print(Fore.YELLOW + "\n[*] Starting comprehensive scan...")
                print("="*50)
                print(Fore.YELLOW + "[*] Initializing environment...")

                try:
                    enhanced_scan(url)
                except Exception as e:
                    logger.error(f"Scan error: {str(e)}")
                    print(Fore.RED + f"[-] Scan error: {str(e)}")

            elif choice == "2":
                if not create_output_folders():
                    print(Fore.RED + "[-] Cannot resume scan due to folder creation issues")
                    continue

                print(Fore.YELLOW + "\n[*] Attempting to resume previous scan...")
                try:
                    enhanced_scan(None, resume=True)
                except Exception as e:
                    logger.error(f"Resume scan error: {str(e)}")
                    print(Fore.RED + f"[-] Resume scan error: {str(e)}")

            elif choice == "3":
                url = input("Enter target URL: ").strip()
                if not url:
                    print(Fore.RED + "[-] URL cannot be empty")
                    continue

                if not create_output_folders():
                    print(Fore.RED + "[-] Cannot proceed with quick scan due to folder creation issues")
                    continue

                print(Fore.YELLOW + "\n[*] Starting quick scan...")
                try:
                    run_quick_scan(url)
                except Exception as e:
                    logger.error(f"Quick scan error: {str(e)}")
                    print(Fore.RED + f"[-] Quick scan error: {str(e)}")

            elif choice == "4":
                show_scan_settings()

            elif choice == "5":
                print(Fore.YELLOW + "\n[*] Exiting...")
                break

            else:
                print(Fore.RED + "[-] Invalid choice. Please enter 1-5")

    except KeyboardInterrupt:
        print(Fore.YELLOW + "\n[*] Operation cancelled by user")
    except Exception as e:
        logger.critical(f"UI interaction error: {str(e)}")
        print(Fore.RED + f"\n[-] Fatal UI error: {str(e)}")

def show_scan_settings():
    """عرض وإعدادات الفحص"""
    while True:
        try:
            print("\n" + "="*50)
            print(Fore.CYAN + "Scan Settings")
            print("="*50)
            print(f"1. Threads: {session.adapters['https://']._pool_connections}")
            print(f"2. Timeout: {session.adapters['https://'].timeout}s")
            print(f"3. Max Retries: {session.adapters['https://'].max_retries.total}")
            print("4. Back to Main Menu")
            print("="*50)

            choice = input("\nSelect setting to change (1-4): ").strip()

            if choice == "1":
                threads = input("Enter number of threads (1-100): ")
                try:
                    threads = int(threads)
                    if 1 <= threads <= 100:
                        session.adapters['https://']._pool_connections = threads
                        session.adapters['http://']._pool_connections = threads
                        print(Fore.GREEN + f"[+] Threads set to {threads}")
                    else:
                        print(Fore.RED + "[-] Please enter value between 1-100")
                except ValueError:
                    print(Fore.RED + "[-] Invalid number")

            elif choice == "2":
                timeout = input("Enter timeout in seconds (1-60): ")
                try:
                    timeout = int(timeout)
                    if 1 <= timeout <= 60:
                        session.adapters['https://'].timeout = timeout
                        session.adapters['http://'].timeout = timeout
                        print(Fore.GREEN + f"[+] Timeout set to {timeout}s")
                    else:
                        print(Fore.RED + "[-] Please enter value between 1-60")
                except ValueError:
                    print(Fore.RED + "[-] Invalid number")

            elif choice == "3":
                retries = input("Enter max retries (0-5): ")
                try:
                    retries = int(retries)
                    if 0 <= retries <= 5:
                        session.adapters['https://'].max_retries.total = retries
                        session.adapters['http://'].max_retries.total = retries
                        print(Fore.GREEN + f"[+] Max retries set to {retries}")
                    else:
                        print(Fore.RED + "[-] Please enter value between 0-5")
                except ValueError:
                    print(Fore.RED + "[-] Invalid number")

            elif choice == "4":
                break

            else:
                print(Fore.RED + "[-] Invalid choice")

        except Exception as e:
            logger.error(f"Error in scan settings: {str(e)}")
            print(Fore.RED + f"[-] An error occurred: {str(e)}")
            continue

def run_quick_scan(url):
    """إصدار سريع من الفحص للفحوصات الأساسية فقط"""
    try:
        print(Fore.YELLOW + "[*] Running quick vulnerability scan...")

        # إعدادات الفحص السريع
        quick_headers = HEADERS.copy()
        quick_headers['X-Scan-Mode'] = 'Quick'

        response = session.get(url, headers=quick_headers, timeout=15)
        if response.status_code != 200:
            print(Fore.RED + f"[-] Target returned status {response.status_code}")
            return

        # الفحوصات الأساسية فقط
        basic_vulns = find_vulnerabilities(response.text, url)

        if basic_vulns:
            print(Fore.GREEN + f"\n[+] Found {len(basic_vulns)} potential vulnerabilities")
            save_to_json(basic_vulns, "quick_scan_results.json")
        else:
            print(Fore.GREEN + "\n[+] No obvious vulnerabilities found")

    except requests.exceptions.RequestException as e:
        logger.error(f"Network error during quick scan: {str(e)}")
        print(Fore.RED + f"[-] Network error: {str(e)}")
    except Exception as e:
        logger.error(f"Quick scan failed: {str(e)}")
        print(Fore.RED + f"[-] Quick scan error: {str(e)}")

def save_to_json(vulnerabilities, filename):
    """حفظ الثغرات في ملف JSON داخل مجلد التقارير"""
    try:
        os.makedirs("reports", exist_ok=True)
        file_path = os.path.join("reports", filename)
        with open(file_path, 'w', encoding='utf-8') as file:
            json.dump(vulnerabilities, file, indent=4, ensure_ascii=False, default=str)
        print(Fore.GREEN + f"[+] Vulnerabilities saved to {file_path}")
    except Exception as e:
        logger.error(f"Error saving vulnerabilities: {str(e)}")
        print(Fore.RED + f"[-] Error saving vulnerabilities: {str(e)}")

def generate_pdf_report(vulnerabilities, filename):
    """توليد تقرير PDF للثغرات"""
    try:
        os.makedirs("reports", exist_ok=True)
        file_path = os.path.join("reports", filename)
        doc = SimpleDocTemplate(file_path, pagesize=letter)
        styles = getSampleStyleSheet()
        story = []
        title = Paragraph("Vulnerability Report", styles['Title'])
        story.append(title)
        story.append(Spacer(1, 0.25 * inch))
        for i, vuln in enumerate(vulnerabilities, 1):
            vuln_title = Paragraph(f"Vulnerability #{i}: {vuln.get('prediction', vuln.get('type', 'Unknown'))} (Severity: {vuln.get('severity', 'Unknown')})", styles['Heading2'])
            story.append(vuln_title)
            details = [
                f"Line {vuln.get('line_number', '?')}: {vuln.get('line_content', '')}",
                f"Model Used: {vuln.get('model_used', 'Unknown')}",
                f"Description: {vuln.get('description', 'No description available')}",
                f"Exploit Method: {vuln.get('exploit_method', 'Unknown')}",
                f"Payload: {vuln.get('payload', 'None')}",
                f"Direct Link: {vuln.get('direct_link', 'None')}"
            ]
            for detail in details:
                story.append(Paragraph(detail, styles['Normal']))
                story.append(Spacer(1, 0.1 * inch))
            screenshot_path = os.path.join("screenshots", f"vuln_{vuln.get('line_number', hash(vuln.get('direct_link', '')))}.png")
            if os.path.exists(screenshot_path):
                img = Image(screenshot_path, width=6 * inch, height=4.5 * inch)
                story.append(img)
                story.append(Spacer(1, 0.25 * inch))
            story.append(Spacer(1, 0.5 * inch))
        doc.build(story)
        print(Fore.GREEN + f"[+] PDF report saved as {file_path}")
    except Exception as e:
        logger.error(f"Error generating PDF report: {str(e)}")
        print(Fore.RED + f"[-] Error generating PDF report: {str(e)}")

def save_full_report(vulnerabilities, filename="full_report.json"):
    """حفظ تقرير كامل للثغرات في ملف JSON داخل مجلد التقارير"""
    try:
        os.makedirs("reports", exist_ok=True)
        file_path = os.path.join("reports", filename)
        with open(file_path, 'w', encoding='utf-8') as file:
            json.dump(vulnerabilities, file, indent=4, ensure_ascii=False, default=str)
        print(Fore.GREEN + f"[+] Full report saved to {file_path}")
    except Exception as e:
        logger.error(f"Error saving full report: {str(e)}")
        print(Fore.RED + f"[-] Error saving full report: {str(e)}")

def capture_vulnerability_image(line, line_number, prediction, model_used, url, filename, output_path, exploit_result=None, request_details=None, response_details=None, severity=None, description=None, payload=None, exploit_method=None):
    """توليد صورة توضيحية للثغرة وحفظها في مجلد لقطات الشاشة"""
    try:
        os.makedirs(output_path, exist_ok=True)
        image = PILImage.new('RGB', (1200, 600), color=(255, 255, 255))
        draw = ImageDraw.Draw(image)
        try:
            font = ImageFont.truetype("arial.ttf", 20)
        except:
            font = ImageFont.load_default()
        y = 10
        draw.text((10, y), f"Vulnerability Line: {line_number}", fill="red", font=font)
        y += 30
        draw.text((10, y), f"Prediction: {prediction}", fill="black", font=font)
        y += 30
        draw.text((10, y), f"Model Used: {model_used}", fill="black", font=font)
        y += 30
        draw.text((10, y), f"Severity: {severity}", fill="black", font=font)
        y += 30
        draw.text((10, y), f"Description: {description}", fill="black", font=font)
        y += 30
        draw.text((10, y), f"Direct Link: {url}#line-{line_number}", fill="blue", font=font)
        y += 30
        if payload:
            draw.text((10, y), f"Payload: {payload}", fill="black", font=font)
            y += 30
        if exploit_method:
            draw.text((10, y), f"Exploit Method: {exploit_method}", fill="black", font=font)
            y += 30
        if exploit_result:
            draw.text((10, y), f"Exploit Result: {exploit_result}", fill="black", font=font)
            y += 30
        if request_details:
            draw.text((10, y), f"Request Details: {str(request_details)}", fill="black", font=font)
            y += 30
        if response_details:
            draw.text((10, y), f"Response Details: {str(response_details)}", fill="black", font=font)
            y += 30
        draw.text((10, y), f"Code Snippet: {line[:200]}", fill="gray", font=font)
        image_path = os.path.join(output_path, filename)
        image.save(image_path)
        print(Fore.GREEN + f"[+] Vulnerability image saved at: {image_path}")
        return image_path
    except Exception as e:
        logger.error(f"Error generating image: {str(e)}")
        print(Fore.RED + f"[-] Error generating image: {str(e)}")
        return None

def capture_screenshot(url, output_path, filename):
    """التقاط لقطة شاشة عادية للصفحة باستخدام Selenium"""
    try:
        os.makedirs(output_path, exist_ok=True)
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        options.add_argument('--ignore-certificate-errors')

        driver = webdriver.Chrome(
            service=Service(ChromeDriverManager().install()),
            options=options
        )

        driver.get(url)
        time.sleep(2)  # انتظار تحميل الصفحة

        # التقاط الصورة الكاملة للصفحة
        total_height = driver.execute_script("return document.body.scrollHeight")
        driver.set_window_size(1920, total_height)

        screenshot_path = os.path.join(output_path, filename)
        driver.save_screenshot(screenshot_path)

        # تحسين الصورة
        try:
            img = Image.open(screenshot_path)
            enhancer = ImageEnhance.Contrast(img)
            img = enhancer.enhance(1.5)
            enhancer = ImageEnhance.Sharpness(img)
            img = enhancer.enhance(2.0)
            img.save(screenshot_path, quality=95)
        except Exception as e:
            logger.warning(f"Image enhancement failed: {str(e)}")

        driver.quit()
        print(Fore.GREEN + f"[+] Screenshot saved at: {screenshot_path}")
        return screenshot_path
    except Exception as e:
        logger.error(f"Error capturing screenshot: {str(e)}")
        print(Fore.RED + f"[-] Error capturing screenshot: {str(e)}")
        return None

def capture_screenshot_enhanced(url, output_path, filename, actions=None):
    """التقاط لقطة شاشة مع تنفيذ تفاعلات (inputs/clicks) قبل الالتقاط"""
    try:
        os.makedirs(output_path, exist_ok=True)
        options = Options()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--window-size=1920,1080')
        options.add_argument('--ignore-certificate-errors')

        driver = webdriver.Chrome(
            service=Service(ChromeDriverManager().install()),
            options=options
        )

        driver.get(url)
        time.sleep(3)  # انتظار تحميل الصفحة

        if actions:
            for action in actions:
                try:
                    if action['type'] == 'click':
                        element = WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((By.XPATH, action['xpath']))
                        )
                        element.click()
                        time.sleep(2)
                    elif action['type'] == 'input':
                        element = WebDriverWait(driver, 10).until(
                            EC.presence_of_element_located((By.XPATH, action['xpath']))
                        )
                        element.clear()
                        element.send_keys(action['text'])
                        time.sleep(1)
                except Exception as e:
                    logger.warning(f"Failed to perform action {action}: {str(e)}")

        # التقاط الصورة الكاملة للصفحة
        total_height = driver.execute_script("return document.body.scrollHeight")
        driver.set_window_size(1920, total_height)

        screenshot_path = os.path.join(output_path, filename)
        driver.save_screenshot(screenshot_path)

        # تحسين الصورة
        try:
            img = Image.open(screenshot_path)
            enhancer = ImageEnhance.Contrast(img)
            img = enhancer.enhance(1.5)
            enhancer = ImageEnhance.Sharpness(img)
            img = enhancer.enhance(2.0)
            img.save(screenshot_path, quality=95)
        except Exception as e:
            logger.warning(f"Image enhancement failed: {str(e)}")

        driver.quit()
        print(Fore.GREEN + f"[+] Enhanced screenshot saved at: {screenshot_path}")
        return screenshot_path
    except Exception as e:
        logger.error(f"Error capturing enhanced screenshot: {str(e)}")
        print(Fore.RED + f"[-] Error capturing enhanced screenshot: {str(e)}")
        return None

def detect_zero_day_vulnerabilities(url, content):
    """كشف ثغرات Zero-Day باستخدام ClamAV + Isolation Forest + OneClassSVM"""
    print(Fore.YELLOW + f"[*] Checking for Zero-Day vulnerabilities in {url}...")
    vulnerabilities = []
    try:
        if clamav_available:
            cd = pyclamd.ClamdAgnostic()
            try:
                cd.ping()
            except Exception:
                cd = pyclamd.ClamdUnixSocket()
                cd.ping()
            scan_result = cd.scan_stream(content.encode('utf-8', errors='ignore'))
            if scan_result:
                for key, val in scan_result.items():
                    vulnerabilities.append({
                        'type': 'Zero-Day (ClamAV)',
                        'severity': 'Critical',
                        'description': f'ClamAV detected: {val[1]}',
                        'url': url
                    })
        else:
            logger.warning('ClamAV (pyclamd) is not available. Please install pyclamd and ensure clamd is running.')
    except Exception as e:
        logger.warning(f"ClamAV error: {str(e)}")
    try:
        vectorizer = TfidfVectorizer()
        X = vectorizer.fit_transform([content])
        clf = IsolationForest(contamination=0.01)
        preds = clf.fit_predict(X.toarray())
        if preds[0] == -1:
            vulnerabilities.append({
                'type': 'Zero-Day (Anomaly)',
                'severity': 'High',
                'description': 'Anomalous page content detected (possible zero-day)',
                'url': url
            })
    except Exception as e:
        logger.warning(f"IsolationForest error: {str(e)}")
    try:
        svm = OneClassSVM(gamma='auto').fit(X.toarray())
        if svm.predict(X.toarray())[0] == -1:
            vulnerabilities.append({
                'type': 'Zero-Day (SVM)',
                'severity': 'High',
                'description': 'SVM detected anomaly (possible zero-day)',
                'url': url
            })
    except Exception as e:
        logger.warning(f"SVM error: {str(e)}")
    return vulnerabilities

def detect_human_errors(url, content):
    """كشف الأخطاء البشرية في التطبيق (تسريب مفاتيح، كلمات مرور، تعليقات حساسة، أسرار)"""
    print(Fore.YELLOW + f"[*] Checking for Human Errors in {url}...")
    vulnerabilities = []
    patterns = [
        (r'password\\s*=\\s*["\'].*?["\']', 'Hardcoded password'),
        (r'api[_-]?key\\s*=\\s*["\'].*?["\']', 'Hardcoded API key'),
        (r'//\\s*TODO', 'TODO comment found'),
        (r'//\\s*FIXME', 'FIXME comment found'),
        (r'<!--.*?secret.*?-->', 'Sensitive HTML comment'),
        (r'secret\\s*=\\s*["\'].*?["\']', 'Hardcoded secret'),
        (r'PRIVATE KEY-----', 'Private key found'),
        (r'AWS_ACCESS_KEY_ID\\s*=\\s*["\'].*?["\']', 'AWS Key found'),
    ]
    for pattern, desc in patterns:
        for match in re.findall(pattern, content, re.IGNORECASE):
            vulnerabilities.append({
                'type': 'Human Error',
                'severity': 'Medium',
                'description': f'{desc}: {match}',
                'url': url
            })
    return vulnerabilities

def detect_business_logic_vulnerabilities_enhanced(vulnerabilities, url):
    """كشف ثغرات منطق الأعمال المتقدمة (تحليل تدفق العمليات وربط النتائج)"""
    print(Fore.YELLOW + f"[*] Checking for Business Logic vulnerabilities in {url}...")
    found = []
    for vuln in vulnerabilities:
        if 'price' in vuln.get('description', '').lower() or 'workflow' in vuln.get('description', '').lower():
            found.append({
                'type': 'Business Logic',
                'severity': 'Critical',
                'description': f"Business logic flaw detected: {vuln['description']}",
                'url': url
            })
    try:
        G = nx.DiGraph()
        for v in vulnerabilities:
            G.add_node(v['type'])
        for v1 in vulnerabilities:
            for v2 in vulnerabilities:
                if v1 != v2 and v1['url'] == v2['url']:
                    G.add_edge(v1['type'], v2['type'])
        if nx.number_of_edges(G) > 0:
            found.append({
                'type': 'Business Logic (Graph)',
                'severity': 'High',
                'description': 'Potential business logic chain detected',
                'url': url
            })
    except Exception as e:
        logger.warning(f"Business logic graph error: {str(e)}")
    return found

def test_registration_panels(url):
    """فحص لوحات التسجيل (محاولة تسجيل مستخدم وهمي وتحليل الرد)"""
    print(Fore.YELLOW + f"[*] Testing registration panels in {url}...")
    vulnerabilities = []
    try:
        options = webdriver.ChromeOptions()
        options.add_argument('--headless')
        driver = webdriver.Chrome(options=options)
        driver.get(url)
        forms = driver.find_elements(By.TAG_NAME, 'form')
        for form in forms:
            if any('register' in (form.get_attribute('action') or '').lower() for _ in [form]):
                try:
                    username = form.find_element(By.NAME, 'username')
                    email = form.find_element(By.NAME, 'email')
                    password = form.find_element(By.NAME, 'password')
                    username.send_keys('testuser123')
                    email.send_keys('<EMAIL>')
                    password.send_keys('TestPassw0rd!')
                    form.submit()
                    time.sleep(2)
                    if "already exists" in driver.page_source or "error" in driver.page_source:
                        vulnerabilities.append({
                            'type': 'Registration Panel',
                            'severity': 'Low',
                            'description': 'Registration error message detected',
                            'url': url
                        })
                    else:
                        vulnerabilities.append({
                            'type': 'Registration Panel',
                            'severity': 'Info',
                            'description': 'Registration form appears functional',
                            'url': url
                        })
                except Exception as e:
                    continue
        driver.quit()
    except Exception as e:
        print(Fore.RED + f"[-] Error testing registration panels: {str(e)}")
    return vulnerabilities

def init_clamav():
    """Initialize ClamAV scanner"""
    if clamav_available:
        try:
            cd = pyclamd.ClamdUnixSocket()
            return cd
        except:
            try:
                cd = pyclamd.ClamdNetworkSocket()
                return cd
            except:
                print("Warning: Could not connect to ClamAV daemon")
                return None
    return None

def scan_content_with_clamav(content, scanner):
    """Scan content using ClamAV"""
    if scanner is None:
        return False, "ClamAV not available"

    try:
        # Convert content to bytes if it's a string
        if isinstance(content, str):
            content = content.encode('utf-8')

        result = scanner.scan_stream(content)
        if result:
            return True, result
        return False, "No malware detected"
    except Exception as e:
        return False, f"Scan error: {str(e)}"

def detect_malware(content, url):
    """Enhanced malware detection using ClamAV"""
    scanner = init_clamav()
    is_malicious, details = scan_content_with_clamav(content, scanner)

    if is_malicious:
        return {
            'type': 'MALWARE',
            'url': url,
            'severity': 'HIGH',
            'description': f'Malware detected: {details}',
            'confidence': 0.95,
            'details': {
                'scanner': 'ClamAV',
                'detection_details': details
            }
        }
    return None

class ClamAVScanner:
    def __init__(self):
        self.scanner = self._init_scanner()
        self.signatures = self._load_signatures()

    def _init_scanner(self):
        """Initialize ClamAV scanner with multiple connection attempts"""
        if not clamav_available:
            return None

        try:
            return pyclamd.ClamdUnixSocket()
        except:
            try:
                return pyclamd.ClamdNetworkSocket()
            except:
                print("Warning: Could not connect to ClamAV daemon")
                return None

    def _load_signatures(self):
        """Load custom signatures for enhanced detection"""
        return {
            'php_shell': r'<\?php\s*@eval\s*\(.*\)',
            'javascript_injection': r'document\.(write|execCommand|createElement)',
            'sql_injection': r'(SELECT|INSERT|UPDATE|DELETE).*FROM',
            'xss_pattern': r'<script.*?>.*?</script>',
            'command_injection': r'(\|\||\&\&|\;).*(rm|wget|curl|nc)'
        }

    def scan_content(self, content, url):
        """Enhanced content scanning with both ClamAV and pattern matching"""
        results = []

        # ClamAV scanning
        if self.scanner:
            try:
                if isinstance(content, str):
                    content_bytes = content.encode('utf-8')
                else:
                    content_bytes = content

                clam_result = self.scanner.scan_stream(content_bytes)
                if clam_result:
                    results.append({
                        'type': 'MALWARE',
                        'url': url,
                        'severity': 'HIGH',
                        'description': f'ClamAV detected: {clam_result}',
                        'confidence': 0.95,
                        'details': {
                            'scanner': 'ClamAV',
                            'detection_details': clam_result
                        }
                    })
            except Exception as e:
                print(f"ClamAV scan error: {str(e)}")

        # Pattern matching
        for pattern_name, pattern in self.signatures.items():
            if re.search(pattern, content, re.IGNORECASE | re.MULTILINE):
                results.append({
                    'type': 'SUSPICIOUS_PATTERN',
                    'url': url,
                    'severity': 'MEDIUM',
                    'description': f'Detected {pattern_name} pattern',
                    'confidence': 0.85,
                    'details': {
                        'pattern': pattern_name,
                        'matched_content': re.search(pattern, content, re.IGNORECASE | re.MULTILINE).group(0)
                    }
                })

        return results

def find_vulnerabilities(content, url):
    """Find vulnerabilities in content using enhanced detection methods"""
    scanner = ClamAVScanner()
    vulnerabilities = scanner.scan_content(content, url)

    # Add other vulnerability checks here...
    # ... existing vulnerability checks ...

    return vulnerabilities

class EnhancedScanner:
    def __init__(self):
        self.clamav_scanner = self._init_clamav()
        self.signatures = self._load_signatures()
        self.patterns = self._load_patterns()

    def _init_clamav(self):
        """Initialize ClamAV scanner with multiple connection attempts"""
        if not clamav_available:
            return None

        try:
            return pyclamd.ClamdUnixSocket()
        except:
            try:
                return pyclamd.ClamdNetworkSocket()
            except:
                print("Warning: Could not connect to ClamAV daemon")
                return None

    def _load_signatures(self):
        """Load custom signatures for enhanced detection"""
        return {
            'php_shell': r'<\?php\s*@eval\s*\(.*\)',
            'javascript_injection': r'document\.(write|execCommand|createElement)',
            'sql_injection': r'(SELECT|INSERT|UPDATE|DELETE).*FROM',
            'xss_pattern': r'<script.*?>.*?</script>',
            'command_injection': r'(\|\||\&\&|\;).*(rm|wget|curl|nc)',
            'webshell': r'(eval|system|exec|shell_exec|passthru)\(.*\)',
            'file_upload': r'enctype=["\']multipart/form-data["\']',
            'sensitive_data': r'(password|credit\s*card|ssn|social\s*security)',
            'malicious_redirect': r'window\.location|header\s*\(["\']Location:',
            'iframe_injection': r'<iframe.*?src=.*?>'
        }

    def _load_patterns(self):
        """Load additional security patterns"""
        return {
            'sensitive_files': [
                r'\.(env|config|htaccess|htpasswd)$',
                r'(wp-config|config\.php|settings\.php)$',
                r'\.(sql|db|mdb|bak|backup)$'
            ],
            'suspicious_paths': [
                r'/admin/',
                r'/backup/',
                r'/temp/',
                r'/tmp/',
                r'/logs/'
            ],
            'malicious_headers': [
                r'X-Forwarded-For:.*',
                r'X-Real-IP:.*',
                r'User-Agent:.*(curl|wget|python)'
            ]
        }

    def scan_content(self, content, url):
        """Enhanced content scanning with multiple detection methods"""
        results = []

        # ClamAV scanning
        if self.clamav_scanner:
            try:
                if isinstance(content, str):
                    content_bytes = content.encode('utf-8')
                else:
                    content_bytes = content

                clam_result = self.clamav_scanner.scan_stream(content_bytes)
                if clam_result:
                    results.append({
                        'type': 'MALWARE',
                        'url': url,
                        'severity': 'HIGH',
                        'description': f'ClamAV detected: {clam_result}',
                        'confidence': 0.95,
                        'details': {
                            'scanner': 'ClamAV',
                            'detection_details': clam_result
                        }
                    })
            except Exception as e:
                print(f"ClamAV scan error: {str(e)}")

        # Signature matching
        for sig_name, pattern in self.signatures.items():
            if re.search(pattern, content, re.IGNORECASE | re.MULTILINE):
                results.append({
                    'type': 'SUSPICIOUS_PATTERN',
                    'url': url,
                    'severity': 'MEDIUM',
                    'description': f'Detected {sig_name} pattern',
                    'confidence': 0.85,
                    'details': {
                        'pattern': sig_name,
                        'matched_content': re.search(pattern, content, re.IGNORECASE | re.MULTILINE).group(0)
                    }
                })

        # Additional pattern checks
        for pattern_type, patterns in self.patterns.items():
            for pattern in patterns:
                if re.search(pattern, content, re.IGNORECASE | re.MULTILINE):
                    results.append({
                        'type': 'SECURITY_ISSUE',
                        'url': url,
                        'severity': 'LOW',
                        'description': f'Detected {pattern_type}',
                        'confidence': 0.75,
                        'details': {
                            'pattern_type': pattern_type,
                            'matched_content': re.search(pattern, content, re.IGNORECASE | re.MULTILINE).group(0)
                        }
                    })

        return results

def find_vulnerabilities(content, url):
    """Find vulnerabilities in content using enhanced detection methods"""
    scanner = EnhancedScanner()
    vulnerabilities = scanner.scan_content(content, url)

    # Add other vulnerability checks here...
    # ... existing vulnerability checks ...

    return vulnerabilities

class ScreenshotManager:
    """Manages parallel screenshot capture with resource control"""
    def __init__(self):
        self.semaphore = asyncio.Semaphore(CONFIG['MAX_CONCURRENT_SCREENSHOTS'])
        self.driver_pool = []
        self.lock = asyncio.Lock()

    async def get_driver(self):
        """Get a webdriver instance from the pool or create a new one"""
        async with self.lock:
            if self.driver_pool:
                return self.driver_pool.pop()

            options = webdriver.ChromeOptions()
            options.add_argument('--headless')
            options.add_argument('--disable-gpu')
            options.add_argument('--no-sandbox')
            options.add_argument('--disable-dev-shm-usage')
            options.add_argument('--window-size=1920,1080')

            driver = webdriver.Chrome(
                service=Service(ChromeDriverManager().install()),
                options=options
            )
            return driver

    async def return_driver(self, driver):
        """Return a driver to the pool"""
        async with self.lock:
            self.driver_pool.append(driver)

    async def capture_screenshot(self, url: str, output_path: str, filename: str) -> bool:
        """Capture screenshot with error handling and resource management"""
        async with self.semaphore:
            driver = None
            try:
                driver = await self.get_driver()
                driver.set_page_load_timeout(CONFIG['TIMEOUT'])
                driver.get(safe_url(url))

                # Wait for page to load
                WebDriverWait(driver, CONFIG['TIMEOUT']).until(
                    lambda d: d.execute_script('return document.readyState') == 'complete'
                )

                # Take screenshot
                screenshot_path = os.path.join(output_path, filename)
                driver.save_screenshot(screenshot_path)
                return True
            except Exception as e:
                logging.error(f"Error capturing screenshot for {url}: {str(e)}")
                return False
            finally:
                if driver:
                    await self.return_driver(driver)

    async def cleanup(self):
        """Clean up all drivers"""
        async with self.lock:
            for driver in self.driver_pool:
                try:
                    driver.quit()
                except:
                    pass
            self.driver_pool.clear()

class ParallelProcessor:
    """Manages parallel processing of tasks with resource control"""
    def __init__(self, max_workers=None):
        self.max_workers = max_workers or CONFIG['MAX_CONCURRENT_REQUESTS']
        self.executor = ThreadPoolExecutor(max_workers=self.max_workers)
        self.session = create_session()

    async def process_batch(self, tasks: List[Any], batch_size: int = None) -> List[Any]:
        """Process a batch of tasks in parallel with controlled concurrency"""
        if batch_size is None:
            batch_size = self.max_workers

        results = []
        for i in range(0, len(tasks), batch_size):
            batch = tasks[i:i + batch_size]
            futures = [self.executor.submit(self._process_task, task) for task in batch]

            for future in as_completed(futures):
                try:
                    result = future.result()
                    results.append(result)
                except Exception as e:
                    logging.error(f"Error processing task: {str(e)}")
                    results.append(None)

            # Check memory usage and cleanup if needed
            if check_memory_limit():
                cleanup_memory()

        return results

    def _process_task(self, task: Any) -> Any:
        """Process a single task with error handling"""
        try:
            # Implement task processing logic here
            return task
        except Exception as e:
            logging.error(f"Error processing task: {str(e)}")
            return None

    def cleanup(self):
        """Clean up resources"""
        self.executor.shutdown(wait=True)
        self.session.close()

@backoff.on_exception(backoff.expo, Exception, max_tries=CONFIG['MAX_RETRIES'])
async def process_screenshots_parallel(links: List[str], output_path: str) -> Dict[str, bool]:
    """Process screenshots in parallel with improved error handling"""
    screenshot_manager = ScreenshotManager()
    results = {}

    try:
        tasks = []
        for link in links:
            filename = f"screenshot_{hash(link)}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
            tasks.append(screenshot_manager.capture_screenshot(link, output_path, filename))

        # Process screenshots in parallel
        screenshot_results = await asyncio.gather(*tasks, return_exceptions=True)

        # Process results
        for link, result in zip(links, screenshot_results):
            if isinstance(result, Exception):
                logging.error(f"Error processing screenshot for {link}: {str(result)}")
                results[link] = False
            else:
                results[link] = result

    except Exception as e:
        logging.error(f"Error in parallel screenshot processing: {str(e)}")
    finally:
        await screenshot_manager.cleanup()

    return results

async def crawl_internal_links(url: str, max_depth: int = 8, max_pages: int = 5000) -> List[str]:
    """Crawl internal links with improved performance and stability"""
    visited = set()
    to_visit = [(url, 0)]
    processor = ParallelProcessor()
    session = create_session()

    try:
        while to_visit and len(visited) < max_pages:
            current_batch = []
            current_depth = to_visit[0][1]

            # Collect batch of URLs at current depth
            while to_visit and to_visit[0][1] == current_depth and len(current_batch) < CONFIG['MAX_CONCURRENT_REQUESTS']:
                current_url, depth = to_visit.pop(0)
                if current_url not in visited and depth <= max_depth:
                    current_batch.append(current_url)

            if not current_batch:
                break

            # Process batch in parallel
            batch_results = await processor.process_batch(current_batch)

            for url, result in zip(current_batch, batch_results):
                if result:
                    visited.add(url)
                    # Extract and add new links
                    new_links = extract_internal_links(result, url)
                    for new_link in new_links:
                        if new_link not in visited:
                            to_visit.append((new_link, current_depth + 1))

            # Check memory usage
            if check_memory_limit():
                cleanup_memory()

    except Exception as e:
        logging.error(f"Error during crawling: {str(e)}")
    finally:
        processor.cleanup()
        session.close()

    return list(visited)

class EnhancedVulnerabilityScanner:
    """Enhanced vulnerability scanner with improved performance and stability"""
    def __init__(self, target_url: str):
        self.target_url = safe_url(target_url)
        self.session = create_session()
        self.processor = ParallelProcessor()
        self.screenshot_manager = ScreenshotManager()
        self.vulnerabilities = []
        self.scan_start_time = None
        self.scan_end_time = None
        self.reporter = VulnerabilityReporter()
        self.scan_completed = False

    async def run_full_scan(self) -> List[Dict[str, Any]]:
        """Run a full vulnerability scan with improved performance"""
        self.scan_start_time = datetime.now()

        try:
            # Initialize scan environment
            await self._init_scan_environment()

            # Run parallel scanning tasks
            tasks = [
                self._scan_trained_models(),
                self._scan_zero_day_vulns(),
                self._scan_business_logic_vulns(),
                self._scan_human_errors(),
                self._scan_registration_panels(),
                self._run_advanced_scanning()
            ]

            # Execute tasks in parallel
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Process results
            for result in results:
                if isinstance(result, Exception):
                    logging.error(f"Error in scan task: {str(result)}")
                elif result:
                    self.vulnerabilities.extend(result)

            # Process human review
            await self._process_human_review()

            # Generate reports
            await self._generate_reports()

            # Capture screenshots
            await self._capture_all_screenshots()

            self.scan_completed = True
            return self.vulnerabilities

        except Exception as e:
            logging.error(f"Error during full scan: {str(e)}")
            return []
        finally:
            self.scan_end_time = datetime.now()
            await self.cleanup()

    async def _capture_all_screenshots(self):
        """Capture screenshots for all vulnerabilities"""
        try:
            screenshot_tasks = []
            for vuln in self.vulnerabilities:
                if 'url' in vuln:
                    filename = f"screenshot_{hash(vuln['url'])}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png"
                    screenshot_tasks.append(
                        self.screenshot_manager.capture_screenshot(
                            vuln['url'],
                            'screenshots',
                            filename
                        )
                    )

            # Execute screenshot tasks in parallel
            await asyncio.gather(*screenshot_tasks, return_exceptions=True)

        except Exception as e:
            logging.error(f"Error capturing screenshots: {str(e)}")

    async def _process_human_review(self):
        """Process human review and categorize vulnerabilities"""
        try:
            # Get human review results
            confirmed, false_positives = await analyze_review_results(
                self.vulnerabilities, []
            )

            # Ensure all vulnerabilities have predictions
            for vuln in confirmed:
                if 'prediction' not in vuln:
                    vuln['prediction'] = "Unknown"

            # Categorize confirmed vulnerabilities
            for vuln in confirmed:
                self.reporter.add_vulnerability(vuln)

            # Save categorized reports
            self.reporter.save_reports()

        except Exception as e:
            logging.error(f"Error in human review processing: {str(e)}")

    async def _scan_trained_models(self) -> List[Dict[str, Any]]:
        """Scan using trained models"""
        try:
            vulnerabilities = await find_vulnerabilities(self.target_url)
            for vuln in vulnerabilities:
                vuln['source'] = 'model_based'
                if 'prediction' not in vuln:
                    vuln['prediction'] = "Unknown"
            return vulnerabilities
        except Exception as e:
            logging.error(f"Error in model-based scanning: {str(e)}")
            return []

    async def _scan_zero_day_vulns(self) -> List[Dict[str, Any]]:
        """Scan for zero-day vulnerabilities"""
        try:
            vulnerabilities = await detect_zero_day_vulnerabilities(self.target_url)
            for vuln in vulnerabilities:
                vuln['source'] = 'zero_day'
                if 'prediction' not in vuln:
                    vuln['prediction'] = "Unknown"
            return vulnerabilities
        except Exception as e:
            logging.error(f"Error in zero-day scanning: {str(e)}")
            return []

    async def _scan_business_logic_vulns(self) -> List[Dict[str, Any]]:
        """Scan for business logic vulnerabilities"""
        try:
            vulnerabilities = await detect_business_logic_vulnerabilities_enhanced(
                self.vulnerabilities, self.target_url
            )
            for vuln in vulnerabilities:
                vuln['source'] = 'business_logic'
                if 'prediction' not in vuln:
                    vuln['prediction'] = "Unknown"
            return vulnerabilities
        except Exception as e:
            logging.error(f"Error in business logic scanning: {str(e)}")
            return []

    async def _scan_human_errors(self) -> List[Dict[str, Any]]:
        """Scan for human errors"""
        try:
            vulnerabilities = await detect_human_errors(self.target_url)
            for vuln in vulnerabilities:
                vuln['source'] = 'human_errors'
                if 'prediction' not in vuln:
                    vuln['prediction'] = "Unknown"
            return vulnerabilities
        except Exception as e:
            logging.error(f"Error in human error scanning: {str(e)}")
            return []

    async def _scan_registration_panels(self) -> List[Dict[str, Any]]:
        """Scan registration panels"""
        try:
            vulnerabilities = await test_registration_panels(self.target_url)
            for vuln in vulnerabilities:
                vuln['source'] = 'registration_panel'
                if 'prediction' not in vuln:
                    vuln['prediction'] = "Unknown"
            return vulnerabilities
        except Exception as e:
            logging.error(f"Error in registration panel scanning: {str(e)}")
            return []

    async def _generate_reports(self):
        """Generate comprehensive reports"""
        try:
            # Generate reports in parallel
            tasks = [
                self._generate_executive_summary(),
                self._generate_technical_report(),
                save_to_json(self.vulnerabilities, 'reports/full_report.json'),
                generate_pdf_report(self.vulnerabilities, 'reports/full_report.pdf')
            ]

            await asyncio.gather(*tasks, return_exceptions=True)

        except Exception as e:
            logging.error(f"Error generating reports: {str(e)}")

    async def cleanup(self):
        """Clean up all resources"""
        try:
            # Clean up in parallel
            tasks = [
                self.processor.cleanup(),
                self.screenshot_manager.cleanup(),
                cleanup_resources()
            ]

            await asyncio.gather(*tasks, return_exceptions=True)

        except Exception as e:
            logging.error(f"Error during cleanup: {str(e)}")

async def process_categorized_reports(vulnerabilities: List[Dict[str, Any]]):
    """Process and save categorized vulnerability reports"""
    try:
        reporter = VulnerabilityReporter()

        # Categorize vulnerabilities
        for vuln in vulnerabilities:
            reporter.add_vulnerability(vuln)

        # Save reports
        reporter.save_reports()

        # Print summary
        summary = reporter.get_report_summary()
        print("\nVulnerability Report Summary:")
        print("="*50)
        for category, count in summary.items():
            if count > 0:
                print(f"{category.replace('_', ' ').title()}: {count}")
        print("="*50)

    except Exception as e:
        logging.error(f"Error processing categorized reports: {str(e)}")

class VulnerabilityReporter:
    """Handles categorized vulnerability reporting"""
    def __init__(self):
        self.reports_dir = 'categorized_reports'
        self.reports = {
            'zero_day': [],
            'human_errors': [],
            'business_logic': [],
            'registration_panel': [],
            'model_based': [],
            'uncategorized': []
        }
        self._init_reports_dir()

    def _init_reports_dir(self):
        """Initialize reports directory"""
        os.makedirs(self.reports_dir, exist_ok=True)

    def _ensure_prediction(self, vuln: Dict[str, Any]) -> Dict[str, Any]:
        """Ensure vulnerability has a prediction field"""
        if 'prediction' not in vuln:
            vuln['prediction'] = "Unknown"
        return vuln

    def categorize_vulnerability(self, vuln: Dict[str, Any]) -> str:
        """Categorize vulnerability based on its source"""
        vuln = self._ensure_prediction(vuln)
        if 'source' in vuln:
            source = vuln['source'].lower()
            if 'zero_day' in source:
                return 'zero_day'
            elif 'human_error' in source:
                return 'human_errors'
            elif 'business_logic' in source:
                return 'business_logic'
            elif 'registration' in source:
                return 'registration_panel'
            elif 'model' in source:
                return 'model_based'
        return 'uncategorized'

    def add_vulnerability(self, vuln: Dict[str, Any]):
        """Add vulnerability to appropriate category"""
        category = self.categorize_vulnerability(vuln)
        self.reports[category].append(vuln)

    def save_reports(self):
        """Save all categorized reports"""
        for category, vulnerabilities in self.reports.items():
            if vulnerabilities:
                filename = f"{category}_report.json"
                filepath = os.path.join(self.reports_dir, filename)
                try:
                    with open(filepath, 'w', encoding='utf-8') as f:
                        json.dump(vulnerabilities, f, indent=4, ensure_ascii=False)
                except Exception as e:
                    logging.error(f"Error saving {category} report: {str(e)}")

    def get_report_summary(self) -> Dict[str, int]:
        """Get summary of vulnerabilities by category"""
        return {category: len(vulns) for category, vulns in self.reports.items()}

class SequentialVulnerabilityScanner:
    """ماسح الثغرات المتسلسل الذي يقوم بفحص كل رابط على حدة بشكل منظم"""
    def __init__(self, target_url: str):
        self.target_url = target_url
        self.vulnerabilities = {
            'model_based': [],
            'business_logic': [],
            'human_errors': [],
            'registration_panels': [],
            'zero_day': []
        }
        self.file_manager = FileManager()
        self.reporter = VulnerabilityReporter()

    async def scan_link(self, url: str) -> Dict[str, List[Dict[str, Any]]]:
        """فحص رابط واحد بشكل متسلسل"""
        print(Fore.CYAN + f"[*] Scanning URL: {url}")

        try:
            # 1. الفحص التقليدي (Model-based Detection)
            print(Fore.YELLOW + "[*] Running traditional vulnerability scan...")
            response = requests.get(url, headers=HEADERS, timeout=30)
            model_based_vulns = find_vulnerabilities(response.text, url)

            # تحليل النتائج وتقييم الإنباكت
            for vuln in model_based_vulns:
                vuln['impact'] = self._calculate_impact(vuln)
                vuln['source'] = 'model_based'
                self.vulnerabilities['model_based'].append(vuln)

            # حفظ التقرير التقليدي
            self.file_manager.save_report('model_based', self.vulnerabilities['model_based'])

            # التقاط صورة للرابط
            screenshot_data = await capture_screenshot_async(url)
            if screenshot_data:
                self.file_manager.save_screenshot('model_based', f"model_based_{hash(url)}.png", screenshot_data)

            # 2. الفحص المتقدم
            print(Fore.YELLOW + "[*] Running advanced vulnerability scans...")

            # فحص ثغرات منطق الأعمال
            business_logic_vulns = await detect_business_logic_vulnerabilities_enhanced(
                self.vulnerabilities['model_based'], url
            )
            for vuln in business_logic_vulns:
                vuln['source'] = 'business_logic'
                self.vulnerabilities['business_logic'].append(vuln)

            # حفظ تقرير ثغرات منطق الأعمال
            self.file_manager.save_report('business_logic', self.vulnerabilities['business_logic'])

            # التقاط صورة لفحص منطق الأعمال
            screenshot_data = await capture_screenshot_async(url)
            if screenshot_data:
                self.file_manager.save_screenshot('business_logic', f"business_logic_{hash(url)}.png", screenshot_data)

            # فحص الأخطاء البشرية
            human_error_vulns = await detect_human_errors(url)
            for vuln in human_error_vulns:
                vuln['source'] = 'human_errors'
                self.vulnerabilities['human_errors'].append(vuln)

            # حفظ تقرير الأخطاء البشرية
            self.file_manager.save_report('human_errors', self.vulnerabilities['human_errors'])

            # التقاط صورة لفحص الأخطاء البشرية
            screenshot_data = await capture_screenshot_async(url)
            if screenshot_data:
                self.file_manager.save_screenshot('human_errors', f"human_errors_{hash(url)}.png", screenshot_data)

            # فحص لوحات التسجيل
            if "register" in url.lower() or "signup" in url.lower():
                registration_vulns = await test_registration_panels(url)
                for vuln in registration_vulns:
                    vuln['source'] = 'registration_panels'
                    self.vulnerabilities['registration_panels'].append(vuln)

                # حفظ تقرير لوحات التسجيل
                self.file_manager.save_report('registration_panels', self.vulnerabilities['registration_panels'])

                # التقاط صورة لفحص لوحات التسجيل
                screenshot_data = await capture_screenshot_async(url)
                if screenshot_data:
                    self.file_manager.save_screenshot('registration', f"registration_{hash(url)}.png", screenshot_data)

            # فحص ثغرات الزيرو دي
            zero_day_vulns = await detect_zero_day_vulnerabilities(url)
            for vuln in zero_day_vulns:
                vuln['source'] = 'zero_day'
                self.vulnerabilities['zero_day'].append(vuln)

            # حفظ تقرير ثغرات الزيرو دي
            self.file_manager.save_report('zero_day', self.vulnerabilities['zero_day'])

            # التقاط صورة لفحص ثغرات الزيرو دي
            screenshot_data = await capture_screenshot_async(url)
            if screenshot_data:
                self.file_manager.save_screenshot('zero_day', f"zero_day_{hash(url)}.png", screenshot_data)

            # حفظ التقرير النهائي
            self.file_manager.save_report('final', self.vulnerabilities)

            return self.vulnerabilities

        except Exception as e:
            error_msg = f"Error scanning URL {url}: {str(e)}"
            logger.error(error_msg)
            self.file_manager.log_error(error_msg)
            return self.vulnerabilities

    def _calculate_impact(self, vuln: Dict[str, Any]) -> str:
        """حساب تأثير الثغرة"""
        severity = vuln.get('severity', 'Unknown').lower()
        if severity in ['critical', 'high']:
            return 'High'
        elif severity in ['medium']:
            return 'Medium'
        else:
            return 'Low'

    def _save_categorized_reports(self):
        """حفظ التقارير في ملفات منفصلة"""
        for category, vulns in self.vulnerabilities.items():
            if vulns:
                filename = f"categorized_reports/{category}_report.json"
                save_to_json(vulns, filename)

async def sequential_scan(target_url: str) -> Dict[str, List[Dict[str, Any]]]:
    """تشغيل الفحص المتسلسل لجميع الروابط"""
    try:
        # إنشاء مدير الملفات
        file_manager = FileManager()

        # إنشاء الماسح المتسلسل
        scanner = SequentialVulnerabilityScanner(target_url)

        # جمع الروابط الداخلية
        print(Fore.CYAN + "[*] Collecting internal links...")
        internal_links = await crawl_internal_links(target_url)

        # فحص كل رابط بشكل متسلسل
        all_vulnerabilities = {
            'model_based': [],
            'business_logic': [],
            'human_errors': [],
            'registration_panels': [],
            'zero_day': []
        }

        for link in internal_links:
            print(Fore.YELLOW + f"\n[*] Processing link: {link}")
            vulnerabilities = await scanner.scan_link(link)

            # تجميع النتائج
            for category, vulns in vulnerabilities.items():
                all_vulnerabilities[category].extend(vulns)

            # حفظ التقرير المؤقت
            file_manager.save_report('temp', all_vulnerabilities)

        # حفظ التقرير النهائي
        file_manager.save_report('final', all_vulnerabilities)

        return all_vulnerabilities

    except Exception as e:
        error_msg = f"Error in sequential scan: {str(e)}"
        logger.error(error_msg)
        file_manager.log_error(error_msg)
        return {}

def analyze_workflow(url: str) -> Dict[str, Any]:
    """تحليل تدفق العمل"""
    try:
        # تحليل مسار العمل الأساسي
        workflow = {
            'is_vulnerable': False,
            'steps': [],
            'vulnerabilities': []
        }

        # تحليل كل خطوة في تدفق العمل
        steps = get_workflow_steps(url)
        for step in steps:
            workflow['steps'].append(step)

            # التحقق من إمكانية تخطي الخطوة
            if can_bypass_step(step):
                workflow['is_vulnerable'] = True
                workflow['vulnerabilities'].append({
                    'type': 'Workflow Bypass',
                    'step': step,
                    'severity': 'High'
                })

        return workflow

    except Exception as e:
        logger.error(f"Error analyzing workflow: {str(e)}")
        return {'is_vulnerable': False, 'steps': [], 'vulnerabilities': []}

def test_parameter_tampering(url: str) -> List[Dict[str, Any]]:
    """اختبار تغيير المعلمات"""
    vulns = []
    try:
        # اختبار تغيير معلمات GET
        get_params = extract_get_parameters(url)
        for param in get_params:
            if is_parameter_tamperable(param):
                vulns.append({
                    'type': 'Parameter Tampering',
                    'parameter': param,
                    'severity': 'Medium',
                    'description': f'Parameter {param} is vulnerable to tampering'
                })

        # اختبار تغيير معلمات POST
        post_params = extract_post_parameters(url)
        for param in post_params:
            if is_parameter_tamperable(param):
                vulns.append({
                    'type': 'Parameter Tampering',
                    'parameter': param,
                    'severity': 'Medium',
                    'description': f'Parameter {param} is vulnerable to tampering'
                })

        return vulns

    except Exception as e:
        logger.error(f"Error testing parameter tampering: {str(e)}")
        return []

def test_price_manipulation(url: str) -> List[Dict[str, Any]]:
    """اختبار التلاعب بالسعر"""
    vulns = []
    try:
        # البحث عن حقول السعر
        price_fields = find_price_fields(url)

        for field in price_fields:
            # اختبار تغيير السعر
            if can_manipulate_price(field):
                vulns.append({
                    'type': 'Price Manipulation',
                    'field': field,
                    'severity': 'High',
                    'description': f'Price field {field} is vulnerable to manipulation'
                })

        return vulns

    except Exception as e:
        logger.error(f"Error testing price manipulation: {str(e)}")
        return []

def test_privilege_escalation(url: str) -> List[Dict[str, Any]]:
    """اختبار التصعيد غير المصرح به للصلاحيات"""
    vulns = []
    try:
        # اختبار التصعيد الأفقي
        horizontal_escalation = test_horizontal_escalation(url)
        if horizontal_escalation:
            vulns.append({
                'type': 'Horizontal Privilege Escalation',
                'severity': 'High',
                'description': 'Horizontal privilege escalation possible'
            })

        # اختبار التصعيد الرأسي
        vertical_escalation = test_vertical_escalation(url)
        if vertical_escalation:
            vulns.append({
                'type': 'Vertical Privilege Escalation',
                'severity': 'Critical',
                'description': 'Vertical privilege escalation possible'
            })

        return vulns

    except Exception as e:
        logger.error(f"Error testing privilege escalation: {str(e)}")
        return []

def test_state_manipulation(url: str) -> List[Dict[str, Any]]:
    """اختبار التلاعب بالحالة"""
    vulns = []
    try:
        # اختبار تغيير حالة الطلب
        order_state = test_order_state_manipulation(url)
        if order_state:
            vulns.append({
                'type': 'Order State Manipulation',
                'severity': 'High',
                'description': 'Order state can be manipulated'
            })

        # اختبار تغيير حالة المستخدم
        user_state = test_user_state_manipulation(url)
        if user_state:
            vulns.append({
                'type': 'User State Manipulation',
                'severity': 'High',
                'description': 'User state can be manipulated'
            })

        return vulns

    except Exception as e:
        logger.error(f"Error testing state manipulation: {str(e)}")
        return []

def test_error_messages(url: str) -> List[Dict[str, Any]]:
    """اختبار كشف الأخطاء في رسائل الخطأ"""
    vulns = []
    try:
        # اختبار رسائل الخطأ التفصيلية
        detailed_errors = test_detailed_error_messages(url)
        if detailed_errors:
            vulns.append({
                'type': 'Detailed Error Messages',
                'severity': 'Medium',
                'description': 'Detailed error messages exposed'
            })

        # اختبار رسائل الخطأ التي تكشف معلومات حساسة
        sensitive_errors = test_sensitive_error_messages(url)
        if sensitive_errors:
            vulns.append({
                'type': 'Sensitive Error Messages',
                'severity': 'High',
                'description': 'Error messages expose sensitive information'
            })

        return vulns

    except Exception as e:
        logger.error(f"Error testing error messages: {str(e)}")
        return []

def test_sensitive_info_exposure(url: str) -> List[Dict[str, Any]]:
    """اختبار كشف المعلومات الحساسة"""
    vulns = []
    try:
        # اختبار كشف معلومات قاعدة البيانات
        db_info = test_database_info_exposure(url)
        if db_info:
            vulns.append({
                'type': 'Database Information Exposure',
                'severity': 'High',
                'description': 'Database information exposed'
            })

        # اختبار كشف معلومات النظام
        system_info = test_system_info_exposure(url)
        if system_info:
            vulns.append({
                'type': 'System Information Exposure',
                'severity': 'Medium',
                'description': 'System information exposed'
            })

        return vulns

    except Exception as e:
        logger.error(f"Error testing sensitive info exposure: {str(e)}")
        return []

def test_misconfigurations(url: str) -> List[Dict[str, Any]]:
    """اختبار كشف التكوينات الخاطئة"""
    vulns = []
    try:
        # اختبار تكوينات الأمان
        security_configs = test_security_configurations(url)
        if security_configs:
            vulns.append({
                'type': 'Security Misconfiguration',
                'severity': 'Medium',
                'description': 'Security misconfiguration detected'
            })

        # اختبار تكوينات الخادم
        server_configs = test_server_configurations(url)
        if server_configs:
            vulns.append({
                'type': 'Server Misconfiguration',
                'severity': 'Medium',
                'description': 'Server misconfiguration detected'
            })

        return vulns

    except Exception as e:
        logger.error(f"Error testing misconfigurations: {str(e)}")
        return []

def analyze_anomalous_behavior(url: str) -> List[Dict[str, Any]]:
    """تحليل السلوك غير المعتاد"""
    vulns = []
    try:
        # تحليل أنماط الاستجابة غير المعتادة
        response_patterns = analyze_response_patterns(url)
        if response_patterns and response_patterns.get('is_unusual', False):
            # إضافة تفاصيل أكثر إلى الثغرة
            unusual_patterns = response_patterns.get('unusual_patterns', [])
            pattern_count = response_patterns.get('pattern_count', 0)
            status_code = response_patterns.get('status_code', 0)

            vulns.append({
                'type': 'Anomalous Response Pattern',
                'severity': 'Medium',
                'description': f'Anomalous response pattern detected ({pattern_count} patterns)',
                'details': {
                    'unusual_patterns': unusual_patterns,
                    'pattern_count': pattern_count,
                    'status_code': status_code,
                    'url': url
                }
            })

        # تحليل أنماط السلوك غير المعتادة
        behavior_patterns = analyze_behavior_patterns(url)
        if behavior_patterns and behavior_patterns.get('is_unusual', False):
            # إضافة تفاصيل أكثر إلى الثغرة
            patterns = behavior_patterns.get('behavior_patterns', [])
            pattern_count = behavior_patterns.get('pattern_count', 0)

            vulns.append({
                'type': 'Anomalous Behavior Pattern',
                'severity': 'Medium',
                'description': f'Anomalous behavior pattern detected ({pattern_count} patterns)',
                'details': {
                    'behavior_patterns': patterns,
                    'pattern_count': pattern_count,
                    'url': url
                }
            })

        return vulns

    except Exception as e:
        logger.error(f"Error analyzing anomalous behavior: {str(e)}")
        return []

def analyze_unknown_patterns(url: str) -> List[Dict[str, Any]]:
    """تحليل الأنماط غير المعروفة"""
    vulns = []
    try:
        # تحليل أنماط الكود غير المعروفة
        code_patterns = analyze_code_patterns(url)
        if code_patterns and code_patterns.get('is_unknown', False):
            # إضافة تفاصيل أكثر إلى الثغرة
            patterns = code_patterns.get('code_patterns', [])
            pattern_count = code_patterns.get('pattern_count', 0)

            vulns.append({
                'type': 'Unknown Code Pattern',
                'severity': 'Medium',
                'description': f'Unknown code pattern detected ({pattern_count} patterns)',
                'details': {
                    'code_patterns': patterns,
                    'pattern_count': pattern_count,
                    'url': url
                }
            })

        # تحليل أنماط البيانات غير المعروفة
        data_patterns = analyze_data_patterns(url)
        if data_patterns and data_patterns.get('is_unknown', False):
            # إضافة تفاصيل أكثر إلى الثغرة
            patterns = data_patterns.get('data_patterns', [])
            pattern_count = data_patterns.get('pattern_count', 0)

            vulns.append({
                'type': 'Unknown Data Pattern',
                'severity': 'Medium',
                'description': f'Unknown data pattern detected ({pattern_count} patterns)',
                'details': {
                    'data_patterns': patterns,
                    'pattern_count': pattern_count,
                    'url': url
                }
            })

        return vulns

    except Exception as e:
        logger.error(f"Error analyzing unknown patterns: {str(e)}")
        return []

def analyze_potential_vulnerabilities(url: str) -> List[Dict[str, Any]]:
    """تحليل الثغرات المحتملة"""
    vulns = []
    try:
        # تحليل الثغرات المحتملة في الكود
        code_vulns = analyze_code_vulnerabilities(url)
        vulns.extend(code_vulns)

        # تحليل الثغرات المحتملة في البيانات
        data_vulns = analyze_data_vulnerabilities(url)
        vulns.extend(data_vulns)

        return vulns

    except Exception as e:
        logger.error(f"Error analyzing potential vulnerabilities: {str(e)}")
        return []

async def main():
    """الدالة الرئيسية لتشغيل الفحص المتسلسل"""
    try:
        # الحصول على عنوان URL من المستخدم
        url = input("Enter the target URL: ")

        # تشغيل الفحص المتسلسل
        print(Fore.CYAN + "[*] Starting sequential vulnerability scan...")
        vulnerabilities = await sequential_scan(url)

        # عرض ملخص النتائج
        print(Fore.GREEN + "\n[+] Scan completed!")
        print(Fore.YELLOW + "\nVulnerability Summary:")
        for category, vulns in vulnerabilities.items():
            print(f"{category}: {len(vulns)} vulnerabilities found")

        # حفظ التقرير النهائي
        save_to_json(vulnerabilities, "categorized_reports/final_report.json")
        print(Fore.GREEN + "\n[+] Final report saved to categorized_reports/final_report.json")

    except Exception as e:
        logger.error(f"Error in main execution: {str(e)}")
        print(Fore.RED + f"[-] Error: {str(e)}")

if __name__ == "__main__":
    try:
        # Initialize environment
        init_environment()

        # Start interactive interface
        asyncio.run(enhanced_interact())

    except KeyboardInterrupt:
        print("\nProgram terminated by user.")
    except Exception as e:
        logging.error(f"Error in main: {str(e)}")
    finally:
        asyncio.run(cleanup_resources())

class FileManager:
    """مدير الملفات والمجلدات"""
    def __init__(self):
        self.base_dirs = {
            'reports': 'categorized_reports',
            'screenshots': 'screenshots',
            'logs': 'logs'
        }
        self.report_files = {
            'model_based': 'model_based_report.json',
            'business_logic': 'business_logic_report.json',
            'human_errors': 'human_errors_report.json',
            'registration_panels': 'registration_panels_report.json',
            'zero_day': 'zero_day_report.json',
            'final': 'final_report.json'
        }
        self.screenshot_patterns = {
            'model_based': 'model_based_*.png',
            'business_logic': 'business_logic_*.png',
            'human_errors': 'human_errors_*.png',
            'registration': 'registration_*.png',
            'zero_day': 'zero_day_*.png'
        }
        self._init_directories()
        self._init_files()

    def _init_directories(self):
        """إنشاء جميع المجلدات المطلوبة"""
        try:
            # إنشاء المجلدات الأساسية
            for dir_name, dir_path in self.base_dirs.items():
                os.makedirs(dir_path, exist_ok=True)
                logger.info(f"Created directory: {dir_path}")

            # إنشاء مجلدات فرعية للتقارير
            report_subdirs = ['detailed', 'summary', 'technical']
            for subdir in report_subdirs:
                subdir_path = os.path.join(self.base_dirs['reports'], subdir)
                os.makedirs(subdir_path, exist_ok=True)
                logger.info(f"Created subdirectory: {subdir_path}")

            # إنشاء مجلدات فرعية للصور
            screenshot_subdirs = ['vulnerabilities', 'pages', 'elements']
            for subdir in screenshot_subdirs:
                subdir_path = os.path.join(self.base_dirs['screenshots'], subdir)
                os.makedirs(subdir_path, exist_ok=True)
                logger.info(f"Created subdirectory: {subdir_path}")

        except Exception as e:
            logger.error(f"Error creating directories: {str(e)}")
            raise

    def _init_files(self):
        """إنشاء جميع الملفات المطلوبة"""
        try:
            # إنشاء ملفات التقارير
            for report_type, filename in self.report_files.items():
                file_path = os.path.join(self.base_dirs['reports'], filename)
                if not os.path.exists(file_path):
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump([], f, ensure_ascii=False, indent=4)
                    logger.info(f"Created report file: {file_path}")

            # إنشاء ملفات السجلات
            log_files = ['scan.log', 'errors.log', 'debug.log']
            for log_file in log_files:
                file_path = os.path.join(self.base_dirs['logs'], log_file)
                if not os.path.exists(file_path):
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(f"Log file created at {datetime.now().isoformat()}\n")
                    logger.info(f"Created log file: {file_path}")

            # إنشاء ملفات التكوين
            config_files = ['scan_config.json', 'vulnerability_patterns.json']
            for config_file in config_files:
                file_path = os.path.join(self.base_dirs['reports'], config_file)
                if not os.path.exists(file_path):
                    with open(file_path, 'w', encoding='utf-8') as f:
                        json.dump({}, f, ensure_ascii=False, indent=4)
                    logger.info(f"Created config file: {file_path}")

        except Exception as e:
            logger.error(f"Error creating files: {str(e)}")
            raise

    def get_report_path(self, report_type: str) -> str:
        """الحصول على مسار ملف التقرير"""
        if report_type not in self.report_files:
            raise ValueError(f"Invalid report type: {report_type}")
        return os.path.join(self.base_dirs['reports'], self.report_files[report_type])

    def get_screenshot_path(self, category: str, filename: str) -> str:
        """الحصول على مسار حفظ لقطة الشاشة"""
        if category not in self.screenshot_patterns:
            raise ValueError(f"Invalid screenshot category: {category}")
        return os.path.join(self.base_dirs['screenshots'], category, filename)

    def save_report(self, report_type: str, data: List[Dict[str, Any]]) -> None:
        """حفظ التقرير في الملف المناسب"""
        try:
            file_path = self.get_report_path(report_type)
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=4)
            logger.info(f"Saved report to {file_path}")
        except Exception as e:
            logger.error(f"Error saving report: {str(e)}")
            raise

    def save_screenshot(self, category: str, filename: str, image_data: bytes) -> None:
        """حفظ لقطة الشاشة"""
        try:
            file_path = self.get_screenshot_path(category, filename)
            with open(file_path, 'wb') as f:
                f.write(image_data)
            logger.info(f"Saved screenshot to {file_path}")
        except Exception as e:
            logger.error(f"Error saving screenshot: {str(e)}")
            raise

    def get_log_path(self, log_type: str = 'scan') -> str:
        """الحصول على مسار ملف السجل"""
        return os.path.join(self.base_dirs['logs'], f"{log_type}.log")

    def log_error(self, error_message: str) -> None:
        """تسجيل خطأ في ملف السجلات"""
        try:
            log_path = self.get_log_path('errors')
            with open(log_path, 'a', encoding='utf-8') as f:
                f.write(f"[{datetime.now().isoformat()}] ERROR: {error_message}\n")
        except Exception as e:
            logger.error(f"Error logging error: {str(e)}")

    def log_debug(self, debug_message: str) -> None:
        """تسجيل رسالة تصحيح في ملف السجلات"""
        try:
            log_path = self.get_log_path('debug')
            with open(log_path, 'a', encoding='utf-8') as f:
                f.write(f"[{datetime.now().isoformat()}] DEBUG: {debug_message}\n")
        except Exception as e:
            logger.error(f"Error logging debug message: {str(e)}")