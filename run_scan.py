#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import asyncio
import importlib.util
from colorama import Fore, init

# Inicializar colorama
init()

# Importar el módulo 66.py
try:
    spec = importlib.util.spec_from_file_location("module_66", "66.py")
    module_66 = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(module_66)
    print(Fore.GREEN + "[+] Módulo importado correctamente")
except Exception as e:
    print(Fore.RED + f"[-] Error importando el módulo: {str(e)}")
    sys.exit(1)

# Crear directorios necesarios
os.makedirs("reports", exist_ok=True)
os.makedirs(os.path.join("reports", "evidence"), exist_ok=True)
os.makedirs(os.path.join("reports", "traditional"), exist_ok=True)

# URL de ejemplo para escanear
TARGET_URL = "https://example.com"
MAX_LINKS = 2

# Función principal asíncrona
async def main():
    # Ejecutar el escaneo tradicional
    print(Fore.CYAN + f"[*] Iniciando escaneo tradicional en {TARGET_URL}")
    try:
        # Usar await para esperar a que la función asíncrona termine
        vulnerabilities = await module_66.run_traditional_scan(TARGET_URL, max_links=MAX_LINKS)
        print(Fore.GREEN + f"[+] Escaneo completado. Se encontraron {len(vulnerabilities)} vulnerabilidades")
    except Exception as e:
        print(Fore.RED + f"[-] Error ejecutando el escaneo: {str(e)}")

# Ejecutar la función principal asíncrona
if __name__ == "__main__":
    asyncio.run(main())
